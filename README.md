# NSE 服务管理系统

## 项目简介
基于Spring Boot 3.x的企业级管理系统，提供完整的用户权限管理、组织架构管理、系统配置等功能。

### 核心功能
- 🔐 **用户管理**: 用户注册、登录、权限控制
- 👥 **角色管理**: 角色定义、权限分配
- 🏢 **组织管理**: 多级组织架构、部门管理
- 🌍 **区域管理**: 地理区域层级管理
- 📚 **字典管理**: 系统字典配置、数据标准化
- ⚙️ **系统配置**: 灵活的系统参数配置

### 系统架构
- 基础模块 (nse-base): 通用组件和依赖管理
- 云服务模块 (nse-cloud): 云原生服务支持 
- 管理模块 (nse-mgr): 核心业务管理功能

## 技术栈
- **核心框架**: Spring Boot 3.4.2
- **Java版本**: JDK 21
- **数据库**: PostgreSQL 12+
- **ORM框架**: MyBatis-Plus 3.5.12
- **连接池**: Druid 1.2.23
- **工具库**: Hutool 6.0.0
- **构建工具**: Maven 3.6+

## 模块说明
nse-base/ # 基础模块 
    ├── nse-common/ # 通用工具类 
    └── nse-dependency # 依赖管理

nse-cloud/ # 云服务模块 
    ├── nse-cloud-common/ 
    ├── nse-cloud-launcher/ 
    ├── nse-cloud-repository/ 
    └── nse-cloud-sys/

nse-mgr/ # 管理系统模块 
    ├── nse-mgr-common/ 
    ├── nse-mgr-launcher/ 
    ├── nse-mgr-repository/
    └── nse-mgr-sys/
    
## 环境要求

### 开发环境
- **JDK**: 21 或更高版本
- **Maven**: 3.6+ 
- **PostgreSQL**: 12.0+
- **IDE**: IntelliJ IDEA 2023+ (推荐)

### 运行环境
- **内存**: 最低 2GB，推荐 4GB+
- **磁盘**: 最低 1GB 可用空间
- **网络**: 需要访问Maven中央仓库

## 数据库设计

数据库初始化脚本位于 `db/V1.0.0_init_db.sql`，包含以下核心表：

### 用户权限模块
- `sys_user` - 用户基础信息表
- `sys_role` - 角色定义表
- `sys_user_role` - 用户角色关联表
- `sys_role_permission` - 角色权限关联表

### 组织架构模块
- `sys_organization` - 组织机构表（树形结构）
- `sys_office` - 办公机构表
- `sys_area` - 区域管理表（树形结构）

### 系统配置模块
- `sys_dict_type` - 字典类型表
- `sys_dict_data` - 字典数据表（支持扩展字段）

## 快速开始

### 1. 环境准备
```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version

# 启动PostgreSQL服务
# Windows: net start postgresql
# Linux: sudo systemctl start postgresql
```

### 2. 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE nse_db;

-- 执行初始化脚本
\i db/V1.0.0_init_db.sql
```

### 3. 配置文件
在各启动模块中创建 `application.yml` 配置文件：

```yaml
spring:
  datasource:
    url: ***************************************
    username: your_username
    password: your_password
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: true

server:
  port: 8080

logging:
  level:
    com.ruijie.nse: DEBUG
```

### 4. 启动应用
```bash
# 编译项目
mvn clean compile

# 云服务启动
cd nse-cloud/nse-cloud-launcher
mvn spring-boot:run

# 管理系统启动  
cd nse-mgr/nse-mgr-launcher
mvn spring-boot:run
```

### 5. 验证安装
访问以下地址验证系统是否正常启动：
- 管理系统: http://localhost:8080
- 云服务: http://localhost:8081 (如果配置了不同端口)

默认管理员账号：
- 用户名: `superAdmin`
- 密码: `111`

## 开发指南

### 项目结构说明
```
nse-service/
├── nse-base/                    # 基础模块
│   ├── nse-common/              # 通用工具类、常量定义
│   └── nse-dependency/          # 统一依赖管理(BOM)
├── nse-cloud/                   # 云服务模块
│   ├── nse-cloud-common/        # 云服务通用组件
│   ├── nse-cloud-launcher/      # 云服务启动器
│   ├── nse-cloud-repository/    # 云服务数据访问层
│   └── nse-cloud-sys/           # 云服务系统管理
├── nse-mgr/                     # 管理系统模块
│   ├── nse-mgr-common/          # 管理系统通用组件
│   ├── nse-mgr-launcher/        # 管理系统启动器
│   ├── nse-mgr-repository/      # 管理系统数据访问层
│   └── nse-mgr-sys/             # 管理系统核心业务
└── db/                          # 数据库脚本
    └── V1.0.0_init_db.sql       # 初始化脚本
```

### 编码规范
- **包命名**: 统一使用 `com.ruijie.nse.{module}` 格式
- **类命名**: 使用驼峰命名法，类名首字母大写
- **方法命名**: 使用驼峰命名法，方法名首字母小写
- **常量命名**: 全大写，单词间用下划线分隔
- **注释**: 重要方法和类必须添加JavaDoc注释

### 数据库规范
- **表命名**: 使用下划线分隔，如 `sys_user`
- **字段命名**: 使用下划线分隔，如 `created_date`
- **主键**: 统一使用 `id` 作为主键字段名
- **审计字段**: 统一包含 `created_date`, `created_by`, `modified_date`, `modified_by`
- **软删除**: 使用 `is_deleted` 字段标识

## API 文档

### 用户管理 API

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "username": "superAdmin",
  "password": "111"
}
```

#### 获取用户信息
```http
GET /api/user/profile
Authorization: Bearer {token}
```

#### 用户列表查询
```http
GET /api/user/list?page=1&size=10&name=admin
Authorization: Bearer {token}
```

### 角色管理 API

#### 角色列表
```http
GET /api/role/list
Authorization: Bearer {token}
```

#### 创建角色
```http
POST /api/role/create
Content-Type: application/json
Authorization: Bearer {token}

{
  "name": "测试角色",
  "remark": "角色描述"
}
```

## 故障排除

### 常见问题

#### 1. 启动失败 - 端口被占用
```bash
# Windows查看端口占用
netstat -ano | findstr :8080

# Linux查看端口占用  
lsof -i :8080

# 修改配置文件中的端口号
server.port=8081
```

#### 2. 数据库连接失败
- 检查PostgreSQL服务是否启动
- 验证数据库连接参数是否正确
- 确认数据库用户权限

#### 3. Maven依赖下载失败
```bash
# 清理本地仓库
mvn clean

# 强制更新依赖
mvn clean install -U

# 使用阿里云镜像(在settings.xml中配置)
```

#### 4. 内存不足
```bash
# 增加JVM内存参数
export MAVEN_OPTS="-Xmx2g -Xms1g"

# 或在启动时指定
java -Xmx2g -Xms1g -jar app.jar
```

### 日志查看
```bash
# 查看应用日志
tail -f logs/nse-service.log

# 查看错误日志
grep ERROR logs/nse-service.log

# 实时监控日志
tail -f logs/nse-service.log | grep -i error
```

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 提交规范
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目维护者: Ruijie NSE Team
- 邮箱: <EMAIL>
- 问题反馈: [GitHub Issues](https://github.com/ruijie/nse-service/issues)

---

**注意**: 本项目仅供学习和开发使用，生产环境部署前请进行充分的安全评估和性能测试。
