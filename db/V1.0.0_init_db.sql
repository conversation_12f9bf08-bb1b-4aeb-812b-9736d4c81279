-- sys_area definition

-- Drop table

-- DROP TABLE sys_area;

CREATE TABLE sys_area (
	id varchar(50) NOT NULL, -- 主键
	remark varchar(300) NULL, -- 描述
	created_date timestamp(6) NULL, -- 创建时间
	created_by varchar(50) NULL,
	modified_date timestamp(6) NULL,
	modified_by varchar(50) NULL, -- 更新者
	is_deleted int2 NULL, -- 是否删除
	parent_code varchar(64) NULL, -- 父级编号
	parent_codes varchar(1000) NULL, -- 所有父级编号
	tree_sort numeric(10) NULL, -- 本级排序号（升序）
	tree_sorts varchar(1000) NULL, -- 所有级别排序号
	tree_level numeric(4) NULL, -- 层次级别
	tree_names varchar(1000) NULL, -- 全节点名
	"name" varchar(100) NULL, -- 区域名称
	"type" bpchar(1) NULL, -- 区域类型
	status bpchar(1) NULL, -- 状态（0正常 1删除 2停用）
	tree_leaf bpchar(1) NULL, -- 是否最末级
	CONSTRAINT sys_area_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sys_area IS '区域表';

-- Column comments

COMMENT ON COLUMN sys_area.id IS '主键';
COMMENT ON COLUMN sys_area.remark IS '描述';
COMMENT ON COLUMN sys_area.created_date IS '创建时间';
COMMENT ON COLUMN sys_area.modified_by IS '更新者';
COMMENT ON COLUMN sys_area.is_deleted IS '是否删除';
COMMENT ON COLUMN sys_area.parent_code IS '父级编号';
COMMENT ON COLUMN sys_area.parent_codes IS '所有父级编号';
COMMENT ON COLUMN sys_area.tree_sort IS '本级排序号（升序）';
COMMENT ON COLUMN sys_area.tree_sorts IS '所有级别排序号';
COMMENT ON COLUMN sys_area.tree_level IS '层次级别';
COMMENT ON COLUMN sys_area.tree_names IS '全节点名';
COMMENT ON COLUMN sys_area."name" IS '区域名称';
COMMENT ON COLUMN sys_area."type" IS '区域类型';
COMMENT ON COLUMN sys_area.status IS '状态（0正常 1删除 2停用）';
COMMENT ON COLUMN sys_area.tree_leaf IS '是否最末级';







-- Drop table

-- DROP TABLE sys_dict_data;

CREATE TABLE sys_dict_data (
	id varchar(64) NOT NULL, -- 字典编码
	dict_key varchar(100) NOT NULL, -- 字典标签
	dict_value varchar(100) NOT NULL, -- 字典键值(避免关键字)
	"type" varchar(100) NOT NULL, -- 字典类型
	sort numeric(10) NULL, -- 本级排序号（升序）
	description varchar(500) NULL, -- 字典描述
	css_style varchar(500) NULL, -- css样式（如：color:red)
	css_class varchar(500) NULL, -- css类名（如：red）
	status bpchar(1) NOT NULL, -- 状态（0正常 1删除 2停用）
	created_by varchar(64) NOT NULL, -- 创建者
	created_date timestamp(6) NOT NULL, -- 创建时间
	modified_by varchar(64) NOT NULL, -- 更新者
	modified_date timestamp(6) NOT NULL, -- 更新时间
	remark varchar(500) NULL, -- 备注信息
	is_deleted int2 NULL,
	extend_s1 varchar(500) NULL, -- 扩展 String 1
	extend_s2 varchar(500) NULL, -- 扩展 String 2
	extend_s3 varchar(500) NULL, -- 扩展 String 3
	extend_s4 varchar(500) NULL, -- 扩展 String 4
	extend_s5 varchar(500) NULL, -- 扩展 String 5
	extend_s6 varchar(500) NULL, -- 扩展 String 6
	extend_s7 varchar(500) NULL, -- 扩展 String 7
	extend_s8 varchar(500) NULL, -- 扩展 String 8
	extend_i1 numeric(19) NULL, -- 扩展 Integer 1
	extend_i2 numeric(19) NULL, -- 扩展 Integer 2
	extend_i3 numeric(19) NULL, -- 扩展 Integer 3
	extend_i4 numeric(19) NULL, -- 扩展 Integer 4
	extend_f1 numeric(19, 4) NULL, -- 扩展 Float 1
	extend_f2 numeric(19, 4) NULL, -- 扩展 Float 2
	extend_f3 numeric(19, 4) NULL, -- 扩展 Float 3
	extend_f4 numeric(19, 4) NULL, -- 扩展 Float 4
	extend_d1 timestamp(6) NULL, -- 扩展 Date 1
	extend_d2 timestamp(6) NULL, -- 扩展 Date 2
	extend_d3 timestamp(6) NULL, -- 扩展 Date 3
	extend_d4 timestamp(6) NULL, -- 扩展 Date 4
	CONSTRAINT sys_dict_data_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sys_dict_data IS '字典数据表';

-- Column comments

COMMENT ON COLUMN sys_dict_data.id IS '字典编码';
COMMENT ON COLUMN sys_dict_data.dict_key IS '字典标签';
COMMENT ON COLUMN sys_dict_data.dict_value IS '字典键值(避免关键字)';
COMMENT ON COLUMN sys_dict_data."type" IS '字典类型';
COMMENT ON COLUMN sys_dict_data.sort IS '本级排序号（升序）';
COMMENT ON COLUMN sys_dict_data.description IS '字典描述';
COMMENT ON COLUMN sys_dict_data.css_style IS 'css样式（如：color:red)';
COMMENT ON COLUMN sys_dict_data.css_class IS 'css类名（如：red）';
COMMENT ON COLUMN sys_dict_data.status IS '状态（0正常 1删除 2停用）';
COMMENT ON COLUMN sys_dict_data.created_by IS '创建者';
COMMENT ON COLUMN sys_dict_data.created_date IS '创建时间';
COMMENT ON COLUMN sys_dict_data.modified_by IS '更新者';
COMMENT ON COLUMN sys_dict_data.modified_date IS '更新时间';
COMMENT ON COLUMN sys_dict_data.remark IS '备注信息';
COMMENT ON COLUMN sys_dict_data.extend_s1 IS '扩展 String 1';
COMMENT ON COLUMN sys_dict_data.extend_s2 IS '扩展 String 2';
COMMENT ON COLUMN sys_dict_data.extend_s3 IS '扩展 String 3';
COMMENT ON COLUMN sys_dict_data.extend_s4 IS '扩展 String 4';
COMMENT ON COLUMN sys_dict_data.extend_s5 IS '扩展 String 5';
COMMENT ON COLUMN sys_dict_data.extend_s6 IS '扩展 String 6';
COMMENT ON COLUMN sys_dict_data.extend_s7 IS '扩展 String 7';
COMMENT ON COLUMN sys_dict_data.extend_s8 IS '扩展 String 8';
COMMENT ON COLUMN sys_dict_data.extend_i1 IS '扩展 Integer 1';
COMMENT ON COLUMN sys_dict_data.extend_i2 IS '扩展 Integer 2';
COMMENT ON COLUMN sys_dict_data.extend_i3 IS '扩展 Integer 3';
COMMENT ON COLUMN sys_dict_data.extend_i4 IS '扩展 Integer 4';
COMMENT ON COLUMN sys_dict_data.extend_f1 IS '扩展 Float 1';
COMMENT ON COLUMN sys_dict_data.extend_f2 IS '扩展 Float 2';
COMMENT ON COLUMN sys_dict_data.extend_f3 IS '扩展 Float 3';
COMMENT ON COLUMN sys_dict_data.extend_f4 IS '扩展 Float 4';
COMMENT ON COLUMN sys_dict_data.extend_d1 IS '扩展 Date 1';
COMMENT ON COLUMN sys_dict_data.extend_d2 IS '扩展 Date 2';
COMMENT ON COLUMN sys_dict_data.extend_d3 IS '扩展 Date 3';
COMMENT ON COLUMN sys_dict_data.extend_d4 IS '扩展 Date 4';












-- sys_dict_type definition

-- Drop table

-- DROP TABLE sys_dict_type;

CREATE TABLE sys_dict_type (
	id varchar(64) NOT NULL, -- 编号
	"name" varchar(100) NOT NULL, -- 字典名称
	"type" varchar(100) NOT NULL, -- 字典类型
	status bpchar(1) NOT NULL, -- 状态（0正常 1删除 2停用）
	created_by varchar(64) NOT NULL, -- 创建者
	created_date timestamp(6) NOT NULL, -- 创建时间
	modified_by varchar(64) NOT NULL, -- 更新者
	modified_date timestamp(6) NOT NULL, -- 更新时间
	remark varchar(500) NULL, -- 备注信息
	is_deleted int2 NULL,
	CONSTRAINT sys_dict_type_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sys_dict_type IS '字典类型表';

-- Column comments

COMMENT ON COLUMN sys_dict_type.id IS '编号';
COMMENT ON COLUMN sys_dict_type."name" IS '字典名称';
COMMENT ON COLUMN sys_dict_type."type" IS '字典类型';
COMMENT ON COLUMN sys_dict_type.status IS '状态（0正常 1删除 2停用）';
COMMENT ON COLUMN sys_dict_type.created_by IS '创建者';
COMMENT ON COLUMN sys_dict_type.created_date IS '创建时间';
COMMENT ON COLUMN sys_dict_type.modified_by IS '更新者';
COMMENT ON COLUMN sys_dict_type.modified_date IS '更新时间';
COMMENT ON COLUMN sys_dict_type.remark IS '备注信息';












-- sys_office definition

-- Drop table

-- DROP TABLE sys_office;

CREATE TABLE sys_office (
	id varchar(50) NOT NULL, -- 机构编码
	remark varchar(300) NULL, -- 描述
	created_date timestamp(6) NULL, -- 创建时间
	created_by varchar(50) NULL,
	modified_date timestamp(6) NULL,
	modified_by varchar(50) NULL, -- 更新者
	is_deleted int2 NULL, -- 是否删除
	parent_code varchar(64) NULL, -- 父级编号
	parent_codes varchar(1000) NULL,
	tree_sort numeric(10) NULL, -- 本级排序号（升序）
	tree_sorts varchar(1000) NULL, -- 所有级别排序号
	tree_leaf bpchar(1) NULL, -- 是否最末级
	tree_level numeric(4) NULL, -- 层次级别
	tree_names varchar(1000) NULL, -- 全节点名
	view_code varchar(100) NULL, -- 机构代码
	"name" varchar(100) NULL, -- 名称
	full_name varchar(255) NULL, -- 机构全称
	"type" bpchar(1) NULL, -- 机构类型
	leader varchar(100) NULL, -- 负责人
	phone varchar(100) NULL, -- 办公电话
	address varchar(255) NULL, -- 联系地址
	zip_code varchar(100) NULL, -- 邮政编码
	email varchar(255) NULL, -- 邮箱地址
	status numeric(4) NULL, -- 状态（0正常 1删除 2停用）
	CONSTRAINT sys_office_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sys_office IS '机构表';

-- Column comments

COMMENT ON COLUMN sys_office.id IS '机构编码';
COMMENT ON COLUMN sys_office.remark IS '描述';
COMMENT ON COLUMN sys_office.created_date IS '创建时间';
COMMENT ON COLUMN sys_office.modified_by IS '更新者';
COMMENT ON COLUMN sys_office.is_deleted IS '是否删除';
COMMENT ON COLUMN sys_office.parent_code IS '父级编号';
COMMENT ON COLUMN sys_office.tree_sort IS '本级排序号（升序）';
COMMENT ON COLUMN sys_office.tree_sorts IS '所有级别排序号';
COMMENT ON COLUMN sys_office.tree_leaf IS '是否最末级';
COMMENT ON COLUMN sys_office.tree_level IS '层次级别';
COMMENT ON COLUMN sys_office.tree_names IS '全节点名';
COMMENT ON COLUMN sys_office.view_code IS '机构代码';
COMMENT ON COLUMN sys_office."name" IS '名称';
COMMENT ON COLUMN sys_office.full_name IS '机构全称';
COMMENT ON COLUMN sys_office."type" IS '机构类型';
COMMENT ON COLUMN sys_office.leader IS '负责人';
COMMENT ON COLUMN sys_office.phone IS '办公电话';
COMMENT ON COLUMN sys_office.address IS '联系地址';
COMMENT ON COLUMN sys_office.zip_code IS '邮政编码';
COMMENT ON COLUMN sys_office.email IS '邮箱地址';
COMMENT ON COLUMN sys_office.status IS '状态（0正常 1删除 2停用）';













-- sys_organization definition

-- Drop table

-- DROP TABLE sys_organization;

CREATE TABLE sys_organization (
	id varchar(50) NOT NULL, -- 主键
	remark varchar(300) NULL, -- 描述
	created_date timestamp(6) NULL, -- 创建时间
	created_by varchar(50) NULL,
	modified_date timestamp(6) NULL,
	modified_by varchar(50) NULL, -- 更新者
	is_deleted int2 NULL, -- 是否删除
	parent_code varchar(100) NOT NULL, -- 父级编号
	parent_codes varchar(200) NOT NULL, -- 所有父级编号
	tree_sort numeric(10) NULL, -- 本级排序号（升序）
	tree_sorts varchar(1000) NULL, -- 所有级别排序号
	tree_level int2 NOT NULL, -- 层次级别
	tree_names varchar(1000) NOT NULL, -- 全节点名
	"name" varchar(100) NOT NULL, -- 区域名称
	"type" int2 NULL, -- 区域类型
	status int2 NULL, -- 状态（0正常 1删除 2停用）
	tree_leaf int2 NOT NULL, -- 是否最末级
	CONSTRAINT sys_organization_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sys_organization IS '组织表';


-- Column comments

COMMENT ON COLUMN sys_organization.id IS '主键';
COMMENT ON COLUMN sys_organization.remark IS '描述';
COMMENT ON COLUMN sys_organization.created_date IS '创建时间';
COMMENT ON COLUMN sys_organization.modified_by IS '更新者';
COMMENT ON COLUMN sys_organization.is_deleted IS '是否删除';
COMMENT ON COLUMN sys_organization.parent_code IS '父级编号';
COMMENT ON COLUMN sys_organization.parent_codes IS '所有父级编号';
COMMENT ON COLUMN sys_organization.tree_sort IS '本级排序号（升序）';
COMMENT ON COLUMN sys_organization.tree_sorts IS '所有级别排序号';
COMMENT ON COLUMN sys_organization.tree_level IS '层次级别';
COMMENT ON COLUMN sys_organization.tree_names IS '全节点名';
COMMENT ON COLUMN sys_organization."name" IS '区域名称';
COMMENT ON COLUMN sys_organization."type" IS '区域类型';
COMMENT ON COLUMN sys_organization.status IS '状态（0正常 1删除 2停用）';
COMMENT ON COLUMN sys_organization.tree_leaf IS '是否最末级';
















-- sys_role definition

-- Drop table

-- DROP TABLE sys_role;

CREATE TABLE sys_role (
	id varchar(50) NOT NULL, -- 主键
	"name" varchar(50) NOT NULL, -- 角色名称
	remark varchar(300) NULL, -- 角色描述
	created_date timestamp(6) NULL, -- 创建时间
	created_by varchar(50) NULL,
	modified_date timestamp(6) NULL,
	modified_by varchar(50) NULL, -- 更新者
	is_deleted int2 NULL, -- 是否删除
	CONSTRAINT sys_role_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sys_role IS '系统角色表';

-- Column comments

COMMENT ON COLUMN sys_role.id IS '主键';
COMMENT ON COLUMN sys_role."name" IS '角色名称';
COMMENT ON COLUMN sys_role.remark IS '角色描述';
COMMENT ON COLUMN sys_role.created_date IS '创建时间';
COMMENT ON COLUMN sys_role.modified_by IS '更新者';
COMMENT ON COLUMN sys_role.is_deleted IS '是否删除';








-- sys_role_permission definition

-- Drop table

-- DROP TABLE sys_role_permission;

CREATE TABLE sys_role_permission (
	role_id varchar(255) NOT NULL,
	permission_id varchar(80) NOT NULL,
	CONSTRAINT sys_role_permission_pkey PRIMARY KEY (role_id, permission_id)
);
COMMENT ON TABLE sys_role_permission IS '角色权限表';









-- sys_user definition

-- Drop table

-- DROP TABLE sys_user;

CREATE TABLE sys_user (
	id varchar(32) NOT NULL, -- id
	"name" varchar(50) NULL, -- 姓名
	"password" bpchar(32) NULL, -- 密码
	salt bpchar(8) NULL, -- 盐
	email varchar(50) NULL,
	mobile_phone varchar(20) NULL,
	employee_id varchar(32) NULL,
	status int2 NULL, -- 用户状态
	avatar varchar(200) NULL, -- 头像
	remark varchar(300) NULL, -- 备注
	created_date timestamp(6) NULL, -- 创建时间
	created_by varchar(50) NULL, -- 创建人
	modified_date timestamp(6) NULL, -- 修改时间
	modified_by varchar(50) NULL, -- 修改人
	login_ip varchar(15) NULL, -- 登录IP
	login_date timestamp(6) NULL, -- 登录时间
	is_deleted int2 NULL, -- 是否删除
	CONSTRAINT sys_user_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sys_user IS '用户表';

-- Column comments

COMMENT ON COLUMN sys_user.id IS 'id';
COMMENT ON COLUMN sys_user."name" IS '姓名';
COMMENT ON COLUMN sys_user."password" IS '密码';
COMMENT ON COLUMN sys_user.salt IS '盐';
COMMENT ON COLUMN sys_user.status IS '用户状态';
COMMENT ON COLUMN sys_user.avatar IS '头像';
COMMENT ON COLUMN sys_user.remark IS '备注';
COMMENT ON COLUMN sys_user.created_date IS '创建时间';
COMMENT ON COLUMN sys_user.created_by IS '创建人';
COMMENT ON COLUMN sys_user.modified_date IS '修改时间';
COMMENT ON COLUMN sys_user.modified_by IS '修改人';
COMMENT ON COLUMN sys_user.login_ip IS '登录IP';
COMMENT ON COLUMN sys_user.login_date IS '登录时间';
COMMENT ON COLUMN sys_user.is_deleted IS '是否删除';


-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO sys_user VALUES ('superAdmin', '111', 'd92c76c3ba91af3da8da861d06361bc8', '583d6eea', '<EMAIL>', '15019460591', 'sss', '1', '/sys/me/avatar/upload/65307c9d69e9a7da510bc8a23e1f5473.png', 'sss', '2018-02-04 14:28:58', null, '2018-09-18 11:26:21', 'superAdmin', null, null, '1');








-- sys_user_role definition

-- Drop table

-- DROP TABLE sys_user_role;

CREATE TABLE sys_user_role (
	user_id varchar(50) NOT NULL, -- 用户主键
	role_id varchar(50) NOT NULL, -- 角色主键
	CONSTRAINT sys_user_role_unique UNIQUE (user_id, role_id)
);
COMMENT ON TABLE sys_user_role IS '用户角色关联表';

-- Column comments

COMMENT ON COLUMN sys_user_role.user_id IS '用户主键';
COMMENT ON COLUMN sys_user_role.role_id IS '角色主键';









CREATE TABLE sys_user (
	id varchar(32) NOT NULL, -- id
	account varchar(64) NULL, -- 账号
	"name" varchar(50) NULL, -- 姓名
	"password" bpchar(32) NULL, -- 密码
	salt bpchar(8) NULL, -- 盐
	email varchar(50) NULL,
	mobile_phone varchar(20) NULL,
	status int2 NULL, -- 用户状态
	avatar varchar(200) NULL, -- 头像
	user_type varchar(32) NULL, -- 用户类型
	classes varchar(64) NULL, -- 班级
	valid_date timestamp(6) NULL, -- 有效期
	remark varchar(300) NULL, -- 备注
	created_date timestamp(6) NULL, -- 创建时间
	created_by varchar(50) NULL, -- 创建人
	modified_date timestamp(6) NULL, -- 修改时间
	modified_by varchar(50) NULL, -- 修改人
	login_ip varchar(15) NULL, -- 登录IP
	login_date timestamp(6) NULL, -- 登录时间
	is_deleted int2 DEFAULT 0, -- 是否删除
	CONSTRAINT sys_user_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sys_user IS '用户表';

COMMENT ON COLUMN sys_user.id IS 'id';
COMMENT ON COLUMN sys_user.account IS '账号';
COMMENT ON COLUMN sys_user."name" IS '姓名';
COMMENT ON COLUMN sys_user."password" IS '密码';
COMMENT ON COLUMN sys_user.salt IS '盐';
COMMENT ON COLUMN sys_user.status IS '用户状态';
COMMENT ON COLUMN sys_user.email IS '用户邮箱';
COMMENT ON COLUMN sys_user.mobile_phone IS '手机号码';
COMMENT ON COLUMN sys_user.avatar IS '头像';
COMMENT ON COLUMN sys_user.classes IS '班级';
COMMENT ON COLUMN sys_user.valid_date IS '有效期';
COMMENT ON COLUMN sys_user.remark IS '备注';
COMMENT ON COLUMN sys_user.user_type IS '用户类型';
COMMENT ON COLUMN sys_user.created_date IS '创建时间';
COMMENT ON COLUMN sys_user.created_by IS '创建人';
COMMENT ON COLUMN sys_user.modified_date IS '修改时间';
COMMENT ON COLUMN sys_user.modified_by IS '修改人';
COMMENT ON COLUMN sys_user.login_ip IS '登录IP';
COMMENT ON COLUMN sys_user.login_date IS '登录时间';
COMMENT ON COLUMN sys_user.is_deleted IS '是否删除';



CREATE TABLE sys_role (
	id varchar(50) NOT NULL, -- 主键
	"name" varchar(50) NOT NULL, -- 角色名称
	level int NULL, -- 角色等级
	remark varchar(300) NULL, -- 角色描述
	created_date timestamp(6) NULL, -- 创建时间
	created_by varchar(50) NULL,
	modified_date timestamp(6) NULL,
	modified_by varchar(50) NULL, -- 更新者
	is_deleted int2 DEFAULT 0, -- 是否删除
	CONSTRAINT sys_role_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE sys_role IS '系统角色表';
COMMENT ON COLUMN sys_role.id IS '主键';
COMMENT ON COLUMN sys_role."name" IS '角色名称';
COMMENT ON COLUMN sys_role.level IS '角色等级';
COMMENT ON COLUMN sys_role.remark IS '角色描述';
COMMENT ON COLUMN sys_role.created_date IS '创建时间';
COMMENT ON COLUMN sys_role.modified_by IS '更新者';
COMMENT ON COLUMN sys_role.is_deleted IS '是否删除';



CREATE TABLE sys_user_role (
	user_id varchar(50) NOT NULL, -- 用户主键
	role_id varchar(50) NOT NULL, -- 角色主键
	CONSTRAINT sys_user_role_unique UNIQUE (user_id, role_id)
);
COMMENT ON TABLE sys_user_role IS '用户角色关联表';

-- Column comments
COMMENT ON COLUMN sys_user_role.user_id IS '用户主键';
COMMENT ON COLUMN sys_user_role.role_id IS '角色主键';



CREATE TABLE sys_menu (
    id varchar(32) NOT NULL, -- ID
    parent_id varchar(32) NOT NULL, -- 父菜单ID
    tree_path varchar(255), -- 父节点ID路径
    "name" varchar(64) NOT NULL, -- 菜单名称
    "type" int2 NOT NULL, -- 菜单类型（1-菜单 2-目录 3-外链 4-按钮）
    route_name varchar(255), -- 路由名称（Vue Router 中用于命名路由）
    route_path varchar(128), -- 路由路径（Vue Router 中定义的 URL 路径）
    component varchar(128), -- 组件路径（组件页面完整路径，相对于 src/views/，缺省后缀 .vue）
    perm varchar(128), -- 【按钮】权限标识
    always_show int2 DEFAULT 0, -- 【目录】只有一个子路由是否始终显示（1-是 0-否）
    keep_alive int2 DEFAULT 0, -- 【菜单】是否开启页面缓存（1-是 0-否）
    visible int2 DEFAULT 1, -- 显示状态（1-显示 0-隐藏）
    sort int4 DEFAULT 0, -- 排序
    icon varchar(64), -- 菜单图标
    redirect varchar(128), -- 跳转路径
    params varchar(255), -- 路由参数
	remark varchar(300) NULL,
	created_date timestamp(6) NULL,
	created_by varchar(50) NULL,
	modified_date timestamp(6) NULL,
	modified_by varchar(50) NULL,
	is_deleted int2 DEFAULT 0,
    CONSTRAINT sys_menu_pkey PRIMARY KEY (id)
);

COMMENT ON TABLE sys_menu IS '菜单管理';
COMMENT ON COLUMN sys_menu.id IS 'ID';
COMMENT ON COLUMN sys_menu.parent_id IS '父菜单ID';
COMMENT ON COLUMN sys_menu.tree_path IS '父节点ID路径';
COMMENT ON COLUMN sys_menu."name" IS '菜单名称';
COMMENT ON COLUMN sys_menu."type" IS '菜单类型（1-菜单 2-目录 3-外链 4-按钮）';
COMMENT ON COLUMN sys_menu.route_name IS '路由名称（Vue Router 中用于命名路由）';
COMMENT ON COLUMN sys_menu.route_path IS '路由路径（Vue Router 中定义的 URL 路径）';
COMMENT ON COLUMN sys_menu.component IS '组件路径（组件页面完整路径，相对于 src/views/，缺省后缀 .vue）';
COMMENT ON COLUMN sys_menu.perm IS '【按钮】权限标识';
COMMENT ON COLUMN sys_menu.always_show IS '【目录】只有一个子路由是否始终显示（1-是 0-否）';
COMMENT ON COLUMN sys_menu.keep_alive IS '【菜单】是否开启页面缓存（1-是 0-否）';
COMMENT ON COLUMN sys_menu.visible IS '显示状态（1-显示 0-隐藏）';
COMMENT ON COLUMN sys_menu.sort IS '排序';
COMMENT ON COLUMN sys_menu.icon IS '菜单图标';
COMMENT ON COLUMN sys_menu.redirect IS '跳转路径';
COMMENT ON COLUMN sys_menu.params IS '路由参数';
COMMENT ON COLUMN sys_menu.remark IS '备注';
COMMENT ON COLUMN sys_menu.created_date IS '创建时间';
COMMENT ON COLUMN sys_menu.created_by IS '创建人';
COMMENT ON COLUMN sys_menu.modified_date IS '修改时间';
COMMENT ON COLUMN sys_menu.modified_by IS '修改人';
COMMENT ON COLUMN sys_menu.is_deleted IS '是否删除';


CREATE TABLE sys_role_menu (
	role_id varchar(50) NOT NULL, -- 角色ID
	menu_id varchar(50) NOT NULL, -- 菜单ID
	CONSTRAINT sys_role_menu_unique UNIQUE (role_id, menu_id)
);
COMMENT ON TABLE sys_role_menu IS '角色和菜单关联表';

-- Column comments
COMMENT ON COLUMN sys_role_menu.role_id IS '角色ID';
COMMENT ON COLUMN sys_role_menu.menu_id IS '菜单ID';