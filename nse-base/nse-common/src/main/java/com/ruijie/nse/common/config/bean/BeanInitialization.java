package com.ruijie.nse.common.config.bean;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;


/**
 * bean初始化处理。
 * 实例化完成了，添加一条日志项，便于调试。
 *
 * <AUTHOR>
 * @date 2018/8/2
 */
@Profile({"dev","test"})
@Configuration
@Slf4j
public class BeanInitialization implements BeanPostProcessor {

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) {
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) {
        log.debug("Bean:{} 实例化完成 \n【{}】", beanName, bean.getClass());
        return bean;

    }
}
