package com.ruijie.nse.common.config.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Component;

/**
 * 缓存配置验证器
 * 用于在应用启动时验证缓存配置是否正确加载
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
@Slf4j
@Component
public class EhcacheConfigValidator implements CommandLineRunner {

    @Autowired(required = false)
    private CacheManager cacheManager;
    
    @Autowired(required = false)
    private EhcacheProperties ehcacheProperties;

    @Override
    public void run(String... args) throws Exception {
        log.info("| ================ 缓存配置验证开始 ================ |");
        
        if (ehcacheProperties != null) {
            log.info("| ✓ EhcacheProperties 已加载");
            log.info("|   - 存储路径: {}", ehcacheProperties.getStoragePath());
        } else {
            log.error("| ✗ EhcacheProperties 未加载");
        }
        
        if (cacheManager != null) {
            log.info("| ✓ CacheManager 已创建");
            log.info("|   - CacheManager 类型: {}", cacheManager.getClass().getName());
            log.info("|   - 可用缓存名称: {}", cacheManager.getCacheNames());
        } else {
            log.error("| ✗ CacheManager 未创建");
        }
        
        log.info("| ================ 缓存配置验证结束 ================ |");
    }
}