package com.ruijie.nse.common.config.cache;


import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * Ehcache 配置属性
 * 
 * <AUTHOR>
 * @date 2025-01-16
 */
@Slf4j
@Data
@ConfigurationProperties(prefix = "nse.ehcache")
public class EhcacheProperties {

    /**
     * 缓存存储路径
     */
    private String storagePath;

    /**
     * 堆内存，默认50M
     */
    private Integer heap = 50;

    /**
     * 堆外内存，默认100M
     */
    private Integer offheap = 100;

    /**
     * 磁盘存储，默认500M
     */
    private Integer disk = 500;

    /**
     * 缓存过期时间，默认30分钟
     */
    private Integer jwtExpire = 1440;
    private Integer serviceExpire = 30;


    @PostConstruct
    public void init() {
        log.info("EhcacheProperties 初始化完成，初始化参数: {}", this);
    }
}
