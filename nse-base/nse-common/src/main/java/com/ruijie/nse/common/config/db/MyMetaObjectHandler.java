package com.ruijie.nse.common.config.db;


import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.ruijie.nse.common.utils.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 自定义填充公共字段
 * 即:没有传的字段自动填充
 *
 * <AUTHOR>
 * @date 2018
 */
@Slf4j
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {

    @Override
    public void insertFill(MetaObject metaObject) {
        this.strictInsertFill(metaObject,"deleted", Boolean.class, false);
        String userId = getCurrentUserId();
        if(CharSequenceUtil.isNotBlank(userId)) {
            this.strictInsertFill(metaObject,"createdBy", String.class, userId);
            this.strictInsertFill(metaObject,"modifiedBy", String.class, userId);
        }
        this.strictInsertFill(metaObject,"createdDate", Date.class, new Date());
        this.strictInsertFill(metaObject,"modifiedDate", Date.class, new Date());
    }


    @Override
    public void updateFill(MetaObject metaObject) {
        this.setFieldValByName("modifiedDate", new Date(), metaObject);
        String userId = getCurrentUserId();
        if(CharSequenceUtil.isNotBlank(userId)) {
            this.setFieldValByName("modifiedBy", getCurrentUserId(), metaObject);
        }
    }


    /**
     * 获取当前用户ID
     *
     * @return
     */
    private String getCurrentUserId() {
        try {
            return SecurityUtils.getUserId();
        } catch (Exception exp) {
            // ignored
        }
        return null;
    }

}
