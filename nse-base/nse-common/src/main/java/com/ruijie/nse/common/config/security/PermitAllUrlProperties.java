package com.ruijie.nse.common.config.security;

import com.ruijie.nse.common.annotation.Anonymous;
import lombok.Getter;
import lombok.Setter;
import org.dromara.hutool.core.regex.ReUtil;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.*;
import java.util.regex.Pattern;


/**
 * 设置Anonymous注解允许匿名访问的url
 * <AUTHOR>
 * @since 2025-07-08
 */
@Configuration
public class PermitAllUrlProperties implements InitializingBean, ApplicationContextAware {

    private static final Pattern PATTERN = Pattern.compile("\\{(.*?)}");

    private ApplicationContext applicationContext;

    @Setter
    @Getter
    private List<String> urls = new ArrayList<>();

    public final static String ASTERISK = "*";

    @Override
    public void afterPropertiesSet() {
        RequestMappingHandlerMapping mapping = applicationContext.getBean(RequestMappingHandlerMapping.class);
        Map<RequestMappingInfo, HandlerMethod> map = mapping.getHandlerMethods();

        map.keySet().forEach(info -> {
            HandlerMethod handlerMethod = map.get(info);

            // 获取方法上边的注解 替代path variable 为 *
            Anonymous method = AnnotationUtils.findAnnotation(handlerMethod.getMethod(), Anonymous.class);
            Optional.ofNullable(method).ifPresent(anonymous -> {
                assert info.getPathPatternsCondition() != null;
                Objects.requireNonNull(info.getPathPatternsCondition().getPatternValues()) //
                        .forEach(url -> urls.add(ReUtil.replaceAll(url, PATTERN, ASTERISK)));
            });

            // 获取类上边的注解, 替代path variable 为 *
            Anonymous controller = AnnotationUtils.findAnnotation(handlerMethod.getBeanType(), Anonymous.class);
            Optional.ofNullable(controller).ifPresent(anonymous -> {
                assert info.getPathPatternsCondition() != null;
                Objects.requireNonNull(info.getPathPatternsCondition().getPatternValues())
                        .forEach(url -> urls.add(ReUtil.replaceAll(url, PATTERN, ASTERISK)));
            });
        });
    }

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        this.applicationContext = context;
    }

}
