package com.ruijie.nse.common.config.security.endpoint;

import com.ruijie.nse.common.dto.R;
import com.ruijie.nse.common.dto.ResultCode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.json.JSONUtil;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 认证入口点
 * 处理未认证的请求
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Component
public class AuthenticationEntryPoint implements org.springframework.security.web.AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request,
                         HttpServletResponse response,
                         AuthenticationException authException) throws IOException {
        
        log.warn("未认证的请求: {}, 异常: {}", request.getRequestURI(), authException.getMessage());
        
        // 设置响应头
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        
        // 构建响应结果
        R<Void> r = R.error(ResultCode.UNAUTHORIZED, "请先登录");
        
        // 写入响应
        response.getWriter().write(JSONUtil.toJsonStr(r));
        response.getWriter().flush();
    }
}