package com.ruijie.nse.common.constant;

public interface CommonConstant {

    interface System {
        String SUPER_ADMIN = "super_admin";
        String ADMIN = "admin";
        String USER = "user";
        String DEFAULT_PASSWORD = "admin123";
        String ROOT_MENU_ID = "0";
    }

    /**
     * JWT相关常量
     */
    interface Jwt {
        /**
         * JWT密钥
         */
        String SECRET = "7x9v2Kj5QpXWnYrLtSzM1uBcDfGhJkLmNoPqRsTuVwXy";

        /**
         * JWT过期时间（毫秒）- 24小时
         */
        Long EXPIRATION = 24 * 60 * 60 * 1000L;

        /**
         * 刷新Token过期时间（毫秒）- 7天
         */
        Long REFRESH_EXPIRATION = 7 * 24 * 60 * 60 * 1000L;

        /**
         * Token前缀
         */
        String TOKEN_PREFIX = "Bearer ";


        /**
         * token类型
         */
        String TOKEN_TYPE = "Bearer";

        /**
         * Token请求头
         */
        String TOKEN_HEADER = "Authorization";

        /**
         * 用户ID声明
         */
        String CLAIM_USER_ID = "userId";

        /**
         * 用户名声明
         */
        String CLAIM_USERNAME = "username";

        /**
         * 权限声明
         */
        String CLAIM_AUTHORITIES = "authorities";
    }

    interface Sql {
        String LIMIT_1 = " limit 1";
    }
}
