package com.ruijie.nse.common.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 返回给客户端的错误信息
 *
 * <AUTHOR>
 * @date 2018/7/5.
 */
@Data
public class ErrorOutput {


    /**
     * 错误编号，可以根据此在服务端日志中查找详细的信息
     */
    private String id;

    /**
     * 错误信息
     */
    private String message;


    /**
     * 文档地址
     */
    private String documentationUrl;


    private Date timestamp;


    /**
     * 错误详情
     */
    private List<Object> details;


    public ErrorOutput() {
        this(UUID.randomUUID().toString());
    }

    public ErrorOutput(String errorId) {
        this.id = errorId;
        this.details = new ArrayList<>(5);
        this.timestamp = new Date();
    }


    /**
     * 添加错误详情
     *
     * @param error
     */
    public void addErrorDetail(Object error) {
        this.details.add(error);
    }


}
