package com.ruijie.nse.common.dto;

import lombok.Data;

/**
 * 分页输入
 *
 * <AUTHOR>
 * @date 2018/9/7.
 */
@Data
public class PageInput {

    public static final String SORT_ASC = "asc";
    public static final String SORT_DESC = "desc";

    private int pageSize;

    private int pageNumber;

    private String sortOrder;

    private String sortName;


    public PageInput() {
        pageSize = 20;
        pageNumber = 1;
        // 按id进行升序排序
        sortOrder = SORT_ASC;
        sortName = "id";
    }
}
