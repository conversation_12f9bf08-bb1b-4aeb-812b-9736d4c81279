package com.ruijie.nse.common.dto;

import lombok.Data;

import java.util.Collection;

/**
 * 分页。与bootstrap-table配合使用。
 *
 * <AUTHOR>
 * @date 2018/7/9.
 */
@Data
public class PageOutput<T> {


    /**
     * 记录数
     */
    private Long total;


    /**
     * 行
     */
    private Collection<T> rows = null;


    public PageOutput(Long total, Collection<T> rows) {
        this.total = total;
        this.rows = rows;
    }

    public PageOutput() {
    }

}
