package com.ruijie.nse.common.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2018/12/19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseTreeEntity extends DataEntity {


    /**
     * 父级编号
     */
    protected String parentCode;

    /**
     * 所有父级编号
     */
    protected String parentCodes;

    /**
     * 本级排序号（升序）
     */
    protected Integer treeSort;

    /**
     * 所有级别排序号
     */
    protected String treeSorts;

    /**
     * 是否最末级，重要
     */
    protected Boolean treeLeaf;

    /**
     * 层次级别
     */
    protected Integer treeLevel;

    /**
     * 全节点名
     */
    protected String treeNames;

    /**
     * 节点名称
     */
    protected String name;
}
