package com.ruijie.nse.common.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 带有审计字段， created, modified, delete, remark 等字段
 *
 * <AUTHOR>
 * @date 2018/8/15.
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class DataEntity extends BaseEntity {


    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    protected String createdBy;

    /**
     * 创建日期
     */
    @TableField(fill = FieldFill.INSERT)
    protected Date createdDate;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, value = "modified_by")
    protected String modifiedBy;

    /**
     * 更新日期
     */
    @TableField(fill = FieldFill.INSERT_UPDATE, value = "modified_date")
    protected Date modifiedDate;


    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    protected Integer isDeleted;


    /**
     * 备注
     */
    protected String remark;


}
