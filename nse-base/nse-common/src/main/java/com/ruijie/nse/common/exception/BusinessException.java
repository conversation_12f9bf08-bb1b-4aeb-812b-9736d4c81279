package com.ruijie.nse.common.exception;

import lombok.Getter;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
public class BusinessException extends RuntimeException {

    @Getter
    private final String code;

    private String message;

    protected BusinessException(String code) {
        this(code, "");
    }

    protected BusinessException(String code, String message) {
        super(code + " " + message);
        this.message = message;
        this.code = code;
    }


    public static BusinessException error(String code) {
        return new BusinessException(code);
    }
    public static BusinessException errorByMessage(String message) {
        return new BusinessException("",  message);
    }
    public static BusinessException error(String code, String message) {
        return new BusinessException(code, message);
    }

    public BusinessException message(String message) {
        this.message = message;
        return this;
    }

    @Override
    public String getMessage() {
        return message;
    }


    @Override
    public String toString() {
        return "BusinessException{" +
                "error='" + code + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
}
