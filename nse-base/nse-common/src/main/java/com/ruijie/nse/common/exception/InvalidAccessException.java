package com.ruijie.nse.common.exception;

/**
 * 非法访问异常
 * 用于访问非本人的信息时，抛出的异常
 *
 * <AUTHOR>
 * @date 2018/8/28.
 */
public class InvalidAccessException extends SecurityException {


    /**
     * @param currentUserId 当前用户
     * @param targetInfo    目标信息
     */
    public InvalidAccessException(String currentUserId, Object targetInfo) {
        super("无法访问非本人信息。当前用户：" + currentUserId + ", 目标信息:" + targetInfo);
    }

    public InvalidAccessException() {
        super("无法访问非本人信息");
    }

    public InvalidAccessException(String s) {
        super(s);
    }

    public InvalidAccessException(String message, Throwable cause) {
        super(message, cause);
    }

    public InvalidAccessException(Throwable cause) {
        super(cause);
    }

    public InvalidAccessException(Integer code, String message, Throwable cause){
        super("[" + code + "]：" + message, cause);
    }

    public InvalidAccessException(Integer code, String message){
        super("[" + code + "]：" + message);
    }




}



