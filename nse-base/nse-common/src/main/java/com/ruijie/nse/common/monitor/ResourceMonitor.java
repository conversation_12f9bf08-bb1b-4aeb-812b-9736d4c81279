package com.ruijie.nse.common.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.GlobalMemory;
import oshi.hardware.HardwareAbstractionLayer;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;

/**
 * 资源监控器
 * <p>
 * 用于监控当前进程和系统的内存、CPU使用情况
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class ResourceMonitor {

    private final SystemInfo systemInfo;
    private final HardwareAbstractionLayer hardware;
    private final CentralProcessor processor;
    private final GlobalMemory memory;
    private final MemoryMXBean memoryMXBean;

    /**
     * CPU使用率缓存相关
     */
    private volatile long lastCpuCheckTime = 0L;
    private volatile double lastCpuPercent = 0.0;
    private volatile long[] lastCpuTicks;

    /**
     * CPU使用率缓存有效期（毫秒）
     */
    private static final long CPU_CACHE_DURATION = 1000L;

    public ResourceMonitor() {
        this.systemInfo = new SystemInfo();
        this.hardware = systemInfo.getHardware();
        this.processor = hardware.getProcessor();
        this.memory = hardware.getMemory();
        this.memoryMXBean = ManagementFactory.getMemoryMXBean();
        this.lastCpuTicks = processor.getSystemCpuLoadTicks();
    }

    /**
     * 获取当前进程的内存使用百分比
     *
     * @return 内存使用百分比 (0-100)
     */
    public double getProcessMemoryUsagePercent() {
        try {
            // 获取系统总内存
            long totalMemory = memory.getTotal();
            
            // 获取当前进程内存使用情况
            MemoryUsage heapMemoryUsage = memoryMXBean.getHeapMemoryUsage();
            MemoryUsage nonHeapMemoryUsage = memoryMXBean.getNonHeapMemoryUsage();
            
            // 计算进程总内存使用量（堆内存 + 非堆内存）
            long processMemoryUsed = heapMemoryUsage.getUsed() + nonHeapMemoryUsage.getUsed();
            
            // 计算当前进程内存使用百分比
            double memoryPercent = (double) processMemoryUsed / totalMemory * 100.0;
            
            log.info("进程内存使用情况: 已使用={}MB, 系统总内存={}MB, 使用率={}%",
                    processMemoryUsed / 1024 / 1024, 
                    totalMemory / 1024 / 1024, 
                    memoryPercent);
            
            return memoryPercent;
        } catch (Exception e) {
            log.warn("获取进程内存使用率失败: {}", e.getMessage());
            return 0.0;
        }
    }

    /**
     * 获取系统整体内存使用百分比
     *
     * @return 系统内存使用百分比 (0-100)
     */
    public double getSystemMemoryUsagePercent() {
        try {
            long totalMemory = memory.getTotal();
            long availableMemory = memory.getAvailable();
            long usedMemory = totalMemory - availableMemory;
            
            double memoryPercent = (double) usedMemory / totalMemory * 100.0;
            
            log.debug("系统内存使用情况: 已使用={}MB, 总内存={}MB, 使用率={}%", 
                    usedMemory / 1024 / 1024, 
                    totalMemory / 1024 / 1024, 
                    memoryPercent);
            
            return memoryPercent;
        } catch (Exception e) {
            log.warn("获取系统内存使用率失败: {}", e.getMessage());
            return 0.0;
        }
    }

    /**
     * 获取当前进程的CPU使用百分比
     * <p>
     * 注意：由于CPU使用率计算需要时间间隔，此方法会缓存结果1秒钟
     * </p>
     *
     * @return CPU使用百分比 (0-100)
     */
    public double getProcessCpuUsagePercent() {
        try {
            long currentTime = System.currentTimeMillis();
            
            // 如果距离上次检查不足1秒，返回缓存值
            if (currentTime - lastCpuCheckTime < CPU_CACHE_DURATION && lastCpuCheckTime > 0) {
                return lastCpuPercent;
            }
            
            // 获取当前CPU ticks
            long[] currentTicks = processor.getSystemCpuLoadTicks();
            
            // 计算CPU使用率
            double cpuPercent = processor.getSystemCpuLoadBetweenTicks(lastCpuTicks) * 100.0;
            
            // 更新缓存
            lastCpuTicks = currentTicks;
            lastCpuPercent = cpuPercent;
            lastCpuCheckTime = currentTime;
            
            log.debug("进程CPU使用率: {}%", cpuPercent);
            
            return cpuPercent;
        } catch (Exception e) {
            log.warn("获取进程CPU使用率失败: {}", e.getMessage());
            return 0.0;
        }
    }

    /**
     * 获取系统整体CPU使用百分比
     *
     * @return 系统CPU使用百分比 (0-100)
     */
    public double getSystemCpuUsagePercent() {
        try {
            double cpuPercent = processor.getSystemCpuLoad(1000) * 100.0;
            
            // 如果获取失败，返回0
            if (cpuPercent < 0) {
                cpuPercent = 0.0;
            }
            
            log.debug("系统CPU使用率: {}%", cpuPercent);
            
            return cpuPercent;
        } catch (Exception e) {
            log.warn("获取系统CPU使用率失败: {}", e.getMessage());
            return 0.0;
        }
    }

    /**
     * 获取内存使用率（根据参数决定是进程级别还是系统级别）
     *
     * @param checkSystemMemory 是否检查系统整体内存
     * @return 内存使用百分比 (0-100)
     */
    public double getMemoryUsagePercent(boolean checkSystemMemory) {
        return checkSystemMemory ? getSystemMemoryUsagePercent() : getProcessMemoryUsagePercent();
    }

    /**
     * 获取内存类型描述
     *
     * @param checkSystemMemory 是否检查系统整体内存
     * @return 内存类型描述
     */
    public String getMemoryTypeDescription(boolean checkSystemMemory) {
        return checkSystemMemory ? "系统" : "进程";
    }

    /**
     * 记录当前资源使用情况
     *
     * @param methodName        方法名
     * @param checkSystemMemory 是否检查系统内存
     * @param phase             阶段（执行前/执行后）
     */
    public void logResourceUsage(String methodName, boolean checkSystemMemory, String phase) {
        try {
            double memoryPercent = getMemoryUsagePercent(checkSystemMemory);
            double cpuPercent = getProcessCpuUsagePercent();
            String memoryType = getMemoryTypeDescription(checkSystemMemory);
            
            log.info("执行 {} {}资源使用情况: {}内存 {}%, CPU {}%", 
                    methodName, phase, memoryType, memoryPercent, cpuPercent);
        } catch (Exception e) {
            log.warn("记录资源使用情况失败: {}", e.getMessage());
        }
    }
}