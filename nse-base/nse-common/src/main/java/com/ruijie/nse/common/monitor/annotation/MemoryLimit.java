package com.ruijie.nse.common.monitor.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 内存限制注解
 * <p>
 * 仅检查内存使用率的便捷注解，等价于@ResourceLimit(cpuThreshold = -1)
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface MemoryLimit {

    /**
     * 内存使用率阈值（百分比，默认90%）
     *
     * @return 内存使用率阈值
     */
    double threshold() default 0.9;

    /**
     * 是否检查系统整体内存而非进程内存（默认false，检查进程内存）
     *
     * @return 是否检查系统整体内存
     */
    boolean checkSystem() default false;

    /**
     * 是否记录资源使用情况日志（默认true）
     *
     * @return 是否记录资源使用情况日志
     */
    boolean logUsage() default true;

    /**
     * 自定义错误消息前缀（可选）
     *
     * @return 自定义错误消息前缀
     */
    String errorMessagePrefix() default "";
}