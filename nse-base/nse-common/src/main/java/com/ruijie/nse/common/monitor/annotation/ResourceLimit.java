package com.ruijie.nse.common.monitor.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 资源限制注解
 * <p>
 * 用于在方法执行前检查系统资源使用情况，如果超过设定阈值则抛出ResourceExhaustedException
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ResourceLimit {

    /**
     * 内存使用率阈值（百分比，默认90%）
     *
     * @return 内存使用率阈值
     */
    double memoryThreshold() default 90.0;

    /**
     * CPU使用率阈值（百分比，-1表示不检查CPU）
     *
     * @return CPU使用率阈值
     */
    double cpuThreshold() default -1.0;

    /**
     * 是否检查系统整体内存而非进程内存（默认false，检查进程内存）
     *
     * @return 是否检查系统整体内存
     */
    boolean checkSystemMemory() default false;

    /**
     * 是否记录资源使用情况日志（默认true）
     *
     * @return 是否记录资源使用情况日志
     */
    boolean logUsage() default true;

    /**
     * 自定义错误消息前缀（可选）
     *
     * @return 自定义错误消息前缀
     */
    String errorMessagePrefix() default "";

    /**
     * 是否在方法执行后也记录资源使用情况（默认true）
     *
     * @return 是否在方法执行后记录资源使用情况
     */
    boolean logPostExecution() default true;
}