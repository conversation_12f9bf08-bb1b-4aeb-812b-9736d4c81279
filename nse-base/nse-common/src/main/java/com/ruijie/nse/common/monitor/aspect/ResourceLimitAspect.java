package com.ruijie.nse.common.monitor.aspect;

import com.ruijie.nse.common.monitor.ResourceMonitor;
import com.ruijie.nse.common.monitor.annotation.ResourceLimit;
import com.ruijie.nse.common.monitor.exception.ResourceExhaustedException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.dromara.hutool.core.text.StrUtil;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 资源限制切面
 * <p>
 * 处理@ResourceLimit注解，在方法执行前检查系统资源使用情况
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class ResourceLimitAspect {

    private final ResourceMonitor resourceMonitor;

    /**
     * 环绕通知：处理@ResourceLimit注解
     *
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 方法执行异常
     */
    @Around("@annotation(com.ruijie.nse.common.monitor.annotation.ResourceLimit)")
    public Object handleResourceLimit(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        ResourceLimit resourceLimit = method.getAnnotation(ResourceLimit.class);

        String methodName = method.getName();
        String className = method.getDeclaringClass().getSimpleName();
        String fullMethodName = className + "." + methodName;

        try {
            // 检查资源使用情况
            checkResourceUsage(resourceLimit, fullMethodName);

            // 记录执行前资源使用情况
            if (resourceLimit.logUsage()) {
                resourceMonitor.logResourceUsage(fullMethodName, resourceLimit.checkSystemMemory(), "前");
            }

            // 执行原方法
            Object result = joinPoint.proceed();

            // 记录执行后资源使用情况
            if (resourceLimit.logUsage() && resourceLimit.logPostExecution()) {
                resourceMonitor.logResourceUsage(fullMethodName, resourceLimit.checkSystemMemory(), "后");
            }

            return result;

        } catch (ResourceExhaustedException e) {
            // 资源耗尽异常直接抛出
            throw e;
        } catch (Exception e) {
            log.error("执行 {} 时发生异常: {}", fullMethodName, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 检查资源使用情况
     *
     * @param resourceLimit 资源限制配置
     * @param methodName    方法名
     * @throws ResourceExhaustedException 资源耗尽异常
     */
    private void checkResourceUsage(ResourceLimit resourceLimit, String methodName) {
        // 检查内存使用率
        checkMemoryUsage(resourceLimit, methodName);

        // 检查CPU使用率（如果设置了阈值）
        if (resourceLimit.cpuThreshold() > 0) {
            checkCpuUsage(resourceLimit, methodName);
        }
    }

    /**
     * 检查内存使用率
     *
     * @param resourceLimit 资源限制配置
     * @param methodName    方法名
     * @throws ResourceExhaustedException 内存耗尽异常
     */
    private void checkMemoryUsage(ResourceLimit resourceLimit, String methodName) {
        double currentMemory = resourceMonitor.getMemoryUsagePercent(resourceLimit.checkSystemMemory());
        double memoryThreshold = resourceLimit.memoryThreshold();
        String memoryType = resourceMonitor.getMemoryTypeDescription(resourceLimit.checkSystemMemory());

        if (currentMemory > memoryThreshold) {
            String errorMessage = buildErrorMessage(
                    resourceLimit.errorMessagePrefix(),
                    String.format("内存使用率过高: %s内存使用率 %.2f%% 超过阈值 %.2f%%, 拒绝执行 %s",
                            memoryType, currentMemory, memoryThreshold, methodName)
            );

            log.error(errorMessage);
            throw ResourceExhaustedException.memoryExhausted(errorMessage);
        }
    }

    /**
     * 检查CPU使用率
     *
     * @param resourceLimit 资源限制配置
     * @param methodName    方法名
     * @throws ResourceExhaustedException CPU耗尽异常
     */
    private void checkCpuUsage(ResourceLimit resourceLimit, String methodName) {
        double currentCpu = resourceMonitor.getProcessCpuUsagePercent();
        double cpuThreshold = resourceLimit.cpuThreshold();

        if (currentCpu > cpuThreshold) {
            String errorMessage = buildErrorMessage(
                    resourceLimit.errorMessagePrefix(), "CPU使用率过高，请等待系统负载降低后重试"
            );

            log.error(errorMessage);
            throw ResourceExhaustedException.cpuExhausted(errorMessage);
        }
    }

    /**
     * 构建错误消息
     *
     * @param prefix      自定义前缀
     * @param baseMessage 基础消息
     * @return 完整错误消息
     */
    private String buildErrorMessage(String prefix, String baseMessage) {
        if (StrUtil.isNotBlank(prefix)) {
            return prefix + ": " + baseMessage;
        }
        return baseMessage;
    }
}