package com.ruijie.nse.common.monitor.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

/**
 * 资源监控配置
 * <p>
 * 提供资源监控的全局配置选项
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Configuration
@EnableAspectJAutoProxy
@ConfigurationProperties(prefix = "nse.monitor.resource")
public class ResourceMonitorConfig {

    /**
     * 是否启用资源监控（默认启用）
     */
    private boolean enabled = true;

    /**
     * 默认内存阈值（百分比）
     */
    private double defaultMemoryThreshold = 90.0;

    /**
     * 默认CPU阈值（百分比）
     */
    private double defaultCpuThreshold = 90.0;

    /**
     * 严格模式的内存阈值（百分比）
     */
    private double strictMemoryThreshold = 80.0;

    /**
     * 严格模式的CPU阈值（百分比）
     */
    private double strictCpuThreshold = 80.0;

    /**
     * 是否默认记录资源使用日志
     */
    private boolean defaultLogUsage = true;

    /**
     * CPU使用率缓存时间（毫秒）
     */
    private long cpuCacheDuration = 1000L;

    /**
     * 是否在应用启动时输出系统资源信息
     */
    private boolean logSystemInfoOnStartup = true;

    /**
     * 资源检查超时时间（毫秒）
     */
    private long resourceCheckTimeout = 5000L;

    /**
     * 是否启用资源监控统计
     */
    private boolean enableStatistics = false;

    /**
     * 统计信息输出间隔（秒）
     */
    private int statisticsInterval = 300;
}