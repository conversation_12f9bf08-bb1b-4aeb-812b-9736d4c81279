//package com.ruijie.nse.common.monitor.example;
//
//import com.ruijie.nse.common.monitor.annotation.MemoryLimit;
//import com.ruijie.nse.common.monitor.annotation.ResourceLimit;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 资源监控功能使用示例
// *
// * 本示例展示了如何在实际业务代码中使用资源监控注解来限制方法的资源使用
// *
// * <AUTHOR> Team
// * @since 1.0.0
// */
//@Slf4j
//@Service
//public class ResourceMonitorExample {
//
//    /**
//     * 基本的资源限制示例
//     * 使用默认的90%内存和90%CPU阈值
//     */
//    @ResourceLimit
//    public String basicResourceLimit() {
//        log.info("执行基本资源限制方法");
//        // 模拟一些计算工作
//        performSomeWork();
//        return "基本资源限制执行完成";
//    }
//
//    /**
//     * 自定义资源限制阈值示例
//     * 设置内存阈值为80%，CPU阈值为85%
//     */
//    @ResourceLimit(memoryThreshold = 80.0, cpuThreshold = 85.0)
//    public String customResourceLimit() {
//        log.info("执行自定义资源限制方法");
//        performSomeWork();
//        return "自定义资源限制执行完成";
//    }
//
//    /**
//     * 仅内存限制示例
//     * 使用MemoryLimit注解，只检查内存使用率
//     */
//    @MemoryLimit(threshold = 85.0)
//    public String memoryOnlyLimit() {
//        log.info("执行仅内存限制方法");
//        // 模拟内存密集型操作
//        List<String> largeList = new ArrayList<>();
//        for (int i = 0; i < 1000; i++) {
//            largeList.add("数据项 " + i);
//        }
//        return "内存限制执行完成，处理了 " + largeList.size() + " 个数据项";
//    }
//
//    /**
//     * 严格资源限制示例
//     * 使用更严格的80%阈值同时检查内存和CPU
//     */
//    public String strictResourceLimit() {
//        log.info("执行严格资源限制方法");
//        performIntensiveWork();
//        return "严格资源限制执行完成";
//    }
//
//    /**
//     * 系统内存检查示例
//     * 检查系统整体内存使用率而不是进程内存
//     */
//    @ResourceLimit(memoryThreshold = 90.0, systemMemory = true)
//    public String systemMemoryCheck() {
//        log.info("执行系统内存检查方法");
//        performSomeWork();
//        return "系统内存检查执行完成";
//    }
//
//    /**
//     * 禁用CPU检查示例
//     * 只检查内存，不检查CPU使用率
//     */
//    @ResourceLimit(memoryThreshold = 85.0, cpuThreshold = -1)
//    public String disableCpuCheck() {
//        log.info("执行禁用CPU检查方法");
//        // 模拟CPU密集型操作，但不会因为CPU使用率高而被限制
//        performCpuIntensiveWork();
//        return "禁用CPU检查执行完成";
//    }
//
//    /**
//     * 自定义错误消息前缀示例
//     * 当资源超限时，错误消息会以指定前缀开始
//     */
//    @ResourceLimit(
//            memoryThreshold = 70.0,
//            cpuThreshold = 70.0,
//            errorMessagePrefix = "[业务处理]"
//    )
//    public String customErrorPrefix() {
//        log.info("执行自定义错误前缀方法");
//        performSomeWork();
//        return "自定义错误前缀执行完成";
//    }
//
//    /**
//     * 禁用日志记录示例
//     * 不记录方法执行前后的资源使用情况
//     */
//    @ResourceLimit(
//            memoryThreshold = 90.0,
//            cpuThreshold = 90.0,
//            logPreExecution = false,
//            logPostExecution = false
//    )
//    public String disableLogging() {
//        log.info("执行禁用日志记录方法");
//        performSomeWork();
//        return "禁用日志记录执行完成";
//    }
//
//    /**
//     * 数据处理业务示例
//     * 在实际的数据处理业务中使用资源监控
//     */
//    @MemoryLimit(threshold = 85.0, errorMessagePrefix = "[数据处理]")
//    public List<String> processLargeDataset(List<String> inputData) {
//        log.info("开始处理大数据集，输入数据量: {}", inputData.size());
//
//        List<String> processedData = new ArrayList<>();
//        for (String data : inputData) {
//            // 模拟数据处理逻辑
//            String processed = "处理后的: " + data.toUpperCase();
//            processedData.add(processed);
//        }
//
//        log.info("数据处理完成，输出数据量: {}", processedData.size());
//        return processedData;
//    }
//
//    /**
//     * 文件上传业务示例
//     * 在文件上传处理中使用严格的资源监控
//     */
//    public String handleFileUpload(String fileName, byte[] fileContent) {
//        log.info("开始处理文件上传: {}, 文件大小: {} bytes", fileName, fileContent.length);
//
//        // 模拟文件处理逻辑
//        try {
//            Thread.sleep(100); // 模拟处理时间
//        } catch (InterruptedException e) {
//            Thread.currentThread().interrupt();
//            throw new RuntimeException("文件处理被中断", e);
//        }
//
//        String result = "文件 " + fileName + " 上传处理完成";
//        log.info("文件上传处理完成: {}", fileName);
//        return result;
//    }
//
//    /**
//     * 报表生成业务示例
//     * 在报表生成中使用资源监控，因为报表生成通常是资源密集型操作
//     */
//    @ResourceLimit(
//            memoryThreshold = 80.0,
//            cpuThreshold = 85.0,
//            systemMemory = true,
//            errorMessagePrefix = "[报表生成]"
//    )
//    public String generateReport(String reportType, int dataRange) {
//        log.info("开始生成报表: {}, 数据范围: {} 天", reportType, dataRange);
//
//        // 模拟报表生成的资源密集型操作
//        performIntensiveWork();
//
//        String result = String.format("报表生成完成: %s (数据范围: %d 天)", reportType, dataRange);
//        log.info("报表生成完成: {}", reportType);
//        return result;
//    }
//
//    /**
//     * 批量数据导入示例
//     * 在批量数据导入中使用内存限制
//     */
//    @MemoryLimit(
//            threshold = 80.0,
//            systemMemory = true,
//            errorMessagePrefix = "[批量导入]"
//    )
//    public int batchImportData(List<Object> dataList) {
//        log.info("开始批量导入数据，数据量: {}", dataList.size());
//
//        int processedCount = 0;
//        for (Object data : dataList) {
//            // 模拟数据导入处理
//            processDataItem(data);
//            processedCount++;
//
//            // 每处理100条记录休息一下
//            if (processedCount % 100 == 0) {
//                try {
//                    Thread.sleep(10);
//                } catch (InterruptedException e) {
//                    Thread.currentThread().interrupt();
//                    break;
//                }
//            }
//        }
//
//        log.info("批量导入完成，处理数据量: {}", processedCount);
//        return processedCount;
//    }
//
//    // ========== 辅助方法 ==========
//
//    /**
//     * 模拟一般性工作负载
//     */
//    private void performSomeWork() {
//        try {
//            // 模拟一些计算工作
//            Thread.sleep(50);
//            for (int i = 0; i < 1000; i++) {
//                Math.sqrt(i);
//            }
//        } catch (InterruptedException e) {
//            Thread.currentThread().interrupt();
//        }
//    }
//
//    /**
//     * 模拟密集型工作负载
//     */
//    private void performIntensiveWork() {
//        try {
//            // 模拟更密集的计算工作
//            Thread.sleep(100);
//            for (int i = 0; i < 10000; i++) {
//                Math.pow(i, 2);
//            }
//        } catch (InterruptedException e) {
//            Thread.currentThread().interrupt();
//        }
//    }
//
//    /**
//     * 模拟CPU密集型工作
//     */
//    private void performCpuIntensiveWork() {
//        // 模拟CPU密集型计算
//        for (int i = 0; i < 50000; i++) {
//            Math.sin(i) * Math.cos(i);
//        }
//    }
//
//    /**
//     * 模拟处理单个数据项
//     */
//    private void processDataItem(Object data) {
//        // 模拟数据处理逻辑
//        try {
//            Thread.sleep(1);
//        } catch (InterruptedException e) {
//            Thread.currentThread().interrupt();
//        }
//    }
//}