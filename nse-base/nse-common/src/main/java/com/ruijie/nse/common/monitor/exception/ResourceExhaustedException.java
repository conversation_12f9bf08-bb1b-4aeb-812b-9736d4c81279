package com.ruijie.nse.common.monitor.exception;

import com.ruijie.nse.common.exception.BusinessException;

/**
 * 资源耗尽异常
 * <p>
 * 当系统资源（内存、CPU等）使用率超过设定阈值时抛出此异常
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class ResourceExhaustedException extends BusinessException {

    /**
     * 内存使用率过高错误码
     */
    public static final String MEMORY_EXHAUSTED = "RESOURCE_MEMORY_EXHAUSTED";

    /**
     * CPU使用率过高错误码
     */
    public static final String CPU_EXHAUSTED = "RESOURCE_CPU_EXHAUSTED";

    /**
     * 系统资源综合过高错误码
     */
    public static final String SYSTEM_EXHAUSTED = "RESOURCE_SYSTEM_EXHAUSTED";

    /**
     * 构造函数
     *
     * @param code    错误码
     * @param message 错误消息
     */
    public ResourceExhaustedException(String code, String message) {
        super(code, message);
    }

    /**
     * 创建内存耗尽异常
     *
     * @param message 错误消息
     * @return ResourceExhaustedException实例
     */
    public static ResourceExhaustedException memoryExhausted(String message) {
        return new ResourceExhaustedException(MEMORY_EXHAUSTED, message);
    }

    /**
     * 创建CPU耗尽异常
     *
     * @param message 错误消息
     * @return ResourceExhaustedException实例
     */
    public static ResourceExhaustedException cpuExhausted(String message) {
        return new ResourceExhaustedException(CPU_EXHAUSTED, message);
    }

    /**
     * 创建系统资源耗尽异常
     *
     * @param message 错误消息
     * @return ResourceExhaustedException实例
     */
    public static ResourceExhaustedException systemExhausted(String message) {
        return new ResourceExhaustedException(SYSTEM_EXHAUSTED, message);
    }

    /**
     * 创建通用资源耗尽异常
     *
     * @param message 错误消息
     * @return ResourceExhaustedException实例
     */
    public static ResourceExhaustedException of(String message) {
        return new ResourceExhaustedException(SYSTEM_EXHAUSTED, message);
    }
}