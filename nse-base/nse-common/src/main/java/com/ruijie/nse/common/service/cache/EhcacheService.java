package com.ruijie.nse.common.service.cache;


import com.ruijie.nse.common.utils.bean.BeanCopierUtils;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;
import org.springframework.cache.jcache.JCacheCacheManager;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;

@Slf4j
@Service
@RequiredArgsConstructor
public class EhcacheService {

    private final JCacheCacheManager cacheManager;


    public void put(String cacheName, String key, Object value) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.put(key, value);
        }
    }


    /**
     * 根据key获取缓存
     * @param cacheName
     * @param key
     * @param type
     * @return
     * @param <T>
     */
    public <T> T get(String cacheName, String key, Class<T> type) {
        Cache cache = cacheManager.getCache(cacheName);
        if(cache == null) {
            return null;
        }
        Cache.ValueWrapper valueWrapper = cache.get(key);
        if (valueWrapper == null) {
            return null;
        }
        Object rawValue = valueWrapper.get();
        if (type.isInstance(rawValue)) {
            return type.cast(rawValue);
        }
        try {
            // 尝试创建目标类型的新实例
            T result = type.getDeclaredConstructor().newInstance();
            assert rawValue != null;
            BeanCopierUtils.copy(rawValue, result);
            return result;
        } catch (Exception e) {
            throw new RuntimeException("获取缓存数据，类型转换异常", e);
        }
    }

    public <T> T getOrCompute(String cacheName, String key, Class<T> type, Callable<T> valueLoader) {
        T value = get(cacheName, key, type);
        if (value == null) {
            try {
                value = valueLoader.call();
                put(cacheName, key, value);
            } catch (Exception e) {
                throw new RuntimeException("计算缓存数据，类型转换异常", e);
            }
        }
        return value;
    }

    /**
     * 根据key删除缓存
     * @param cacheName
     * @param key
     */
    public void evict(String cacheName, String key) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.evict(key);
        }
    }

    /**
     * 清空缓存
     * @param cacheName
     */
    public void clear(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.clear();
        }
    }

    /**
     * 获取缓存中的所有值
     * @param cacheName
     * @param type
     * @return
     * @param <T>
     */
    public <T> List<T> getAllValues(@NonNull String cacheName, Class<T> type) {
        List<T> result = new ArrayList<>();
        
        try {
            Cache cache = cacheManager.getCache(cacheName);
            if (cache == null) {
                return result;
            }
            
            // 获取原生的JCache实例
            javax.cache.Cache<Object, Object> nativeCache = (javax.cache.Cache<Object, Object>) cache.getNativeCache();

            nativeCache.forEach(entry -> {
                Object rawValue = entry.getValue();
                if (rawValue == null) {
                    return ;
                }

                if (type.isInstance(rawValue)) {
                    result.add(type.cast(rawValue));
                    return;
                }

                // 处理String类型特殊情况
                if (String.class.equals(type)) {
                    result.add(type.cast(rawValue.toString()));
                    return;
                }

                try {
                    T convertedValue = type.getDeclaredConstructor().newInstance();
                    BeanCopierUtils.copy(rawValue, convertedValue);
                    result.add(convertedValue);
                } catch (Exception e) {
                    log.warn("类型转换异常，源类型：{}，目标类型：{}", rawValue.getClass().getSimpleName(), type.getSimpleName(), e);
                }
            });

        } catch (Exception e) {
            log.error("获取缓存中的所有值失败，缓存名称：{}", cacheName, e);
        }
        
        return result;
    }

}
