package com.ruijie.nse.common.utils.oshi;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.dromara.hutool.core.text.StrPool;
import org.dromara.hutool.core.text.StrUtil;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.HWDiskStore;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.hardware.NetworkIF;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.List;


/**
 * 获取硬件机器码信息
 * <AUTHOR>
 * @date 2025-07-18
 */
@Slf4j
public class HardwareUtil {

    private volatile static String machineCodeHash = null;


    /**
     * 获取缓存中的机器码
     * @return
     */
    public static String getMachineCode() {
        if(StrUtil.isBlankIfStr(machineCodeHash)) {
            machineCodeHash = generateMachineCode();
        }
        return machineCodeHash;
    }

    /**
     * 生成当前机器的机器码，并返回hash值
     * SHA-256
     *
     * @return
     */
    public static String generateMachineCode() {
        try {
            SystemInfo systemInfo = new SystemInfo();
            HardwareAbstractionLayer hardware = systemInfo.getHardware();

            // 处理器
            String cpuId = getCpuId(hardware.getProcessor());

            // 设备序列号
            String serialNumber = hardware.getComputerSystem().getSerialNumber();

            // 网卡mac
            String macAddress = getMacAddress(hardware.getNetworkIFs(false));

            // 磁盘序列号
            String diskSerial = getDiskSerial(hardware.getDiskStores());

            // 组合硬件信息
            // 处理器ID@主板序列号@mac地址@磁盘序列号
            String combined = cpuId + StrPool.AT + serialNumber + StrPool.AT + macAddress + StrPool.AT + diskSerial;
            // 生成哈希值作为机器码
            return hashSHA256(combined);
        } catch (Exception e) {
            throw new RuntimeException("生成机器码失败", e);
        }
    }


    /**
     * 获取处理器信息
     * @param processor
     * @return
     */
    private static String getCpuId(CentralProcessor processor) {
        String processorId = processor.getProcessorIdentifier().getProcessorID();
        return processorId != null ? processorId : "";
    }


    /**
     * 网卡mac，获取第一个非空的mac
     * @param networkIFs
     * @return
     */
    private static String getMacAddress(List<NetworkIF> networkIFs) {
        if (networkIFs != null) {
            for (NetworkIF net : networkIFs) {
                if (!net.getMacaddr().isEmpty() && !net.getMacaddr().equals("00:00:00:00:00:00")) {
                    return net.getMacaddr();
                }
            }
        }
        return "";
    }


    /**
     * 磁盘
     * @param diskStores
     * @return
     */
    private static String getDiskSerial(List<HWDiskStore> diskStores) {
        if (diskStores != null && !diskStores.isEmpty()) {
            String serial = diskStores.get(0).getSerial();
            return serial != null ? serial.trim() : "";
        }
        return "";
    }


    /**
     * 生成hash
     * @param input
     * @return
     * @throws Exception
     */
    private static String hashSHA256(String input) throws Exception {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] hash = digest.digest(input.getBytes(StandardCharsets.UTF_8));
        return Hex.encodeHexString(hash);
    }

    public static void main(String[] args) {
        System.out.println(generateMachineCode());
    }

}
