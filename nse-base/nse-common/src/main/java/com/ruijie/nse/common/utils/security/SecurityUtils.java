package com.ruijie.nse.common.utils.security;


import com.ruijie.nse.common.config.security.UserPrincipal;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * 获取用户上下文信息
 * <AUTHOR>
 */
public class SecurityUtils {

    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    public static UserPrincipal getUserInfo() {
        return (UserPrincipal) getAuthentication().getPrincipal();
    }

    public static String getUserId() {
        return getUserInfo().getUserId();
    }

    public static String getUserType() {
        return getUserInfo().getUserType();
    }

    public static String getUserName() {
        return getUserInfo().getUsername();
    }

    public static String getAccount() {
        return getUserInfo().getAccount();
    }

    public static boolean isSuperAdmin() {
        return getUserInfo().isSuperAdmin();
    }

}
