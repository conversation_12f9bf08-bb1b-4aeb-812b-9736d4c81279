package com.ruijie.nse.common.utils.system;

import lombok.extern.slf4j.Slf4j;

/**
 * 操作系统工具类
 * 提供跨平台的操作系统检测和相关工具方法
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
public class OSUtil {

    /**
     * 操作系统类型枚举
     */
    public enum OSType {
        WINDOWS("Windows"),
        LINUX("Linux"),
        MAC("Mac OS"),
        UNKNOWN("Unknown");

        private final String displayName;

        OSType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }

    /**
     * 缓存的操作系统类型
     */
    private static volatile OSType cachedOSType = null;

    /**
     * 获取当前操作系统类型
     * 
     * @return 操作系统类型
     */
    public static OSType getOSType() {
        if (cachedOSType != null) {
            return cachedOSType;
        }

        synchronized (OSUtil.class) {
            if (cachedOSType != null) {
                return cachedOSType;
            }

            String osName = System.getProperty("os.name", "").toLowerCase();
            log.debug("检测到操作系统: {}", osName);

            if (osName.contains("win")) {
                cachedOSType = OSType.WINDOWS;
            } else if (osName.contains("nix") || osName.contains("nux") || osName.contains("aix")) {
                cachedOSType = OSType.LINUX;
            } else if (osName.contains("mac") || osName.contains("darwin")) {
                cachedOSType = OSType.MAC;
            } else {
                cachedOSType = OSType.UNKNOWN;
                log.warn("未识别的操作系统类型: {}", osName);
            }

            log.info("当前操作系统: {}", cachedOSType.getDisplayName());
            return cachedOSType;
        }
    }

    /**
     * 判断是否为Windows系统
     * 
     * @return true表示Windows系统
     */
    public static boolean isWindows() {
        return getOSType() == OSType.WINDOWS;
    }

    /**
     * 判断是否为Linux系统
     * 
     * @return true表示Linux系统
     */
    public static boolean isLinux() {
        return getOSType() == OSType.LINUX;
    }

    /**
     * 判断是否为Mac系统
     * 
     * @return true表示Mac系统
     */
    public static boolean isMac() {
        return getOSType() == OSType.MAC;
    }

    /**
     * 判断是否为Unix-like系统（Linux或Mac）
     * 
     * @return true表示Unix-like系统
     */
    public static boolean isUnixLike() {
        OSType osType = getOSType();
        return osType == OSType.LINUX || osType == OSType.MAC;
    }

    /**
     * 获取操作系统名称
     * 
     * @return 操作系统名称
     */
    public static String getOSName() {
        return System.getProperty("os.name", "Unknown");
    }

    /**
     * 获取操作系统版本
     * 
     * @return 操作系统版本
     */
    public static String getOSVersion() {
        return System.getProperty("os.version", "Unknown");
    }

    /**
     * 获取操作系统架构
     * 
     * @return 操作系统架构
     */
    public static String getOSArch() {
        return System.getProperty("os.arch", "Unknown");
    }

    /**
     * 获取系统详细信息
     * 
     * @return 系统详细信息字符串
     */
    public static String getSystemInfo() {
        return String.format("操作系统: %s %s (%s)", 
                getOSName(), getOSVersion(), getOSArch());
    }

    /**
     * 获取文件路径分隔符
     * 
     * @return 文件路径分隔符
     */
    public static String getFileSeparator() {
        return System.getProperty("file.separator");
    }

    /**
     * 获取路径分隔符
     * 
     * @return 路径分隔符
     */
    public static String getPathSeparator() {
        return System.getProperty("path.separator");
    }

    /**
     * 获取行分隔符
     * 
     * @return 行分隔符
     */
    public static String getLineSeparator() {
        return System.getProperty("line.separator");
    }

    /**
     * 清除缓存的操作系统类型
     * 主要用于测试
     */
    public static void clearCache() {
        cachedOSType = null;
    }

    /**
     * 获取当前用户名
     * 
     * @return 当前用户名
     */
    public static String getCurrentUser() {
        return System.getProperty("user.name", "Unknown");
    }

    /**
     * 获取用户主目录
     * 
     * @return 用户主目录路径
     */
    public static String getUserHome() {
        return System.getProperty("user.home", "");
    }

    /**
     * 获取临时目录
     * 
     * @return 临时目录路径
     */
    public static String getTempDir() {
        return System.getProperty("java.io.tmpdir", "");
    }

    /**
     * 判断是否为64位系统
     * 
     * @return true表示64位系统
     */
    public static boolean is64Bit() {
        String arch = getOSArch().toLowerCase();
        return arch.contains("64");
    }

    /**
     * 获取系统环境变量
     * 
     * @param name 环境变量名
     * @return 环境变量值，不存在返回null
     */
    public static String getEnv(String name) {
        return System.getenv(name);
    }

    /**
     * 获取系统环境变量，带默认值
     * 
     * @param name 环境变量名
     * @param defaultValue 默认值
     * @return 环境变量值，不存在返回默认值
     */
    public static String getEnv(String name, String defaultValue) {
        String value = System.getenv(name);
        return value != null ? value : defaultValue;
    }
}
