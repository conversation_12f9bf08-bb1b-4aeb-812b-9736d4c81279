<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.ruijie.nse</groupId>
    <artifactId>nse-base</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>nse-common</module>
        <module>nse-dependency</module>
    </modules>

    <properties>
        <java.version>21</java.version>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <nse.dependency.version>1.0.0-SNAPSHOT</nse.dependency.version>
    </properties>


    <dependencyManagement>
        <dependencies>
            <!-- 引入依赖管理BOM -->
            <dependency>
                <groupId>com.ruijie.nse</groupId>
                <artifactId>nse-dependency</artifactId>
                <version>${nse.dependency.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>