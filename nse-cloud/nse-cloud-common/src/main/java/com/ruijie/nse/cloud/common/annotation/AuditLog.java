package com.ruijie.nse.cloud.common.annotation;



import com.ruijie.nse.cloud.common.annotation.enums.LogModuleEnum;

import java.lang.annotation.*;

/**
 * 审计日志注解
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
@Documented
public @interface AuditLog {

    /**
     * 日志模块
     *
     * @return 日志模块
     */

    LogModuleEnum module();

    /**
     * 标题
     *
     * @return 标题
     */
    String title() default "";
}