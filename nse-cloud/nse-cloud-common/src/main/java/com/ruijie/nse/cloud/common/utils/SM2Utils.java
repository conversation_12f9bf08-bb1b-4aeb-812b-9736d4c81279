package com.ruijie.nse.cloud.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.crypto.params.ParametersWithRandom;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPublicKey;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.jce.spec.ECParameterSpec;
import org.dromara.hutool.core.io.IoUtil;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.*;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

/**
 * SM2工具类
 * 国密SM2非对称加密算法实现
 */
@Slf4j
public class SM2Utils {

    private static final String ALGORITHM = "EC";
    private static final String PROVIDER = "BC";
    private static final String CURVE_NAME = "sm2p256v1";
    
    static {
        // 添加BouncyCastle作为安全提供者
        if (Security.getProvider(PROVIDER) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    /**
     * 生成SM2密钥对
     * @return 密钥对
     * @throws Exception 生成密钥对异常
     */
    public static KeyPair generateKeyPair() throws Exception {
        KeyPairGenerator generator = KeyPairGenerator.getInstance(ALGORITHM, PROVIDER);
        X9ECParameters ecParameters = GMNamedCurves.getByName(CURVE_NAME);
        ECParameterSpec ecSpec = new ECParameterSpec(
                ecParameters.getCurve(),
                ecParameters.getG(),
                ecParameters.getN(),
                ecParameters.getH());
        generator.initialize(ecSpec, new SecureRandom());
        return generator.generateKeyPair();
    }

    /**
     * 从PEM格式文件加载SM2公钥
     * @param inputStream 公钥文件输入流
     * @return SM2公钥
     * @throws Exception 加载公钥异常
     */
    public static PublicKey loadPublicKey(InputStream inputStream) throws Exception {
        String pemContent = IoUtil.readUtf8(inputStream)
                .replaceAll("-----.*-----", "").replaceAll("\\s", "");
        byte[] keyBytes = Base64.getDecoder().decode(pemContent);
        return KeyFactory.getInstance(ALGORITHM, PROVIDER)
                .generatePublic(new X509EncodedKeySpec(keyBytes));
    }
    
    /**
     * 从PEM格式文件加载SM2私钥
     * @param inputStream 私钥文件输入流
     * @return SM2私钥
     * @throws Exception 加载私钥异常
     */
    public static PrivateKey loadPrivateKey(InputStream inputStream) throws Exception {
        String pemContent = IoUtil.readUtf8(inputStream)
                .replaceAll("-----.*-----", "").replaceAll("\\s", "");
        byte[] keyBytes = Base64.getDecoder().decode(pemContent);
        return KeyFactory.getInstance(ALGORITHM, PROVIDER)
                .generatePrivate(new PKCS8EncodedKeySpec(keyBytes));
    }

    /**
     * 使用SM2公钥加密数据
     * @param content 待加密内容
     * @param publicKey SM2公钥
     * @return Base64编码的加密结果
     */
    public static String encryptSm2(String content, PublicKey publicKey) {
        try {
            if (publicKey == null) {
                throw new RuntimeException("未配置SM2公钥，无法加密明文内容");
            }

            X9ECParameters ecParameters = GMNamedCurves.getByName(CURVE_NAME);
            ECDomainParameters domainParameters = new ECDomainParameters(
                    ecParameters.getCurve(),
                    ecParameters.getG(),
                    ecParameters.getN(),
                    ecParameters.getH());

            // 转换为BC的公钥格式
            BCECPublicKey bcecPublicKey = (BCECPublicKey) publicKey;
            ECPublicKeyParameters publicKeyParameters = new ECPublicKeyParameters(
                    bcecPublicKey.getQ(),
                    domainParameters);

            // 初始化SM2加密引擎
            SM2Engine engine = new SM2Engine(SM2Engine.Mode.C1C3C2);
            engine.init(true, new ParametersWithRandom(publicKeyParameters, new SecureRandom()));

            // 执行加密
            byte[] inputBytes = content.getBytes(StandardCharsets.UTF_8);
            byte[] encryptedBytes = engine.processBlock(inputBytes, 0, inputBytes.length);

            // 返回Base64编码的加密结果
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            log.error("License数字签名失败", e);
            throw new RuntimeException("License数字签名失败: " + e.getMessage(), e);
        }
    }

    /**
     * 使用SM2私钥解密数据
     * @param encryptedBase64 Base64编码的加密内容
     * @param privateKey SM2私钥
     * @return 解密后的明文
     */
    public static String decryptSm2(String encryptedBase64, PrivateKey privateKey) {
        try {
            if (privateKey == null) {
                throw new RuntimeException("未配置SM2私钥，无法解密内容");
            }

            X9ECParameters ecParameters = GMNamedCurves.getByName(CURVE_NAME);
            ECDomainParameters domainParameters = new ECDomainParameters(
                    ecParameters.getCurve(),
                    ecParameters.getG(),
                    ecParameters.getN(),
                    ecParameters.getH());

            // 转换为BC的私钥格式
            BCECPrivateKey bcecPrivateKey = (BCECPrivateKey) privateKey;
            ECPrivateKeyParameters privateKeyParameters = new ECPrivateKeyParameters(
                    bcecPrivateKey.getD(),
                    domainParameters);

            // 初始化SM2解密引擎
            SM2Engine engine = new SM2Engine(SM2Engine.Mode.C1C3C2);
            engine.init(false, privateKeyParameters);

            // 解码Base64密文
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedBase64);

            // 执行解密
            byte[] decryptedBytes = engine.processBlock(encryptedBytes, 0, encryptedBytes.length);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("SM2解密失败", e);
            throw new RuntimeException("License数据内容有误，请确认", e);
        }
    }

    /**
     * 保存PEM格式密钥到文件
     * @param path 文件路径
     * @param key 密钥
     * @param header 密钥头部标识
     * @throws Exception 保存密钥异常
     */
    public static void saveKeyToFile(String path, Key key, String header) throws Exception {
        String content = header + "\n" +
                Base64.getMimeEncoder().encodeToString(key.getEncoded()) +
                "\n" + header.replace("BEGIN", "END");
        Files.write(Paths.get(path), content.getBytes());
    }
}