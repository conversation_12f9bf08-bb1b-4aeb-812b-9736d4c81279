package com.ruijie.nse.cloud.common.utils.pdf;

import com.itextpdf.html2pdf.ConverterProperties;
import com.itextpdf.html2pdf.HtmlConverter;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.kernel.events.PdfDocumentEvent;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.geom.PageSize;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.font.FontProvider;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.thymeleaf.templateresolver.ClassLoaderTemplateResolver;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Map;

/**
 * Itext7转换工具类
 */
@Slf4j
public class HtmlToPdfUtils {

    /**
     * html转pdf
     *
     * @param htmlFileName 模板文件名称
     * @param data         数据填充
     * @param pdfFilePath  输出的pdf文件
     * @param waterMark    水印
     * @param fontPath     字体路径，ttc后缀的字体需要添加<b>,0<b/>
     */
    public static void htmlToPdf(String htmlFileName, Map<String, Object> data, String pdfFilePath, String waterMark, String fontPath) throws IOException {
        // 模板引擎渲染模板内容
        TemplateEngine templateEngine = new TemplateEngine();
        ClassLoaderTemplateResolver resolver = new ClassLoaderTemplateResolver();
        resolver.setPrefix("/templates/");
        resolver.setSuffix(".html");
        templateEngine.setTemplateResolver(resolver);
        Context context = new Context();
        context.setVariables(data);
        String html = templateEngine.process(htmlFileName, context);

        PdfWriter pdfWriter = new PdfWriter(Files.newOutputStream(Paths.get(pdfFilePath)));
        PdfDocument pdfDocument = new PdfDocument(pdfWriter);

        // 设置为A4大小
        pdfDocument.setDefaultPageSize(PageSize.A4);
        // 添加水印
        pdfDocument.addEventHandler(PdfDocumentEvent.END_PAGE, new WaterMarkEventHandler(waterMark));

        // 添加中文字体支持
        ConverterProperties properties = new ConverterProperties();
        FontProvider fontProvider = new FontProvider();

        // 设置字体
        // 添加自定义字体，例如微软雅黑
        if (CharSequenceUtil.isNotBlank(fontPath)) {
            PdfFont microsoft = PdfFontFactory.createFont(fontPath, PdfEncodings.IDENTITY_H, false);
            fontProvider.addFont(microsoft.getFontProgram(), PdfEncodings.IDENTITY_H);
        }else{
            // 默认字体
            PdfFont sysFont = PdfFontFactory.createFont("STSongStd-Light", "UniGB-UCS2-H", false);
            fontProvider.addFont(sysFont.getFontProgram(), "UniGB-UCS2-H");
        }

        properties.setFontProvider(fontProvider);
        //  生成pdf文档
        HtmlConverter.convertToPdf(html, pdfDocument, properties);
        pdfWriter.close();
        pdfDocument.close();
    }
}