package com.ruijie.nse.cloud.launcher;

import com.ruijie.nse.cloud.license.controller.LicenseCodeController;
import com.ruijie.nse.cloud.repository.entity.LicenseCode;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


@SpringBootApplication(scanBasePackages = "com.ruijie.*")
@MapperScan("com.ruijie.**.mapper")
@EnableAspectJAutoProxy
@EnableCaching
public class NseCloudApplication {

    public static void main(String[] args) {
        //关闭热部署
        System.setProperty("spring.devtools.restart.enabled","false");

//        System.setProperty(CacheManager.ENABLE_SHUTDOWN_HOOK_PROPERTY, "true");

        ConfigurableApplicationContext context = SpringApplication.run(NseCloudApplication.class, args);
        ConfigurableEnvironment environment = context.getEnvironment();
        String info = """

                ----------------------------------------------------------
                应用 '%s' 运行成功! 当前环境 '%s' !!! 端口 '[%s]' !!!
                ----------------------------------------------------------

                """;
        System.out.printf((info) + "%n",
                environment.getProperty("spring.application.name"),
                Arrays.toString(environment.getActiveProfiles()),
                environment.getProperty("server.port"));
    }
}
