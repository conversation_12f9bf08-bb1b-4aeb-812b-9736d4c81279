package com.ruijie.nse.license.service;


import com.ruijie.nse.cloud.launcher.NseCloudApplication;
import com.ruijie.nse.cloud.license.service.LicenseFileBuilderService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.nio.file.Path;

@SpringBootTest
@RunWith(SpringRunner.class)
@ContextConfiguration(classes = NseCloudApplication.class)
public class LicenseBuilderServiceTest {

    @Autowired
    private LicenseFileBuilderService licenseBuilderService;


    @Test
    public void testGenerateLicenseFile() {
        Path path = licenseBuilderService.buildLicenseFile("1949755761167773697");
        System.out.println(path);
    }

}
