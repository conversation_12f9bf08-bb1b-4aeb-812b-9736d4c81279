package com.ruijie.nse.license.utils;

import com.ruijie.nse.cloud.common.utils.SM2Utils;
import com.ruijie.nse.cloud.launcher.NseCloudApplication;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

/**
 * SM2工具类测试
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@ContextConfiguration(classes = NseCloudApplication.class)
public class SM2UtilsTest {

    @Test
    public void testSM2EncryptDecrypt() throws Exception {
        // 生成SM2密钥对
        KeyPair keyPair = SM2Utils.generateKeyPair();
        assertNotNull(keyPair);
        
        PublicKey publicKey = keyPair.getPublic();
        PrivateKey privateKey = keyPair.getPrivate();
        
        // 测试数据
        String originalText = "这是一段需要加密的测试数据";
        
        // 使用公钥加密
        String encryptedText = SM2Utils.encryptSm2(originalText, publicKey);
        assertNotNull(encryptedText);
        System.out.println("加密后的数据: " + encryptedText);
        
        // 使用私钥解密
        String decryptedText = SM2Utils.decryptSm2(encryptedText, privateKey);
        assertNotNull(decryptedText);
        System.out.println("解密后的数据: " + decryptedText);
        
        // 验证解密结果与原始数据一致
        assertEquals(originalText, decryptedText);
    }

    @Test
    public void testSM2PemFile() throws Exception {
        // 生成SM2密钥对
        KeyPair keyPair = SM2Utils.generateKeyPair();
        SM2Utils.saveKeyToFile("C:\\__ruijie_work_space\\nse\\pem2\\public_key.pem", keyPair.getPublic(), "-----BEGIN NSE-RUIJIE PUBLIC KEY-----");
        SM2Utils.saveKeyToFile("C:\\__ruijie_work_space\\nse\\pem2\\private_key.pem", keyPair.getPrivate(), "-----BEGIN NSE-RUIJIE PRIVATE KEY-----");
    }
}