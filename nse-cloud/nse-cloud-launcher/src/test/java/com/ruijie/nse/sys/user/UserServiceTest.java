package com.ruijie.nse.sys.user;

import com.ruijie.nse.cloud.repository.entity.User;
import com.ruijie.nse.cloud.launcher.NseCloudApplication;
import com.ruijie.nse.cloud.sys.service.UserService;
import com.ruijie.nse.common.constant.CommonConstant;
import com.ruijie.nse.common.utils.enctry.PasswordUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
@ContextConfiguration(classes = NseCloudApplication.class)
public class UserServiceTest {

    @Autowired
    private UserService userService;


    @Test
    public void testSaveUser(){
        String[] saltAndPassword = PasswordUtil.generateSaltAndEncodePassword(CommonConstant.System.DEFAULT_PASSWORD);
        User user = new User();
        user.setSalt(saltAndPassword[0]);
        user.setPassword(saltAndPassword[1]);
        user.setAccount("admin");
        user.setName("管理员");
        userService.save(user);
    }

}
