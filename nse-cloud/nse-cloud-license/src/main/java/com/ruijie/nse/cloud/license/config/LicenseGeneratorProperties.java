package com.ruijie.nse.cloud.license.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * License生成器配置属性
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Component
@ConfigurationProperties(prefix = "nse.license.generator")
public class LicenseGeneratorProperties {

    /**
     * License输出目录
     */
    private String outputDir = "./workspace/license-output";

    /**
     * 默认发行方
     */
    private String issuer = "Ruijie";

    /**
     * 默认产品名称
     */
    private String format = "NSE-LICENSE-1.0";

    /**
     * 默认license文件扩展名
     */
    private String filExtension = "lic";

    private String productVersion = "1.0.0";

    private List<String> modules = List.of();

}
