package com.ruijie.nse.cloud.license.controller;

import com.ruijie.nse.cloud.common.annotation.AuditLog;
import com.ruijie.nse.cloud.common.annotation.enums.LogModuleEnum;
import com.ruijie.nse.cloud.license.service.LicenseCodeService;
import com.ruijie.nse.cloud.license.pojo.dto.LicenseCodeDto;
import com.ruijie.nse.cloud.license.pojo.dto.LicenseCodeQueryDto;
import com.ruijie.nse.cloud.license.pojo.vo.LicenseCodeInfoVo;
import com.ruijie.nse.cloud.license.pojo.vo.LicenseCodeVo;
import com.ruijie.nse.common.dto.PageInput;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.dto.R;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/license-code")
public class LicenseCodeController {

    @Autowired
    private LicenseCodeService licenseCodeService;

    /**
     * 分页查询授权码记录列表
     *
     * @param queryDto  查询参数
     * @param pageInput 分页参数
     * @return PageOutput<List < LicenseCodeVo>>
     */
    @GetMapping("/listPageQuery")
    public R<PageOutput<LicenseCodeVo>> listPageQuery(LicenseCodeQueryDto queryDto, PageInput pageInput) {
        return R.success(licenseCodeService.listPageQuery(queryDto, pageInput));
    }

    /**
     * 授权码申请
     *
     * @return LicenseCodeVo
     */
    @AuditLog(module = LogModuleEnum.LICENSE, title = "授权码申请")
    @PostMapping(value = "/apply", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<LicenseCodeVo> apply(@ModelAttribute LicenseCodeDto licenseCodeDto) throws IOException {
        return R.success(licenseCodeService.apply(licenseCodeDto));
    }


    /**
     * 获取详情
     * @param id
     * @return
     */
    @GetMapping("/detail/{id}")
    public R<LicenseCodeVo> detail(@PathVariable String id) {
        return R.success(licenseCodeService.detail(id));
    }


    @AuditLog(module = LogModuleEnum.LICENSE, title = "下载授权文件")
    @GetMapping("/downloadDocumentation")
    public R<Void> downloadDocumentation(@RequestParam("url") String url, HttpServletResponse response) throws IOException {
        File file = new File(url);
        Path path = Paths.get(file.getAbsolutePath());
        // 下载文件
        response.setHeader("content-disposition", "attachment; filename=" + file.getName());
        response.setContentType("application/octet-stream");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            IOUtils.copy(Files.newInputStream(path), outputStream);
        }
        return R.success();
    }

    /**
     * 【授权绑定申请】通过授权码获取授权码销售信息回显
     *
     * @param licenseCode 授权码
     * @return LicenseCodeInfoVo
     */
    @GetMapping("/salesInfoByCode")
    public R<LicenseCodeInfoVo> salesInfoByCode(@RequestParam("licenseCode") String licenseCode) {
        return R.success(licenseCodeService.salesInfoByCode(licenseCode));
    }

    /**
     * 下载授权
     *
     * @param id       主键id
     * @param response 响应体
     */
    @AuditLog(module = LogModuleEnum.LICENSE, title = "下载软件使用授权书")
    @GetMapping("/downloadLicenseFile")
    public void downloadLicenseFile(String id, HttpServletResponse response) throws IOException {
        licenseCodeService.downloadLicenseFile(id, response);
    }

}
