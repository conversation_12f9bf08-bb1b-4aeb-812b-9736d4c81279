package com.ruijie.nse.cloud.license.pojo;

import lombok.*;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


/**
 * lic内容
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class License {

    /**
     * 授权申请码
     */
    private String activationCode;
    /**
     * 注销码
     */
    private String cancelCode;

    /**
     * lic使用情况
     */
    private Useage useage;

    /**
     * lic具体内容
     */
    private Cdata cdata;



    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Useage {
        /**
         * lic状态. 0-未使用, 1-已使用, 2-已过期, 3-已撤销
         */
        private Integer status;
        /**
         * 使用时间
         */
        private String usetime;
        /**
         * 使用说明
         */
        private String usedetail;
    }


    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Cdata {

        /**
         * 动态指纹
         */
        private String finger;

        /**
         * cdata内容签名
         */
        private String signature;
        private Header header;
        private Payload payload;


        @Getter
        @Setter
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Header {
            private String format;
            private String issuer;
            private String issueDate;
        }

        @Getter
        @Setter
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Payload {
            private String licenseId;
            private Product product;
            private Validity validity;
            private Restriction restrictions;

            private Map<String, Object> ext;


            @Getter
            @Setter
            @Builder
            @NoArgsConstructor
            @AllArgsConstructor
            public static class Product {
                private String name;
                private String version;
                private List<String> modules;
            }

            @Getter
            @Setter
            @Builder
            @NoArgsConstructor
            @AllArgsConstructor
            public static class Validity {
                private String start;
                private String end;
                private String validType;
            }

            @Getter
            @Setter
            @Builder
            @NoArgsConstructor
            @AllArgsConstructor
            public static class Restriction {
                private Integer mgr;
                private Integer user;
                private String hardwareHash;
            }

        }

    }

}
