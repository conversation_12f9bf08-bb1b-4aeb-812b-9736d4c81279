package com.ruijie.nse.cloud.license.pojo.dto;

import lombok.Getter;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class LicenseCodeDto {
    /**
     * 授权类型
     */
    private String licenseType;
    /**
     * 销售订单编号
     */
    private String salesOrderNo;
    /**
     * 销售项目名称
     */
    private String salesProjectName;
    /**
     * 最终客户名称
     */
    private String finalCustomerName;
    /**
     * 项目销售
     */
    private String sellerName;
    /**
     * 有效期
     */
    private String validType;
    /**
     * 普通账号并发数量
     */
    private Integer permitUserCnt;
    /**
     * 管理员账号并发数量
     */
    private Integer permitMgrCnt;
    /**
     * 证明文件
     */
    private MultipartFile documentation;
}