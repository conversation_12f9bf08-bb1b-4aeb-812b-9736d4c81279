package com.ruijie.nse.cloud.license.pojo.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class LicenseCodeInfoVo {
    /**
     * 销售订单编号
     */
    private String salesOrderNo;
    /**
     * 销售项目名称
     */
    private String salesProjectName;
    /**
     * 最终客户名称
     */
    private String finalCustomerName;
    /**
     * 项目销售
     */
    private String sellerName;
    /**
     * 授权产品信息
     */
    private String productInfo;
    /**
     * 有效期
     */
    private String validType;
    /**
     * 终端许可数量（管理员）
     */
    private Integer permitMgrCnt;
    /**
     * 终端许可数量（普通用户）
     */
    private Integer permitUserCnt;

    /**
     * 下载授权按钮loading
     */
    private boolean downloadLoading;
}