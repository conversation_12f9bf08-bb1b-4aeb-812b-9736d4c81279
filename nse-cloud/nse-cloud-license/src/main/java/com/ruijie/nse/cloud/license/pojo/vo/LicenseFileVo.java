package com.ruijie.nse.cloud.license.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class LicenseFileVo {
    private String id;
    /**
     * 销售订单编号
     */
    private String salesOrderNo;
    /**
     * 销售项目名称
     */
    private String salesProjectName;
    /**
     * 最终客户名称
     */
    private String finalCustomerName;
    /**
     * 项目销售
     */
    private String sellerName;
    /**
     * 授权产品信息
     */
    private String licenseProductInfo;
    /**
     * 授权码信息
     */
    private String licenseCodeInfo;
    /**
     * 终端许可数量
     */
    private String terminalLicenseCount;
    /**
     * 有效期
     */
    private String validType;
    /**
     * 授权类型
     */
    private String licenseType;
    /**
     * 服务器机器码
     */
    private String machineCode;
    /**
     * 服务器注销码
     */
    private String machineCancelCode;
    /**
     * 申请人
     */
    private String applicant;
    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date applyDate;

    private boolean downloadLoading;
}