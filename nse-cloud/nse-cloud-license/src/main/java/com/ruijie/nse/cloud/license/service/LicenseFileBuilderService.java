package com.ruijie.nse.cloud.license.service;

import com.ruijie.nse.cloud.common.utils.SM2Utils;
import com.ruijie.nse.cloud.license.config.LicenseGeneratorProperties;
import com.ruijie.nse.cloud.license.pojo.License;
import com.ruijie.nse.cloud.repository.entity.LicenseFile;
import com.ruijie.nse.common.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.date.DateFormatPool;
import org.dromara.hutool.core.date.DateUtil;
import org.dromara.hutool.core.io.file.FileUtil;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.dromara.hutool.core.text.StrUtil;
import org.dromara.hutool.http.meta.HttpStatus;
import org.dromara.hutool.json.JSONUtil;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import java.io.File;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Date;
import java.util.HexFormat;
import java.util.Objects;

/**
 * License文件生成服务
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LicenseFileBuilderService {

    private final LicenseFileService licenseFileService;
    private final LicenseGeneratorProperties licenseGeneratorProperties;

    // 加密算法常量
    private static final String AES_ALGORITHM = "AES";
    private static final String AES_TRANSFORMATION = "AES/GCM/NoPadding";
    private static final String RSA_ALGORITHM = "RSA";
    private static final String RSA_SIGNATURE_ALGORITHM = "SHA256withRSA";
    private static final String HASH_ALGORITHM = "SHA-256";
    private static final int GCM_IV_LENGTH = 12;
    private static final int GCM_TAG_LENGTH = 16;


    /**
     * 生成License文件
     *
     * @param licenseId 申请授权文件ID
     * @return 生成结果
     */
    public Path buildLicenseFile(String licenseId) {
        log.info("开始生成License文件，License ID: {}", licenseId);

        try {
            LicenseFile licenseFile = licenseFileService.getById(licenseId);

            checkLicenseException(licenseFile);

            // 1. 构建License对象
            License license = buildLicense(licenseFile);

            // 2. 序列化为JSON
            String licenseJson = JSONUtil.toJsonStr(license);
            log.debug("License JSON内容: {}", licenseJson);

            // 3. 加密License内容
            String encryptedContent = encryptLicenseContent(licenseJson);

            // 4. rsa加密
            String encryptRsaContent = SM2Utils.encryptSm2(encryptedContent, getCloudPublicKeyBase64());

            // 5. 构建最终License文件内容
            String fileName = generateFileName(licenseFile.getActivationCode());
            Path filePath = saveToFile(encryptRsaContent, fileName);

            log.info("License文件生成成功: {}", filePath);
            return filePath;

        } catch (Exception e) {
            throw new RuntimeException("License文件生成失败", e);
        }
    }

    /**
     * 构建License对象
     *
     * @param licenseFile 授权文件申请记录
     * @return License对象
     */
    private License buildLicense(LicenseFile licenseFile) {
        String currentTime = LocalDateTime.now().format(DateFormatPool.NORM_DATETIME_FORMATTER);

        // 构建Header
        License.Cdata.Header header = License.Cdata.Header.builder()
                .format(licenseGeneratorProperties.getFormat())
                .issuer(licenseGeneratorProperties.getIssuer())
                .issueDate(currentTime)
                .build();

        // 构建Product
        License.Cdata.Payload.Product product = License.Cdata.Payload.Product.builder()
                .name(licenseFile.getActivationProductInfo())
                .version(licenseGeneratorProperties.getProductVersion())
                .modules(licenseGeneratorProperties.getModules())
                .build();

        // 构建Validity
        License.Cdata.Payload.Validity validity = License.Cdata.Payload.Validity.builder()
                // 毫秒级
                .start(String.valueOf(licenseFile.getValidFrom().toInstant().toEpochMilli()))
                .end(String.valueOf(licenseFile.getValidTo().toInstant().toEpochMilli()))
                .validType(licenseFile.getValidType())
                .build();

        // 构建Restriction
        License.Cdata.Payload.Restriction restrictions = License.Cdata.Payload.Restriction.builder()
                .mgr(licenseFile.getPermitMgrCnt())
                .user(licenseFile.getPermitUserCnt())
                .hardwareHash(licenseFile.getMachineCode())
                .build();

        // 构建Payload
        License.Cdata.Payload payload = License.Cdata.Payload.builder()
                .licenseId(licenseFile.getId())
                .product(product)
                .validity(validity)
                .restrictions(restrictions)
                .build();

        // 生成指纹和签名
        String signature = generateContentSignature(licenseFile.getFinger(), header, payload);

        // 构建Cdata
        License.Cdata cdata = License.Cdata.builder()
                .finger(licenseFile.getFinger())
                .signature(signature)
                .header(header)
                .payload(payload)
                .build();

        // 构建Usage
        License.Useage usage = License.Useage.builder()
                .status(0) // 0-未使用
                .usetime(null)
                .usedetail("License已生成，等待激活")
                .build();

        // 构建完整License
        return License.builder()
                .activationCode(licenseFile.getActivationCode())
                .cancelCode(licenseFile.getCancelCode())
                .useage(usage)
                .cdata(cdata)
                .build();
    }


    /**
     * 生成内容签名
     *
     * @param payload License载荷
     * @return 内容签名
     */
    private String generateContentSignature(String finger, License.Cdata.Header header, License.Cdata.Payload payload) {
        try {
            String payloadJson = JSONUtil.toJsonStr(payload);
            String headerJson = JSONUtil.toJsonStr(header);
            MessageDigest digest = MessageDigest.getInstance(HASH_ALGORITHM);

            //
            String contentToSign = headerJson + "|" + finger + "|" + payloadJson + "|" + finger;
            log.debug("[license file 生成] - 待签名内容: {}", contentToSign);
            byte[] hash = digest.digest(contentToSign.getBytes(StandardCharsets.UTF_8));
            return HexFormat.of().formatHex(hash);

        } catch (Exception e) {
            throw new RuntimeException("[license file 生成] - 生成内容签名失败", e);
        }
    }

    /**
     * 加密License内容
     *
     * @param content License内容
     * @return 加密后的Base64字符串
     */
    private String encryptLicenseContent(String content) {
        try {
            // 生成AES密钥
            KeyGenerator keyGenerator = KeyGenerator.getInstance(AES_ALGORITHM);
            keyGenerator.init(256);
            SecretKey secretKey = keyGenerator.generateKey();

            // 生成随机IV
            byte[] iv = new byte[GCM_IV_LENGTH];
            new SecureRandom().nextBytes(iv);

            // 执行AES-GCM加密
            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, gcmSpec);

            byte[] encryptedData = cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));

            // 组合加密结果：密钥 + IV + 加密数据
            byte[] keyBytes = secretKey.getEncoded();
            byte[] result = new byte[keyBytes.length + iv.length + encryptedData.length];

            System.arraycopy(keyBytes, 0, result, 0, keyBytes.length);
            System.arraycopy(iv, 0, result, keyBytes.length, iv.length);
            System.arraycopy(encryptedData, 0, result, keyBytes.length + iv.length, encryptedData.length);

            return Base64.getEncoder().encodeToString(result);

        } catch (Exception e) {
            log.error("License内容加密失败", e);
            throw new RuntimeException("License内容加密失败: " + e.getMessage(), e);
        }
    }


    /**
     * 生成文件名
     *
     * @param activationCode 授权码
     * @return 文件名
     */
    private String generateFileName(String activationCode) {
        String timestamp = DateUtil.format(new Date(), "yyyyMMdd_HHmmss");
        return CharSequenceUtil.format("{}_{}.{}", activationCode, timestamp, licenseGeneratorProperties.getFilExtension());
    }

    /**
     * 保存License文件
     *
     * @param licenseContent License内容
     * @param fileName       文件名
     * @return 文件路径
     */
    private Path saveToFile(String licenseContent, String fileName) {
        try {
            // 确保输出目录存在
            File outputDir = new File(licenseGeneratorProperties.getOutputDir());
            if (!outputDir.exists()) {
                boolean created = outputDir.mkdirs();
                if (!created) {
                    throw new RuntimeException("无法创建License输出目录: " + licenseGeneratorProperties.getOutputDir());
                }
            }

            // 构建文件路径
            Path filePath = Paths.get(licenseGeneratorProperties.getOutputDir(), fileName);
            FileUtil.writeUtf8String(licenseContent, filePath.toFile());

            log.info("License文件已保存: {}", filePath.toAbsolutePath());
            return filePath.toAbsolutePath();

        } catch (Exception e) {
            log.error("保存License文件失败", e);
            throw new RuntimeException("保存License文件失败: " + e.getMessage(), e);
        }
    }

    private void checkLicenseException(LicenseFile licenseFile) {
        if(Objects.isNull(licenseFile) || StrUtil.isNotBlank(licenseFile.getLicenseUrl())) {
            throw BusinessException.error(String.valueOf(HttpStatus.HTTP_INTERNAL_ERROR), "License申请记录异常，请确认");
        }
    }


    /**
     * 获取resource下的public_key.pem的内容
     * 私钥加密
     * @return
     */
    private PublicKey getCloudPublicKeyBase64() {
        try (InputStream inputStream = this.getClass().getResourceAsStream("/certs/public_key.pem")) {
            // 读取PEM文件内容
            return SM2Utils.loadPublicKey(inputStream);
        } catch (Exception e) {
            throw new RuntimeException("获取云平台私钥失败", e);
        }
    }
}
