package com.ruijie.nse.cloud.license.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruijie.nse.cloud.license.constants.LicenseConstants;
import com.ruijie.nse.cloud.license.pojo.dto.LicenseFileDto;
import com.ruijie.nse.cloud.license.pojo.dto.LicenseFileQueryDto;
import com.ruijie.nse.cloud.license.pojo.vo.LicenseFileVo;
import com.ruijie.nse.cloud.repository.entity.LicenseCode;
import com.ruijie.nse.cloud.repository.entity.LicenseFile;
import com.ruijie.nse.cloud.repository.mapper.LicenseCodeDao;
import com.ruijie.nse.cloud.repository.mapper.LicenseFileDao;
import com.ruijie.nse.common.dto.PageInput;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.exception.BusinessException;
import com.ruijie.nse.common.utils.bean.BeanCopierUtils;
import com.ruijie.nse.common.utils.security.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.dromara.hutool.core.data.id.IdUtil;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class LicenseFileService extends ServiceImpl<LicenseFileDao, LicenseFile> {

    private final LicenseCodeDao licenseCodeDao;

    /**
     * 分页查询授权文件记录列表
     *
     * @param queryDto  查询参数
     * @param pageInput 分页参数
     * @return PageOutput<List < LicenseFileVo>>
     */
    public PageOutput<LicenseFileVo> listPageQuery(LicenseFileQueryDto queryDto, PageInput pageInput) {
        Page<LicenseFile> pageInfo = lambdaQuery()
                .like(CharSequenceUtil.isNotBlank(queryDto.getSalesOrderNo()), LicenseFile::getSalesOrderNo, queryDto.getSalesOrderNo())
                .like(CharSequenceUtil.isNotBlank(queryDto.getSalesProjectName()), LicenseFile::getSalesProjectName, queryDto.getSalesProjectName())
                .like(CharSequenceUtil.isNotBlank(queryDto.getFinalCustomerName()), LicenseFile::getFinalCustomerName, queryDto.getFinalCustomerName())
                .like(CharSequenceUtil.isNotBlank(queryDto.getMachineCode()), LicenseFile::getMachineCode, queryDto.getMachineCode())
                .eq(CharSequenceUtil.isNotBlank(queryDto.getLicenseType()), LicenseFile::getActivationType, queryDto.getLicenseType())
                .like(CharSequenceUtil.isNotBlank(queryDto.getApplicant()), LicenseFile::getApplicant, queryDto.getApplicant())
                .orderByDesc(LicenseFile::getCreatedDate)
                .page(Page.of(pageInput.getPageNumber(), pageInput.getPageSize()));

        List<LicenseFile> records = pageInfo.getRecords();

        List<LicenseFileVo> licenseCodeVos = BeanCopierUtils.copyListProperties(records, LicenseFileVo::new, (s, t) -> {
            t.setLicenseType(s.getActivationType());
            t.setLicenseProductInfo(s.getActivationProductInfo());
            t.setLicenseCodeInfo(s.getActivationCode());
            t.setTerminalLicenseCount(CharSequenceUtil.format("普通用户{}+管理员{}", s.getPermitUserCnt(), s.getPermitMgrCnt()));
            t.setLicenseType(s.getActivationType());
            t.setMachineCancelCode(s.getCancelCode());
            t.setApplyDate(s.getCreatedDate());
        });
        return new PageOutput<>(pageInfo.getTotal(), licenseCodeVos);
    }

    /**
     * 授权绑定申请
     *
     * @return LicenseFileVo
     */
    @Transactional(rollbackFor = Exception.class)
    public LicenseFileVo apply(LicenseFileDto licenseFileDto) {
        LicenseCode codeInfo = licenseCodeDao.getCodeInfo(licenseFileDto.getLicenseCode());
        if (codeInfo == null) {
            throw BusinessException.error("500", "授权码不存在！");
        }
        LicenseFile licenseFile = BeanCopierUtils.copy(licenseFileDto, LicenseFile::new, (s, t) -> {
            t.setFinger(IdUtil.nanoId(32));
            t.setActivationCode(s.getLicenseCode());
            t.setActivationType(codeInfo.getType());
            t.setActivationProductInfo(codeInfo.getProductInfo());
            t.setStatus(codeInfo.getStatus());
            t.setPermitUserCnt(codeInfo.getPermitUserCnt());
            t.setPermitMgrCnt(codeInfo.getPermitMgrCnt());
            t.setValidType(codeInfo.getValidType());
            t.setValidFrom(codeInfo.getValidFrom());
            t.setValidTo(codeInfo.getValidTo());
            t.setCancelCode(IdUtil.nanoId());
            t.setApplicant(SecurityUtils.getUserName());
        });
        String machineCancelCode = licenseFileDto.getMachineCancelCode();
        if(CharSequenceUtil.isNotBlank(machineCancelCode)){
            lambdaUpdate()
                    .set(LicenseFile::getIsDeleted, 1)
                    .set(LicenseFile::getStatus, "已撤销")
                    .eq(LicenseFile::getCancelCode, machineCancelCode)
                    .update();
        }
        save(licenseFile);

        // 将该注销码设置为已使用
        licenseCodeDao.update(Wrappers.lambdaUpdate(LicenseCode.class)
                .set(LicenseCode::getStatus, LicenseConstants.LicenseCodeStatus.USED)
                .eq(LicenseCode::getCode, licenseFileDto.getLicenseCode())
        );


        return BeanCopierUtils.copy(licenseFile, LicenseFileVo::new, (s, t) -> {
            t.setTerminalLicenseCount(CharSequenceUtil.format("普通用户{}+管理员{}", s.getPermitUserCnt(), s.getPermitMgrCnt()));
            t.setLicenseType(s.getActivationType());
            t.setLicenseProductInfo(s.getActivationProductInfo());
            t.setLicenseCodeInfo(s.getActivationCode());
            t.setApplyDate(s.getCreatedDate());
        });
    }

    /**
     * 【授权绑定申请】通过授权码获取机器码
     *
     * @param licenseCode 授权码
     * @return String
     */
    public String getMachineCode(String licenseCode) {
        LicenseFile licenseFile = lambdaQuery().eq(LicenseFile::getActivationCode, licenseCode)
                .select(LicenseFile::getMachineCode)
                .one();
        return Optional.ofNullable(licenseFile).map(LicenseFile::getMachineCode).orElse(null);
    }

    /**
     * 注销码是否正确
     *
     * @param licenseCode 授权码
     * @param cancelCode 注销码
     * @return boolean
     */
    public Boolean isCancelCodeCorrect(String licenseCode, String cancelCode) {
        return lambdaQuery()
                .eq(LicenseFile::getActivationCode, licenseCode)
                .eq(LicenseFile::getCancelCode, cancelCode)
                .count() > 0;
    }
}
