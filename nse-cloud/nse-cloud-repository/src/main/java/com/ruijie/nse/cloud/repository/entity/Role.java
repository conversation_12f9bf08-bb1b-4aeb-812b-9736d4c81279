package com.ruijie.nse.cloud.repository.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.ruijie.nse.common.entity.DataEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-08-14
 */

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("sys_role")
public class Role extends DataEntity {


    /**
     * 名称
     */
    private String name;

    /**
     * 等级
     */
    private Integer level;

}
