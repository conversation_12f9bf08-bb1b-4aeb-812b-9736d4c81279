package com.ruijie.nse.cloud.repository.entity.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

/**
 * 用户类型枚举
 */
@Getter
public enum UserType implements IEnum<String> {

    TEACHER("TEACHER", "老师"),
    STUDENT("STUDENT","学生");

    private String value;
    private String description;

    UserType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }
}
