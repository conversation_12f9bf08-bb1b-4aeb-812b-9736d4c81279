package com.ruijie.nse.cloud.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruijie.nse.cloud.repository.entity.LicenseCode;
import org.apache.ibatis.annotations.Param;


/**
 * <AUTHOR>
 */
public interface LicenseCodeDao extends BaseMapper<LicenseCode> {

    /**
     * 根据授权码查询相关快照信息
     *
     * @param licenseCode 授权码
     * @return LicenseCode
     */
    LicenseCode getCodeInfo(@Param("licenseCode") String licenseCode);
}
