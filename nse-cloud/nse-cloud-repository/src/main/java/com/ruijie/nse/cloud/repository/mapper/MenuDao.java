package com.ruijie.nse.cloud.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruijie.nse.cloud.repository.entity.Menu;

import java.util.List;

/**
 * <p>
 * 菜单表 Mapper 接口
 * </p>
 */
public interface MenuDao extends BaseMapper<Menu> {

    /**
     * 根据用户ID查询菜单
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<Menu> selectMenusByUserId(String userId);

    /**
     * 获取菜单路由列表
     *
     * @param roles 角色集合
     */
    List<Menu> getMenusByRoles(List<String> roles);
}
