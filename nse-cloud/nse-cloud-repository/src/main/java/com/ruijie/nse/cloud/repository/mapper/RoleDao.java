package com.ruijie.nse.cloud.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruijie.nse.cloud.repository.entity.Role;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;


/**
 * <p>
 * 角色表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-08-14
 */
public interface RoleDao extends BaseMapper<Role> {

    /**
     * 通过userId来查找角色
     *
     * @param userId
     * @return
     */
    List<Role> listRolesByUserId(String userId);


    /**
     * 根据roleId查找权限
     *
     * @param roleId
     * @return
     */
    List<String> listMenusByRoleId(String roleId);

    /**
     * 删除某个角色的权限
     *
     * @param roleId
     */
    void deleteMenusOfRole(String roleId);


    /**
     * 插入菜单到角色中
     *
     * @param menus
     * @param roleId
     */
    void insertMenusToRole(@Param("menus") Collection<String> menus,
                                 @Param("roleId") String roleId);


    /**
     * 根据角色id查找拥有此角色的用户
     *
     * @param roleId
     * @return
     */
    List<String> findUserListByRoleId(String roleId);
}
