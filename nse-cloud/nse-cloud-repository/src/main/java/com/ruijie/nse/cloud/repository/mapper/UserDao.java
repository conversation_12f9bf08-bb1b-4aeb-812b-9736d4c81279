package com.ruijie.nse.cloud.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruijie.nse.cloud.repository.entity.User;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-08-15
 */
public interface UserDao extends BaseMapper<User> {


    /**
     * 根据roleId来查找user
     *
     * @param roleId
     * @return
     */
    List<User> selectUserByRoleId(String roleId);


    /**
     * 根据userId来查找权限
     *
     * @param roleId
     * @return
     */
    List<String> listPermissionsByUserId(String roleId);

    /**
     * 根据userId来查找角色ID
     *
     * @param roleId
     * @return
     */
    List<String> listRolesByUserId(String roleId);


    /**
     * 删除某个用户的所有角色
     *
     * @param userId
     */
    void deleteRolesOfUser(String userId);

    /**
     * 给某个用户添加角色
     *
     * @param roles
     * @param userId
     */
    void insertRolesToUser(@Param("roles") Collection<String> roles,
                           @Param("userId") String userId);


    /**
     * 根据用户id查到其所拥有的权限
     *
     * @param userId
     * @return
     */
    List<String> findUserRoleByUserId(String userId);
}
