package com.ruijie.nse.cloud.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruijie.nse.cloud.repository.entity.UserRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户角色关联表 Mapper 接口
 * </p>
 */
public interface UserRoleDao extends BaseMapper<UserRole> {

    /**
     * 批量新增用户角色关联
     *
     * @param userRoleList 用户角色列表
     * @return 结果
     */
    int batchInsert(List<UserRole> userRoleList);

    /**
     * 通过用户ID删除用户角色关联
     *
     * @param userId 用户ID
     * @return 结果
     */
    int deleteByUserId(String userId);

    /**
     * 通过角色ID删除用户角色关联
     *
     * @param roleId 角色ID
     * @return 结果
     */
    int deleteByRoleId(String roleId);

    /**
     * 通过用户ID和角色ID列表批量删除
     *
     * @param userId  用户ID
     * @param roleIds 角色ID列表
     * @return 结果
     */
    int deleteByUserIdAndRoleIds(@Param("userId") String userId, @Param("roleIds") List<String> roleIds);

    /**
     * 查询用户拥有的角色ID列表
     *
     * @param userId 用户ID
     * @return 角色ID列表
     */
    List<String> selectRoleIdsByUserId(String userId);
}
