<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijie.nse.cloud.repository.mapper.LicenseCodeDao">

    <select id="getCodeInfo" resultType="com.ruijie.nse.cloud.repository.entity.LicenseCode">
        select
            product_info,
            "type",
            status,
            permit_user_cnt,
            permit_mgr_cnt,
            valid_type,
            valid_from,
            valid_to
        from
            lic_activation_code
        where code = #{licenseCode}
    </select>
</mapper>
