<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijie.nse.cloud.repository.mapper.MenuDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id,
        a.name,
        a.parent_id,
        a.route_name,
        a.route_path,
        a.component,
        a.icon,
        a.sort,
        a.visible,
        a.redirect,
        a.type,
        a.always_show,
        a.keep_alive,
        a.params,
        a.remark,
        a.created_date,
        a.created_by,
        a.modified_date,
        a.modified_by
    </sql>

    <!-- 根据用户ID查询菜单 -->
    <select id="selectMenusByUserId" parameterType="string" resultType="com.ruijie.nse.cloud.repository.entity.Menu">
        SELECT DISTINCT
        <include refid="Base_Column_List"/>
        FROM sys_menu a
        LEFT JOIN sys_role_menu b ON a.id = b.menu_id
        LEFT JOIN sys_user_role c ON b.role_id = c.role_id
        WHERE c.user_id = #{userId}
        AND a.is_deleted = 0
        ORDER BY a.order_num
    </select>

    <!-- 获取路由列表 -->
    <select id="getMenusByRoles" resultType="com.ruijie.nse.cloud.repository.entity.Menu">
        SELECT
            DISTINCT
            <include refid="Base_Column_List"/>
        FROM
        sys_menu a
        INNER JOIN sys_role_menu b ON a.id = b.menu_id
        INNER JOIN sys_role c ON b.role_id = c.id AND c.is_deleted = 0
        WHERE
        a.is_deleted = 0 and
        a.type != '${@<EMAIL>()}'
        <choose>
            <when test="roles != null and roles.size() > 0">
                AND c.name IN
                <foreach item="roleCode" open="(" close=")" separator="," collection="roles" >
                    #{roleCode}
                </foreach>
            </when>
            <otherwise>
                AND 1 = 0
            </otherwise>
        </choose>
        ORDER BY
        a.sort
    </select>


</mapper>
