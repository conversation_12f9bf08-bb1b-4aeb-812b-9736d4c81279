<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijie.nse.cloud.repository.mapper.RoleDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id,
        a.name,
        a.level,
        a.remark,
        a.created_date ,
        a.created_by ,
        a.modified_date ,
        a.modified_by
    </sql>


    <select id="listRolesByUserId" parameterType="string"
            resultType="com.ruijie.nse.cloud.repository.entity.Role">
        select
        <include refid="Base_Column_List" />
        from sys_role a, sys_user_role b
        WHERE
        b.role_id = a.id and b.user_id = #{userId}
    </select>


    <select id="listMenusByRoleId" resultType="java.lang.String">
        select menu_id
        from sys_role_menu
        where role_id = #{role_id}
    </select>

    <select id="findUserListByRoleId" resultType="java.lang.String">
        SELECT sys_user.name FROM sys_role
        LEFT JOIN sys_user_role ON sys_role.id=sys_user_role.role_id
        LEFT JOIN sys_user ON sys_user_role.user_id=sys_user.id
        WHERE sys_role.id = #{role_id}
    </select>

    <delete id="deleteMenusOfRole" parameterType="string">
        delete from
        sys_role_menu
        where role_id = #{role_id}
    </delete>


    <insert id="insertMenusToRole">
        insert into sys_role_menu ( role_id, menu_id ) values
        <foreach collection="menus" item="menu" separator=",">
            (#{roleId}, #{menu})
        </foreach>
    </insert>


</mapper>
