<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijie.nse.cloud.repository.mapper.UserDao">


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id, a.name, a.password, a.salt,
        a.avatar, a.mobile_phone, a.email, a.employee_id,
        a.login_ip, a.login_date,
        a.created_date, a.created_by, a.modified_date, a.modified_by,
        a.status, a.is_deleted, a.remark

    </sql>


    <select id="selectUserByRoleId" parameterType="string"
            resultType="com.ruijie.nse.cloud.repository.entity.User">
        select
        <include refid="Base_Column_List" />
        from sys_user a
        left join sys_user_role b ON b.user_id = a.id
        WHERE b.role_id = #{roleId}
    </select>

    <select id="listPermissionsByUserId" resultType="java.lang.String">
        select sm.perm from sys_role_menu srm
        inner join sys_menu sm on sm.id = srm.menu_id
        where role_id in
        ( select role_id from sys_user_role
        where user_id = #{user_id}
        )
    </select>

    <select id="findUserRoleByUserId" resultType="java.lang.String">
        SELECT sys_role.name FROM sys_user_role
        LEFT JOIN sys_role ON sys_user_role.role_id=sys_role.id
        WHERE sys_user_role.user_id=#{user_id}

    </select>


    <select id="listRolesByUserId" resultType="java.lang.String">
        SELECT sys_role.name FROM sys_user_role
        LEFT JOIN sys_role ON sys_user_role.role_id=sys_role.id
        WHERE sys_user_role.user_id=#{user_id}
    </select>


    <delete id="deleteRolesOfUser">
        delete from
        sys_user_role
        where user_id = #{userId}
    </delete>

    <insert id="insertRolesToUser">
        insert into sys_user_role(user_id, role_id ) values
        <foreach collection="roles" item="roleId" separator=",">
            (#{userId}, #{roleId})
        </foreach>
    </insert>

</mapper>
