package com.ruijie.nse.cloud.sys.config.security;

import com.ruijie.nse.common.config.security.UserPrincipal;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

/**
 * 加盐认证提供者
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SaltedAuthenticationProvider implements AuthenticationProvider {

    private final UserDetailsServiceImpl userDetailsService;
    private final SaltedPasswordEncoder saltedPasswordEncoder;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String account = authentication.getName();
        String password = authentication.getCredentials().toString();

        log.debug("开始认证用户: {}", account);

        try {
            // 加载用户详情
            UserPrincipal userPrincipal = userDetailsService.loadUserByUsername(account);
            
            // 验证密码
            boolean passwordMatches = saltedPasswordEncoder.matches(
                password, account, userPrincipal.getPassword()
            );
            
            if (!passwordMatches) {
                log.warn("用户密码验证失败: {}", account);
                throw new BadCredentialsException("用户名或密码错误");
            }

            log.debug("用户认证成功: {}", account);
            
            // 创建认证成功的Authentication对象
            return new UsernamePasswordAuthenticationToken(
                    userPrincipal,
                null, // 清除密码
                    userPrincipal.getAuthorities()
            );
            
        } catch (AuthenticationException e) {
            log.error("用户认证失败: {}, 原因: {}", account, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("用户认证过程中发生异常: {}", account, e);
            throw new BadCredentialsException("认证过程中发生异常", e);
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }
}