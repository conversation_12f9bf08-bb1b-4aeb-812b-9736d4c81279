package com.ruijie.nse.cloud.sys.controller;

import com.ruijie.nse.cloud.sys.dto.output.OnlineUserOutput;
import com.ruijie.nse.cloud.sys.service.OnlineUserService;
import com.ruijie.nse.common.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 云端在线用户管理控制器
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@RestController
@RequestMapping("/api/online-users")
@RequiredArgsConstructor
public class OnlineUserController {

    private final OnlineUserService onlineUserService;

    /**
     * 获取当前所有在线用户列表
     * 
     * @return 在线用户列表
     */
    @GetMapping("/list")
    @PreAuthorize("hasAuthority('sys:user:view')")
    public R<List<OnlineUserOutput>> getOnlineUsers() {
        try {
            List<OnlineUserOutput> onlineUsers = onlineUserService.getOnlineUsers();
            return R.success(onlineUsers);
        } catch (Exception e) {
            log.error("获取在线用户列表失败", e);
            return R.error("获取在线用户列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取在线用户数量统计
     * 
     * @return 在线用户统计信息
     */
    @GetMapping("/count")
    @PreAuthorize("hasAuthority('sys:user:view')")
    public R<Map<String, Object>> getOnlineUserCount() {
        try {
            int onlineCount = onlineUserService.getOnlineUserCount();
            List<OnlineUserOutput> onlineUsers = onlineUserService.getOnlineUsers();
            
            // 统计不同类型用户数量
            long teacherCount = onlineUsers.stream()
                    .filter(user -> user.getUserType() != null && "TEACHER".equals(user.getUserType().name()))
                    .count();
            
            long studentCount = onlineUsers.stream()
                    .filter(user -> user.getUserType() != null && "STUDENT".equals(user.getUserType().name()))
                    .count();
            
            long adminCount = onlineUsers.stream()
                    .filter(user -> user.getUserType() != null && "ADMIN".equals(user.getUserType().name()))
                    .count();
            
            Map<String, Object> statistics = Map.of(
                    "totalOnline", onlineCount,
                    "teacherCount", teacherCount,
                    "studentCount", studentCount,
                    "adminCount", adminCount,
                    "lastUpdateTime", System.currentTimeMillis()
            );
            
            return R.success(statistics);
        } catch (Exception e) {
            log.error("获取在线用户统计失败", e);
            return R.error("获取在线用户统计失败: " + e.getMessage());
        }
    }

    /**
     * 检查指定用户是否在线
     * 
     * @param userId 用户ID
     * @return 是否在线
     */
    @GetMapping("/check/{userId}")
    @PreAuthorize("hasAuthority('sys:user:view')")
    public R<Map<String, Object>> checkUserOnline(@PathVariable String userId) {
        try {
            boolean isOnline = onlineUserService.isUserOnline(userId);
            OnlineUserOutput userInfo = null;
            
            if (isOnline) {
                userInfo = onlineUserService.getOnlineUserInfo(userId);
            }
            
            Map<String, Object> result = Map.of(
                    "userId", userId,
                    "isOnline", isOnline,
                    "userInfo", userInfo != null ? userInfo : "用户不在线"
            );
            
            return R.success(result);
        } catch (Exception e) {
            log.error("检查用户在线状态失败, userId: {}", userId, e);
            return R.error("检查用户在线状态失败: " + e.getMessage());
        }
    }

    /**
     * 强制指定用户下线
     * 
     * @param userId 用户ID
     * @return 操作结果
     */
    @PostMapping("/force-logout/{userId}")
    @PreAuthorize("hasAuthority('sys:user:manage')")
    public R<String> forceLogout(@PathVariable String userId) {
        try {
            boolean success = onlineUserService.forceLogout(userId);
            
            if (success) {
                log.info("管理员强制用户下线成功, userId: {}", userId);
                return R.success("用户已强制下线");
            } else {
                return R.error("强制下线失败，用户可能已经离线");
            }
        } catch (Exception e) {
            log.error("强制用户下线失败, userId: {}", userId, e);
            return R.error("强制下线失败: " + e.getMessage());
        }
    }

    /**
     * 批量强制用户下线
     * 
     * @param userIds 用户ID列表
     * @return 操作结果
     */
    @PostMapping("/batch-force-logout")
    @PreAuthorize("hasAuthority('sys:user:manage')")
    public R<Map<String, Object>> batchForceLogout(@RequestBody List<String> userIds) {
        try {
            if (userIds == null || userIds.isEmpty()) {
                return R.error("用户ID列表不能为空");
            }
            
            int successCount = onlineUserService.batchForceLogout(userIds);
            
            Map<String, Object> result = Map.of(
                    "totalCount", userIds.size(),
                    "successCount", successCount,
                    "failedCount", userIds.size() - successCount
            );
            
            log.info("批量强制用户下线完成, 总数: {}, 成功: {}", userIds.size(), successCount);
            return R.success(result);
        } catch (Exception e) {
            log.error("批量强制用户下线失败", e);
            return R.error("批量强制下线失败: " + e.getMessage());
        }
    }

    /**
     * 清理过期的Token
     * 
     * @return 清理结果
     */
    @PostMapping("/clean-expired")
    @PreAuthorize("hasAuthority('sys:user:manage')")
    public R<Map<String, Object>> cleanExpiredTokens() {
        try {
            int cleanedCount = onlineUserService.cleanExpiredTokens();
            
            Map<String, Object> result = Map.of(
                    "cleanedCount", cleanedCount,
                    "cleanTime", System.currentTimeMillis()
            );
            
            log.info("清理过期Token完成, 清理数量: {}", cleanedCount);
            return R.success(result);
        } catch (Exception e) {
            log.error("清理过期Token失败", e);
            return R.error("清理过期Token失败: " + e.getMessage());
        }
    }

    /**
     * 获取在线用户详细信息
     * 
     * @param userId 用户ID
     * @return 用户详细信息
     */
    @GetMapping("/detail/{userId}")
    @PreAuthorize("hasAuthority('sys:user:view')")
    public R<OnlineUserOutput> getOnlineUserDetail(@PathVariable String userId) {
        try {
            OnlineUserOutput userInfo = onlineUserService.getOnlineUserInfo(userId);
            
            if (userInfo != null) {
                return R.success(userInfo);
            } else {
                return R.error("用户不在线或不存在");
            }
        } catch (Exception e) {
            log.error("获取在线用户详细信息失败, userId: {}", userId, e);
            return R.error("获取用户详细信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取在线用户实时统计（用于仪表板）
     * 
     * @return 实时统计信息
     */
    @GetMapping("/dashboard")
    @PreAuthorize("hasAuthority('sys:user:view')")
    public R<Map<String, Object>> getDashboardStatistics() {
        try {
            List<OnlineUserOutput> onlineUsers = onlineUserService.getOnlineUsers();
            
            // 计算各种统计信息
            long totalOnline = onlineUsers.size();
            long recentLogin = onlineUsers.stream()
                    .filter(user -> user.getOnlineDuration() != null && user.getOnlineDuration() <= 30)
                    .count();
            
            long longTimeOnline = onlineUsers.stream()
                    .filter(user -> user.getOnlineDuration() != null && user.getOnlineDuration() > 480) // 8小时
                    .count();
            
            long expiringSoon = onlineUsers.stream()
                    .filter(user -> user.getRemainingTime() != null && user.getRemainingTime() <= 60) // 1小时内过期
                    .count();
            
            Map<String, Object> dashboard = Map.of(
                    "totalOnline", totalOnline,
                    "recentLogin", recentLogin,
                    "longTimeOnline", longTimeOnline,
                    "expiringSoon", expiringSoon,
                    "updateTime", System.currentTimeMillis(),
                    "recentUsers", onlineUsers.stream()
                            .sorted((u1, u2) -> u2.getLoginTime().compareTo(u1.getLoginTime()))
                            .limit(5)
                            .toList()
            );
            
            return R.success(dashboard);
        } catch (Exception e) {
            log.error("获取仪表板统计失败", e);
            return R.error("获取仪表板统计失败: " + e.getMessage());
        }
    }
}
