package com.ruijie.nse.cloud.sys.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruijie.nse.cloud.repository.entity.Menu;
import com.ruijie.nse.cloud.repository.entity.User;
import com.ruijie.nse.cloud.sys.dto.input.UserInput;
import com.ruijie.nse.cloud.sys.dto.login.LoginDto;
import com.ruijie.nse.cloud.sys.dto.output.UserInfoOutput;
import com.ruijie.nse.cloud.sys.dto.output.UserOutput;
import com.ruijie.nse.cloud.sys.service.UserService;
import com.ruijie.nse.common.constant.CacheConstants;
import com.ruijie.nse.common.dto.PageInput;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.dto.R;
import com.ruijie.nse.common.service.cache.EhcacheService;
import com.ruijie.nse.common.utils.security.SecurityUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.hutool.core.bean.BeanUtil;
import org.dromara.hutool.core.collection.CollUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private EhcacheService ehcacheService;

    @DeleteMapping("/logout")
    public R<LoginDto.Resp> logout(HttpServletRequest request, HttpServletResponse response) {
        // 从缓存中删除jwt令牌
        ehcacheService.evict(CacheConstants.JWT_TOKEN_CACHE, SecurityUtils.getUserId());
        // 执行注销操作
        new SecurityContextLogoutHandler().logout(request, response, SecurityContextHolder.getContext().getAuthentication());
        // 清除认证信息
        SecurityContextHolder.clearContext();
        return R.success();
    }

    /**
     * 获取当前用户信息
     * @return
     */
    @GetMapping("me")
    public R<UserInfoOutput> me() {
        return R.success(userService.me());
    }

    /**
     * 获取用户分页数据
     * @param pageInput
     * @param account
     * @param name
     * @param phone
     * @return
     */
    @GetMapping("page")
    public R<PageOutput<UserOutput>> page(PageInput pageInput, String account, String name, String phone, String userType) {
        return R.success(userService.findByPage(pageInput, account, name, phone, userType));
    }

    /**
     * 保存用户信息
     *
     * @param userInput 用户实体对象
     */
    @PostMapping
    public R<Void> save(@RequestBody UserInput userInput) {
        userService.saveOrUpdate(userInput);
        return R.success();
    }

    /**
     * 根据用户账号获取用户信息
     *
     * @param account 用户账号
     * @return 返回用户对象
     */
    @GetMapping("/account/{account}")
    public R<UserOutput> getByAccount(@PathVariable String account) {
        List<User> users = userService.list(Wrappers.lambdaQuery(User.class).eq(User::getAccount, account));
        if (CollUtil.isNotEmpty(users)) {
            return R.success(BeanUtil.toBean(users.get(0), UserOutput.class));
        }
        return R.success();
    }

    /**
     * 重置密码
     *
     * @param userInput 用户输入对象
     */
    @PostMapping("/resetPassword")
    public R<Void> resetPassword(@RequestBody UserInput userInput) {
        userService.resetPassword(userInput);
        return R.success();
    }

    /**
     * 批量设置失效时间
     *
     * @param userInput 用户输入对象
     */
    @PostMapping("/batchSetValidDate")
    public R<Void> batchSetValidDate(@RequestBody UserInput userInput) {
        if (CollUtil.isEmpty(userInput.getIds())) {
            return R.error("请选择要设置的用户");
        }
        userService.update(Wrappers.lambdaUpdate(User.class)
                .set(User::getValidDate, userInput.getValidDate())
                .in(User::getId, userInput.getIds()));
        return R.success();
    }

    /**
     * 获取所有用户列表
     *
     * @return 返回用户列表数据
     */
    @GetMapping
    public R<List<User>> list() {
        return R.success(userService.list());
    }

    /**
     * 根据用户 ID 获取用户信息
     *
     * @param id 用户唯一标识
     * @return 返回用户对象
     */
    @GetMapping("/{id}")
    public R<User> get(@PathVariable String id) {
        return R.success(userService.getById(id));
    }

    /**
     * 删除指定 ID 的用户
     *
     * @param id 用户唯一标识
     */
    @DeleteMapping("/{id}")
    public R<Void> delete(@PathVariable String id) {
        userService.removeById(id);
        return R.success();
    }

    /**
     * 批量删除
     *
     * @param id 用户唯一标识
     */
    @DeleteMapping("/batch")
    public R<Void> delete(@RequestBody List<String> id) {
        userService.removeByIds(id);
        return R.success();
    }

    /**
     * 获取指定用户的关联角色列表
     *
     * @param userId 用户唯一标识
     * @return 返回角色 ID 列表
     */
    @GetMapping("/role/{userId}")
    public R<List<String>> getUserRoles(@PathVariable String userId) {
        return R.success(userService.listRolesByUserId(userId));
    }
    /**
     * 给指定用户分配角色
     *
     * @param userId  用户唯一标识
     * @param roleIds 角色 ID 列表
     */
    @PostMapping("/role/{userId}")
    public R<Void> saveUserRoles(@PathVariable String userId, @RequestBody List<String> roleIds) {
        userService.assignRole(userId, roleIds);
        return R.success();
    }

    /**
     * 获取指定用户的菜单权限列表
     *
     * @param userId 用户唯一标识
     * @return 返回菜单对象列表
     */
    @GetMapping("/menu/{userId}")
    public R<List<Menu>> getUserMenus(@PathVariable String userId) {
        return R.success(userService.findMenusByUserId(userId));
    }

}
