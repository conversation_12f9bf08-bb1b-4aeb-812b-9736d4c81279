package com.ruijie.nse.cloud.sys.dto.input;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import java.util.List;

/**
 * 菜单表单对象
 */
@Data
public class MenuFormInput {

    /**
     * 菜单ID
     */
    private String id;

    /**
     * 父菜单ID
     */
    private String parentId;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 菜单类型（1-菜单 2-目录 3-外链 4-按钮）
     */
    private Integer type;

    /**
     * 路由名称
     */
    private String routeName;

    /**
     * 路由路径
     */
    private String routePath;

    /**
     * 组件路径(vue页面完整路径，省略.vue后缀)
     */
    private String component;

    /**
     * 权限标识
     */
    private String perm;

    /**
     * 显示状态(1:显示;0:隐藏)
     */
    @Range(max = 1, min = 0, message = "显示状态不正确")
    private Integer visible;

    /**
     * 排序(数字越小排名越靠前)
     */
    private Integer sort;

    /**
     * 菜单图标
     */
    private String icon;

    /**
     * 跳转路径
     */
    private String redirect;

    /**
     * 【菜单】是否开启页面缓存
     * 示例: 1
     */
    private Integer keepAlive;

    /**
     * 【目录】只有一个子路由是否始终显示
     * 示例: 1
     */
    private Integer alwaysShow;

    /**
     * 路由参数
     */
    private List<KeyValue> params;

    @Data
    @NoArgsConstructor
    public static class KeyValue {

        public KeyValue(String key, String value) {
            this.key = key;
            this.value = value;
        }

        /**
         * 选项的值
         */
        private String key;

        /**
         * 选项的标签
         */
        private String value;

    }
}
