package com.ruijie.nse.cloud.sys.dto.input;

import com.ruijie.nse.cloud.repository.entity.enums.UserType;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 用户保存输入
 */
@Data
public class UserInput {

    /**
     * 主键
     */
    private String id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 登录i账号
     */
    private String account;

    /**
     * 手机
     */
    private String mobilePhone;

    /**
     * 用户类型
     */
    private UserType userType;

    /**
     * 班级
     */
    private String classes;

    /**
     * 有效期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date validDate;

    /**
     * 主键列表
     */
    private List<String> ids;
}
