package com.ruijie.nse.cloud.sys.dto.login;

import lombok.*;

@Data
public class LoginDto {


    @Getter
    @Setter
    public static class Req {
        private String account;
        private String password;
    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class Resp {

        /**
         * 访问令牌
         */
        private String accessToken;

        /**
         * 刷新令牌
         */
        private String refreshToken;

        /**
         * 令牌类型
         */
        private String tokenType = "Bearer";

        /**
         * 过期时间（秒）
         */
        private Long expiresIn;

        /**
         * 用户信息
         */
        private UserInfo userInfo;

        /**
         * 用户信息内部类
         */
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class UserInfo {

            /**
             * 用户ID
             */
            private String userId;

            /**
             * 用户名
             */
            private String username;

        }
    }

}
