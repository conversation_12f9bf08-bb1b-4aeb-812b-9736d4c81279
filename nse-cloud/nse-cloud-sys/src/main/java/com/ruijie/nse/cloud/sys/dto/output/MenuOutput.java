package com.ruijie.nse.cloud.sys.dto.output;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

/**
 * 路由输出
 */
@Data
public class MenuOutput {

    /**
     * 菜单ID
     */
    private String id;

    /**
     * 父菜单ID
     */
    private String parentId;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 菜单类型
     */
    private Integer type;

    /**
     * 路由名称
     */
    private String routeName;

    /**
     * 路由路径
     */
    private String routePath;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 菜单排序(数字越小排名越靠前)
     */
    private Integer sort;

    /**
     * 菜单是否可见(1:显示;0:隐藏)
     */
    private Integer visible;

    /**
     * ICON
     */
    private String icon;

    /**
     * 跳转路径
     */
    private String redirect;

    /**
     * 按钮权限标识
     */
    private String perm;

    /**
     * 子菜单
     */
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    private List<MenuOutput> children;

}
