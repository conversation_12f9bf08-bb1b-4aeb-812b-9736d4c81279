package com.ruijie.nse.cloud.sys.dto.output;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 路由输出
 */
@Data
@Accessors(chain = true)
public class RouteOutput {

    /**
     * 路由路径
     * 示例: user
     */
    private String path;

    /**
     * 组件路径
     * 示例: system/user/index
     */
    private String component;

    /**
     * 跳转链接
     * 示例: https://www.youlai.tech
     */
    private String redirect;

    /**
     * 路由名称
     */
    private String name;

    /**
     * 路由属性
     */
    private Meta meta;

    /**
     * 路由属性类型
     */
    @Data
    public static class Meta {

        /**
         * 路由title
         */
        private String title;

        /**
         * ICON
         */
        private String icon;

        /**
         * 是否隐藏(true-是 false-否)
         * 示例: true
         */
        private Boolean hidden;

        /**
         * 【菜单】是否开启页面缓存
         * 示例: true
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Boolean keepAlive;

        /**
         * 【目录】只有一个子路由是否始终显示
         * 示例: true
         */
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Boolean alwaysShow;

        /**
         * 路由参数
         */
        private Map<String,String> params;
    }

    /**
     * 子路由列表
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<RouteOutput> children;

}
