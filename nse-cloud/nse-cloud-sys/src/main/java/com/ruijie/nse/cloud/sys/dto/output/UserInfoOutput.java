package com.ruijie.nse.cloud.sys.dto.output;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 用户信息
 */
@Data
@Accessors(chain = true)
public class UserInfoOutput {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 姓名
     */
    private String nickName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 权限
     */
    private List<String> perms;

    /**
     * 角色
     */
    private List<String> roles;

}
