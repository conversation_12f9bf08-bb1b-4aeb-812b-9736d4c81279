package com.ruijie.nse.cloud.sys.service;


import com.ruijie.nse.cloud.sys.dto.login.LoginDto;
import com.ruijie.nse.common.config.security.UserPrincipal;
import com.ruijie.nse.common.constant.CacheConstants;
import com.ruijie.nse.common.constant.CommonConstant;
import com.ruijie.nse.common.exception.BusinessException;
import com.ruijie.nse.common.service.cache.EhcacheService;
import com.ruijie.nse.common.utils.enctry.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.text.StrValidator;
import org.dromara.hutool.core.xml.XmlUtil;
import org.dromara.hutool.http.HttpUtil;
import org.dromara.hutool.json.JSONObject;
import org.dromara.hutool.json.JSONUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;


/**
 * 登录服务
 * <AUTHOR>
 * @date 2025-07-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LoginService {

    private final AuthenticationManager authenticationManager;
    private final EhcacheService ehcacheService;


    @Value("${app-base-url:http://oa.ruijie.com.cn}")
    private String appBaseUrl;

    @Value("${sid-base-url:https://sid.ruijie.com.cn}")
    private String sidBaseUrl;


    /**
     * 账号密码登录
     * @param account
     * @param password
     * @return
     */
    public LoginDto.Resp loginByAccount(String account, String password) {
        log.info("用户登录: {}", account);

        try {
            // 创建认证令牌
            UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                    account, password
            );

            // 进行认证
            Authentication authentication = authenticationManager.authenticate(authToken);

            // 获取用户信息
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

            // 生成JWT令牌
            String accessToken = JwtUtil.generateAccessToken(
                    userPrincipal.getUserId(),
                    userPrincipal.getUsername(),
                    userPrincipal.getAccount(),
                    userPrincipal.getAuthoritiesString()
            );

            String refreshToken = JwtUtil.generateRefreshToken(
                    userPrincipal.getUserId(),
                    userPrincipal.getUsername(),
                    userPrincipal.getAccount()
            );

            // 构建登录结果
            LoginDto.Resp.UserInfo userInfo = LoginDto.Resp.UserInfo.builder()
                    .userId(userPrincipal.getUserId())
                    .username(userPrincipal.getUsername())
                    .build();

            LoginDto.Resp result = LoginDto.Resp.builder()
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .tokenType(CommonConstant.Jwt.TOKEN_TYPE)
                    .expiresIn(CommonConstant.Jwt.EXPIRATION / 1000)
                    .userInfo(userInfo)
                    .build();

            // 将token存储到Ehcache
            ehcacheService.put(CacheConstants.JWT_TOKEN_CACHE, userPrincipal.getUserId(), accessToken);

            log.info("用户登录成功: {}", result.toString());
            return result;

        } catch (BadCredentialsException e) {
            log.warn("用户登录失败，用户名或密码错误: {}", account);
            throw new RuntimeException("用户名或密码错误");
        } catch (Exception e) {
            log.error("用户登录失败: {}, 错误: {}", account, e.getMessage(), e);
            throw new RuntimeException("登录失败: " + e.getMessage());
        }
    }

    /**
     * CAS登录
     * @param body
     * @return
     */
    public LoginDto.Resp loginByCasTicket(String body) {
        try {
            JSONObject json = JSONUtil.parseObj(body);
            String ticket = json.getStr("ticket");
            String account = getOaUserInfo(ticket);
            if (StrValidator.isBlank(account)) {
                return null;
            }

            // 创建认证令牌
            UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                    account, "cas-sid-ruijie@123"
            );

            // 进行认证
            Authentication authentication = authenticationManager.authenticate(authToken);

            // 获取用户信息
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

            // 生成JWT令牌
            String accessToken = JwtUtil.generateAccessToken(
                    userPrincipal.getUserId(),
                    userPrincipal.getUsername(),
                    userPrincipal.getAccount(),
                    userPrincipal.getAuthoritiesString()
            );

            String refreshToken = JwtUtil.generateRefreshToken(
                    userPrincipal.getUserId(),
                    userPrincipal.getUsername(),
                    userPrincipal.getAccount()
            );

            // 构建登录结果
            LoginDto.Resp.UserInfo userInfo = LoginDto.Resp.UserInfo.builder()
                    .userId(userPrincipal.getUserId())
                    .username(userPrincipal.getUsername())
                    .build();

            LoginDto.Resp result = LoginDto.Resp.builder()
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .tokenType(CommonConstant.Jwt.TOKEN_TYPE)
                    .expiresIn(CommonConstant.Jwt.EXPIRATION / 1000)
                    .userInfo(userInfo)
                    .build();

            // 将token存储到Ehcache
            ehcacheService.put(CacheConstants.JWT_TOKEN_CACHE, userPrincipal.getUserId(), accessToken);

            log.info("用户登录成功: {}", result.toString());
            return result;

        } catch (UsernameNotFoundException e) {
            return new LoginDto.Resp();
        } catch (Exception e) {
            log.error("OA方式登录失败: {}", e.getMessage(), e);
            throw BusinessException.errorByMessage("登录失败: " + e.getMessage());
        }
    }

    /**
     * 获取OA用户信息
     * @param ticket
     * @return
     */
    public String getOaUserInfo(String ticket) {
        String url = String.format("%s/p3/serviceValidate?ticket=%s&service=%s", sidBaseUrl, ticket,
                URLEncoder.encode(appBaseUrl, StandardCharsets.UTF_8));
        String xmlContent = HttpUtil.get(url);
        // 解析XML响应，提取cas:user的值
        if (StrValidator.isNotBlank(xmlContent)) {
            Document root = XmlUtil.parseXml(xmlContent);
            // 查找cas:authenticationSuccess节点
            NodeList successNodes = root.getElementsByTagName("cas:authenticationSuccess");
            if (successNodes.getLength() > 0) {
                Element successElement = (Element) successNodes.item(0);

                // 查找cas:user节点
                NodeList userNodes = successElement.getElementsByTagName("cas:user");
                if (userNodes.getLength() > 0) {
                    return userNodes.item(0).getTextContent();
                }
            }
        }
        return null;
    }
}
