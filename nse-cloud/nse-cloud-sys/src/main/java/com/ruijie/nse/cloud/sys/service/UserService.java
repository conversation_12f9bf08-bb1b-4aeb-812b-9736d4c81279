package com.ruijie.nse.cloud.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruijie.nse.cloud.repository.entity.Menu;
import com.ruijie.nse.cloud.repository.entity.User;
import com.ruijie.nse.cloud.repository.entity.enums.UserStatus;
import com.ruijie.nse.cloud.repository.mapper.MenuDao;
import com.ruijie.nse.cloud.repository.mapper.RoleDao;
import com.ruijie.nse.cloud.repository.mapper.RoleMenuDao;
import com.ruijie.nse.cloud.repository.mapper.UserDao;
import com.ruijie.nse.cloud.sys.dto.input.UserInput;
import com.ruijie.nse.cloud.sys.dto.output.UserInfoOutput;
import com.ruijie.nse.cloud.sys.dto.output.UserOutput;
import com.ruijie.nse.common.constant.CacheConstants;
import com.ruijie.nse.common.constant.CommonConstant;
import com.ruijie.nse.common.dto.PageInput;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.exception.BusinessException;
import com.ruijie.nse.common.utils.enctry.PasswordUtil;
import com.ruijie.nse.common.utils.security.SecurityUtils;
import jakarta.annotation.Resource;
import org.dromara.hutool.core.bean.BeanUtil;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.dromara.hutool.core.text.StrValidator;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <p>
 * $!{table.comment} 服务类
 * </p>
 *
 * <AUTHOR>
 * @date 2018-08-14
 */
@Service
@CacheConfig(cacheNames = CacheConstants.SERVICE_CACHE)
public class UserService extends ServiceImpl<UserDao, User> {

    @Resource
    private MenuDao menuDao;
    @Resource
    private RoleMenuDao roleMenuDao;
    @Resource
    private RoleDao roleDao;

    /**
     * 获取当前登录用户信息
     * @return
     */
    public UserInfoOutput me() {
        String userId = SecurityUtils.getUserId();
        User user = baseMapper.selectById(userId);
        return new UserInfoOutput()
                .setUserId(userId)
                .setUsername(user.getAccount())
                .setNickName(user.getName())
                .setAvatar(user.getAvatar())
                .setPerms(listPermissionsByUserId(userId))
                .setRoles(listRolesByUserId(userId));
    }

    /**
     * 分页查询用户
     * @param pageInput
     * @param account
     * @param name
     * @param phone
     * @return
     */
    public PageOutput<UserOutput> findByPage(PageInput pageInput, String account, String name, String phone, String userType) {
        LambdaQueryWrapper<User> wrapper = Wrappers.lambdaQuery(User.class)
                .like(StrValidator.isNotBlank(account), User::getAccount, account)
                .like(StrValidator.isNotBlank(name), User::getName, name)
                .like(StrValidator.isNotBlank(phone), User::getMobilePhone, phone)
                .eq(StrValidator.isNotBlank(userType), User::getUserType, userType)
                .orderByDesc(User::getCreatedDate);
        Page<User> userPage = baseMapper.selectPage(new Page<>(pageInput.getPageNumber(), pageInput.getPageSize()), wrapper);
        List<User> records = userPage.getRecords();
        return new PageOutput<>(userPage.getTotal(), BeanUtil.copyToList(records, UserOutput.class));
    }


    /**
     * 保存或更新用户
     * @param userInput
     * @return
     */
    public void saveOrUpdate(UserInput userInput) {
        User user = BeanUtil.toBean(userInput, User.class);
        if (userInput.getId() == null) {
            String salt = PasswordUtil.generateSalt();
            user.setSalt(salt);
            user.setPassword(PasswordUtil.encodePassword(CommonConstant.System.DEFAULT_PASSWORD, salt));
            user.setStatus(UserStatus.OK);
            baseMapper.insert(user);
            return;
        }
        baseMapper.updateById(user);
    }

    /**
     * 重置密码
     * @param userInput
     */
    public void resetPassword(UserInput userInput) {
        User user = baseMapper.selectById(userInput.getId());
        if (user == null) {
            throw BusinessException.error(CharSequenceUtil.EMPTY, "用户不存在");
        }
        user.setPassword(PasswordUtil.encodePassword(CommonConstant.System.DEFAULT_PASSWORD, user.getSalt()));
        baseMapper.updateById(user);
    }

    /**
     * 根据用户id获取权限。
     * 这里整合了超级管理员账号和其它账号
     *
     * @param userId
     * @return
     */
    public List<String> listPermissionsByUserId(String userId) {
        List<String> permissionList;
        List<String> roles = listRolesByUserId(userId);
        // 若是超级管理员，则直接有所有权限，否则从数据库中取权限
        if (roles.contains(CommonConstant.System.SUPER_ADMIN)) {
            permissionList = menuDao.selectList(Wrappers.lambdaQuery(Menu.class).isNotNull(Menu::getPerm).orderByAsc(Menu::getSort))
                    .stream()
                    .map(Menu::getPerm)
                    .toList();
        } else {
            permissionList = baseMapper.listPermissionsByUserId(userId);
        }
        return permissionList;
    }

    /**
     * 根据用户id获取角色ID。
     *
     * @param userId
     * @return
     */
    public List<String> listRolesByUserId(String userId) {
        return baseMapper.listRolesByUserId(userId);
    }

    /**
     * 分配角色
     * @param userId
     * @param roleList
     */
    public void assignRole(String userId, List<String> roleList) {
        // 清除现有的
        baseMapper.deleteRolesOfUser(userId);
        // 添加新的
        baseMapper.insertRolesToUser(roleList, userId);
    }

    /**
     * 根据用户id获取菜单。
     * @param userId
     * @return
     */
    public List<Menu> findMenusByUserId(String userId) {
        // 若是超级管理员，则直接有所有权限，否则从数据库中取权限
        List<String> roles = listRolesByUserId(userId);
        if (roles.contains(CommonConstant.System.SUPER_ADMIN)) {
            return menuDao.selectList(Wrappers.lambdaQuery(Menu.class).orderByAsc(Menu::getSort));
        }
        return menuDao.selectMenusByUserId(userId);
    }

}
