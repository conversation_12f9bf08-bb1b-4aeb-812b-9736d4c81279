#!/bin/bash


# ==================== nse_server.conf ==============
config_nse_server_conf() {
    if [ "$EUID" -ne 0 ]; then
        error_exit "请使用 root  用户运行此脚本"
    fi

    log "配置 nse_server.conf..."
    mkdir -p /root/.config/NSE/2.2
cat <<EOFC > /root/.config/NSE/2.2/nse_server.conf
[Server]
host = 0.0.0.0
certfile=/root/.config/NSE/2.2/ssl/server.cert
certkey=/root/.config/NSE/2.2/ssl/server.key
ssl=True

[Qemu]
enable_kvm = false
EOFC
}

create_ssl() {
    if [ "$EUID" -ne 0 ]; then
        error_exit "请使用 root  用户运行此脚本"
    fi
  mkdir -p /root/.config/NSE/2.2/ssl
  DST_DIR="$HOME/.config/NSE/2.2/ssl"
  OLD_DIR=`pwd`

  fail_if_error() {
    [ $1 != 0 ] && {
      unset PASSPHRASE
      cd $OLD_DIR
      exit 10
    }
  }


  mkdir -p $DST_DIR
  fail_if_error $?
  cd $DST_DIR

  SUBJ="/C=US/ST=Texas/O=NSESELF/localityName=Austin/commonName=localhost/organizationalUnitName=RuijieNseServer/emailAddress=<EMAIL>"

  openssl req -nodes -new -x509 -keyout server.key -out server.cert -subj "$SUBJ"
}

# ==================== 主执行流程 ====================
main() {
    config_nse_server_conf
    create_ssl

    success "NSE部署完成！"
}

# 执行主函数
main "$@"