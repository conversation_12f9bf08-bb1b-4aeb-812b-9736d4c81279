package com.ruijie.nse.mgr.common.aspect;

import com.ruijie.nse.mgr.common.annotation.AuditLog;
import com.ruijie.nse.mgr.repository.mapper.AuditLogDao;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.dromara.hutool.core.date.DateTime;
import org.dromara.hutool.core.date.DateUnit;
import org.dromara.hutool.core.date.DateUtil;
import org.dromara.hutool.core.text.StrValidator;
import org.dromara.hutool.json.JSONUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 日志切面
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class AuditLogAspect {

    private final HttpServletRequest request;

    private final AuditLogDao auditLogDao;

    /**
     * 切点
     */
    @Pointcut("@annotation(com.ruijie.nse.mgr.common.annotation.AuditLog)")
    public void logPointcut() {
    }

    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @Around("logPointcut() && @annotation(logAnnotation)")
    public Object doAround(ProceedingJoinPoint joinPoint, AuditLog logAnnotation) throws Throwable {
        DateTime now = DateUtil.now();
        Object result = null;
        Exception exception = null;

        try {
            result = joinPoint.proceed();
        } catch (Exception e) {
            exception = e;
            throw e;
        } finally {
            long executionTime = DateUtil.between(now, DateUtil.now(), DateUnit.MS);
            this.saveLog(joinPoint, exception, result, executionTime, logAnnotation);
        }
        return result;
    }


    /**
     * 保存日志
     *
     * @param joinPoint     切点
     * @param e             异常
     * @param jsonResult    响应结果
     * @param executionTime 执行时长
     * @param logAnnotation 日志注解
     */
    private void saveLog(final JoinPoint joinPoint, final Exception e, Object jsonResult, long executionTime, AuditLog logAnnotation) {
        try {
            String requestBody = getRequestParameters(joinPoint);
            String responseContent = e != null ? JSONUtil.toJsonStr(e.getStackTrace()) : JSONUtil.toJsonStr(jsonResult);
            String methodName = request.getMethod();
            String ipAddress = getIpAddr(request);
            String requestURI = request.getRequestURI();
            // 获取所有的请求头
            Map<String, String> headers = Collections.list(request.getHeaderNames())
                    .stream()
                    .collect(Collectors.toMap(h -> h, request::getHeader));
            // String mime = request.getHeader("Content-Type");
            com.ruijie.nse.mgr.repository.entity.AuditLog auditLog = new com.ruijie.nse.mgr.repository.entity.AuditLog();
            auditLog.setUrl(requestURI)
                    .setIp(ipAddress)
                    .setRequestBody(requestBody)
                    .setResponseBody(responseContent)
                    .setRequestHeaders(JSONUtil.toJsonStr(headers))
                    .setMethodName(methodName)
                    .setElapsedMilliSeconds((int)executionTime)
                    .setAuditTitle(logAnnotation.title());
            Optional.ofNullable(logAnnotation.module()).ifPresent(logModuleEnum -> auditLog.setModule(logModuleEnum.name()));
            auditLogDao.insert(auditLog);
            // 打印所有参数
            log.info("请求来源IP：{}， 请求地址：{}, 请求方式：{}， 请求参数： {}， 响应结果：{}，执行时间：{}", ipAddress, requestURI, methodName, requestBody, responseContent, executionTime);
        } catch (Exception ex) {
            log.error("保存审计日志失败: {}", ex.getMessage(), ex);
        }
    }


    /**
     * 获取IP地址
     *
     * @param request HttpServletRequest对象
     * @return 客户端IP地址
     */
    public static String getIpAddr(HttpServletRequest request) {
        String ip = null;
        try {
            if (request == null) {
                return "";
            }
            ip = request.getHeader("x-forwarded-for");
            if (checkIp(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (checkIp(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (checkIp(ip)) {
                ip = request.getHeader("HTTP_CLIENT_IP");
            }
            if (checkIp(ip)) {
                ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            }
            if (checkIp(ip)) {
                ip = request.getRemoteAddr();
                if ("127.0.0.1".equals(ip) || "0:0:0:0:0:0:0:1".equals(ip)) {
                    // 根据网卡取本机配置的IP
                    ip = getLocalAddr();
                }
            }
        } catch (Exception e) {
            log.error("IPUtils ERROR, {}", e.getMessage());
        }

        // 使用代理，则获取第一个IP地址
        if (StrValidator.isNotBlank(ip) && ip.indexOf(",") > 0) {
            ip = ip.substring(0, ip.indexOf(","));
        }

        return ip;
    }


    private static boolean checkIp(String ip) {
        String unknown = "unknown";
        return StrValidator.isEmpty(ip) || unknown.equalsIgnoreCase(ip);
    }

    /**
     * 获取本机的IP地址
     *
     * @return 本机IP地址
     */
    private static String getLocalAddr() {
        try {
            return InetAddress.getLocalHost().getHostAddress();
        } catch (UnknownHostException e) {
            log.error("InetAddress.getLocalHost()-error, {}", e.getMessage());
        }
        return null;
    }

    /**
     * 设置请求参数到日志对象中
     *
     * @param joinPoint 切点
     */
    private String getRequestParameters(JoinPoint joinPoint) {
        Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
        Parameter[] parameters = method.getParameters();
        Object[] args = joinPoint.getArgs();
        Map<String, Object> paramMap = new LinkedHashMap<>();
        for (int i = 0; i < parameters.length; i++) {
            String name = parameters[i].getName();
            Object arg = args[i];
            if (shouldFilterObject(arg)) {
                continue;
            }
            paramMap.put(name, arg);
        }
        return JSONUtil.toJsonStr(paramMap);
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param obj 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    private boolean shouldFilterObject(Object obj) {
        if (obj == null) {
            return true;
        }
        Class<?> clazz = obj.getClass();
        if (clazz.isArray()) {
            return MultipartFile.class.isAssignableFrom(clazz.getComponentType());
        } else if (Collection.class.isAssignableFrom(clazz)) {
            Collection<?> collection = (Collection<?>) obj;
            return collection.stream().anyMatch(MultipartFile.class::isInstance);
        } else if (Map.class.isAssignableFrom(clazz)) {
            Map<?, ?> map = (Map<?, ?>) obj;
            return map.values().stream().anyMatch(MultipartFile.class::isInstance);
        }
        return obj instanceof MultipartFile || obj instanceof HttpServletRequest || obj instanceof HttpServletResponse;
    }

}
