package com.ruijie.nse.mgr.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 防抖配置属性
 */
@Data
@Component
@ConfigurationProperties(prefix = "debounce")
public class DebounceProperties {
    
    /**
     * 是否启用防抖功能
     */
    private boolean enabled = true;
    
    /**
     * 防抖时间窗口（毫秒），默认500ms
     */
    private long timeWindow = 500L;
    
    /**
     * 需要排除的路径模式
     */
    private List<String> excludePatterns = new ArrayList<>();
}