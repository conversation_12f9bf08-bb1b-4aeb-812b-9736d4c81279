package com.ruijie.nse.mgr.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Spring Integration事件处理配置属性
 *
 * <AUTHOR> Team
 */
@Data
@Component
@ConfigurationProperties(prefix = "nse.event.integration")
public class EventIntegrationProperties {

    /**
     * 是否启用事件集成
     */
    private boolean enabled = true;

    /**
     * 队列配置
     */
    private Queue queue = new Queue();

    /**
     * 重试配置
     */
    private Retry retry = new Retry();

    /**
     * 错误处理配置
     */
    private ErrorHandling errorHandling = new ErrorHandling();

    @Data
    public static class Queue {
        /**
         * 队列容量
         */
        private int capacity = 100;

        /**
         * 队列类型 (direct, queue, publish-subscribe)
         */
        private String type = "queue";
    }

    @Data
    public static class Retry {
        /**
         * 是否启用重试
         */
        private boolean enabled = true;

        /**
         * 最大重试次数
         */
        private int maxAttempts = 3;

        /**
         * 重试间隔（毫秒）
         */
        private long interval = 1000;

        /**
         * 重试间隔倍数
         */
        private double multiplier = 2.0;
    }

    @Data
    public static class ErrorHandling {
        /**
         * 是否启用错误处理
         */
        private boolean enabled = true;

        /**
         * 错误日志级别
         */
        private String logLevel = "ERROR";

        /**
         * 是否发送错误通知
         */
        private boolean sendNotification = false;
    }
}