package com.ruijie.nse.mgr.common.constants;

import java.nio.file.Path;

public interface CommonConstants {

    interface Paths {

        /**
         * workspace路径
         */
        Path WORKSPACE_PATH = Path.of(System.getProperty("user.dir"), "workspace");

        /**
         * nse项目路径
         */
        Path NSE_STORAGE_PATH = WORKSPACE_PATH.resolve("nse-storage");

        /**
         * 课程库路径
         */
        Path COURSE_REPO_PATH = NSE_STORAGE_PATH.resolve("course-repo");

        /**
         * 学生练习时长持久化路径
         */
        Path STUDENT_USAGE_DURATION_PATH = NSE_STORAGE_PATH.resolve("student-usage-duration");
    }

}
