package com.ruijie.nse.mgr.common.context;

import com.ruijie.nse.mgr.common.dto.License;
import lombok.Getter;

public class LicenseContext {

    private static final ThreadLocal<Boolean> IS_VALID_LOCAL = new ThreadLocal<>();
    @Getter
    private static License license = null;

    public static boolean isValid() {
        return IS_VALID_LOCAL.get();
    }

    public static void setValid(boolean valid) {
        IS_VALID_LOCAL.set(valid);
    }

    public static void clear() {
        IS_VALID_LOCAL.remove();
    }


    public static void setLicense(License license) {
        LicenseContext.license = license;
    }
}
