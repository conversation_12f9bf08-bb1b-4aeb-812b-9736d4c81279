package com.ruijie.nse.mgr.common.controller;

import com.ruijie.nse.common.annotation.Anonymous;
import com.ruijie.nse.mgr.common.constants.CommonConstants;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.io.file.FileNameUtil;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 文件控制器
 * 提供通用的文件预览和下载功能
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/file")
@Anonymous
public class FileController {

    /**
     * 通用文件预览接口
     * 支持PDF等文件类型的在线预览
     *
     * @param filePath 本地文件路径
     * @param response HTTP响应对象
     */
    @GetMapping("/preview")
    public void previewFile(@RequestParam("filePath") String filePath,
                            @RequestParam(name = "type", defaultValue = "manual", required = false) String type,
                            HttpServletResponse response) {
        log.info("开始预览文件: {}", filePath);

        try {
            // 检查文件路径是否为空
            if (filePath == null || filePath.trim().isEmpty()) {
                log.warn("文件路径为空");
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }

            File file = new File(filePath);
            if("manual".equals(type)) { // 手册预览
                // 注意：这里应该检查路径安全性
                file = CommonConstants.Paths.COURSE_REPO_PATH.resolve(filePath).toFile();
            }

            // 检查文件是否存在
            if (!file.exists()) {
                log.warn("文件不存在: {}", filePath);
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                return;
            }

            // 检查是否为文件（非目录）
            if (!file.isFile()) {
                log.warn("路径不是文件: {}", filePath);
                response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
                return;
            }

            // 安全检查：防止路径遍历攻击
            String canonicalPath = file.getCanonicalPath();
            String allowedBasePath = CommonConstants.Paths.COURSE_REPO_PATH.toFile().getCanonicalPath();
            if (!canonicalPath.startsWith(allowedBasePath)) {
                log.warn("文件访问超出允许范围: {}", filePath);
                response.setStatus(HttpServletResponse.SC_FORBIDDEN);
                return;
            }

            // 获取文件名和扩展名
            String fileName = file.getName();
            String fileExtension = FileNameUtil.getSuffix(fileName).toLowerCase();

            // 设置响应头
            setResponseHeaders(response, fileName, fileExtension);

            // 读取文件并写入响应流
            try (InputStream inputStream = new FileInputStream(file);
                 ServletOutputStream outputStream = response.getOutputStream()) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();

                log.info("文件预览成功: {}", filePath);
            }

        } catch (IOException e) {
            log.error("文件预览失败: {}", filePath, e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("文件预览失败: " + e.getMessage());
            } catch (IOException ioException) {
                log.error("发送错误响应失败", ioException);
            }
        } catch (Exception e) {
            log.error("文件预览发生未知错误: {}", filePath, e);
            try {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                response.getWriter().write("文件预览发生未知错误: " + e.getMessage());
            } catch (IOException ioException) {
                log.error("发送错误响应失败", ioException);
            }
        }
    }

    private void setResponseHeaders(HttpServletResponse response, String fileName, String fileExtension) {
        try {
            // 根据文件类型设置Content-Type
            String contentType = getContentType(fileExtension);
            response.setContentType(contentType);

            // 设置字符编码
            response.setCharacterEncoding("UTF-8");

            // 设置Content-Disposition为inline，支持浏览器内嵌预览
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "inline; filename*=UTF-8''" + encodedFileName);

            // 设置缓存控制
            response.setHeader(HttpHeaders.CACHE_CONTROL, "public, max-age=3600");

        } catch (Exception e) {
            log.warn("设置响应头失败: {}", fileName, e);
        }
    }

    /**
     * 根据文件扩展名获取Content-Type
     *
     * @param fileExtension 文件扩展名
     * @return Content-Type
     */
    private String getContentType(String fileExtension) {
        return switch (fileExtension) {
            case "pdf" -> "application/pdf";
            case "doc", "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "xls", "xlsx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "ppt", "pptx" -> "application/vnd.openxmlformats-officedocument.presentationml.presentation";
            case "txt" -> "text/plain; charset=UTF-8";
            case "jpg", "jpeg" -> "image/jpeg";
            case "png" -> "image/png";
            case "gif" -> "image/gif";
            default -> "application/octet-stream";
        };
    }

}
