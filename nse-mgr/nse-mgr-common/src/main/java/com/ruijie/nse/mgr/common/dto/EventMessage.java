package com.ruijie.nse.mgr.common.dto;

import com.ruijie.nse.mgr.repository.entity.enums.EventTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 事件消息DTO
 * 用于Spring Integration消息传递
 *
 * <AUTHOR> Team
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventMessage {
    
    /**
     * 事件类型
     */
    private EventTypeEnum eventType;
    
    /**
     * 事件详情
     */
    private String eventDetails;
    
    /**
     * 事件级别
     */
    private String eventLevel;
    
    /**
     * 事件消息
     */
    private String eventMessage;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 消息状态
     */
    private String status;
    
    /**
     * 消息载荷（原始数据）
     */
    private String payload;
}