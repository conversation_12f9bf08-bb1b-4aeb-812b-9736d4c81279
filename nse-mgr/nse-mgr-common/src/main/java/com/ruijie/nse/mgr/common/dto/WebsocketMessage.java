package com.ruijie.nse.mgr.common.dto;

import lombok.Data;

@Data
public class WebsocketMessage {

    /**
     * 事件
     */
    private String event;

    /**
     * 消息
     */
    private String message;

    /**
     * 数据
     */
    private Object data;


    public static WebsocketMessage create(String event) {
        return new WebsocketMessage(event);
    }

    public static WebsocketMessage create(String event, String message) {
        return new WebsocketMessage(event, message);
    }

    public static WebsocketMessage create(String event, Object data) {
        return new WebsocketMessage(event, null, data);
    }

    public static WebsocketMessage create(String event, String message, Object data) {
        return new WebsocketMessage(event, message, data);
    }

    public WebsocketMessage(String event) {
        this(event, null, null);
    }

    public WebsocketMessage(String event, String message) {
        this(event, message, null);
    }

    public WebsocketMessage(String event, String message, Object data) {
        this.event = event;
        this.message = message;
        this.data = data;
    }

}
