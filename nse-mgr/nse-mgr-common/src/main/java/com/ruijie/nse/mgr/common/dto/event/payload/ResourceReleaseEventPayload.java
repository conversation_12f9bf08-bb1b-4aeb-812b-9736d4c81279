package com.ruijie.nse.mgr.common.dto.event.payload;

import lombok.*;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 资源释放事件数据
 * 用于携带资源释放相关的详细信息
 *
 * <AUTHOR> Team
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResourceReleaseEventPayload extends Payload {
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户类型
     */
    private String userType;
    
    /**
     * 会话ID
     */
    private String sessionId;
    
    /**
     * 释放原因
     */
    private String releaseReason;
    
    /**
     * 释放类型：MANUAL(手动), AUTO(自动), FORCE(强制), TIMEOUT(超时)
     */
    private String releaseType;
    

    /**
     * 释放时间
     */
    private LocalDateTime releaseTime;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> extensions;


    /**
     * 创建学生资源释放事件数据
     * @param userId
     * @param userType
     * @param username
     * @param releaseReason
     * @param releaseType MANUAL(手动), AUTO(自动), FORCE(强制), TIMEOUT(超时)
     * @return
     */
    public static ResourceReleaseEventPayload ofEvent(String userId, String userType, String username,
                                                                        String releaseReason, String releaseType) {
        return ResourceReleaseEventPayload.builder()
                .userId(userId)
                .username(username)
                .userType(userType)
                .releaseReason(releaseReason)
                .releaseType(releaseType)
                .releaseTime(LocalDateTime.now())
                .build();
    }
}
