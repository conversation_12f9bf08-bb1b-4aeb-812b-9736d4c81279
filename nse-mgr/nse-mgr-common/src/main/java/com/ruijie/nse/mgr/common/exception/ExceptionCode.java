package com.ruijie.nse.mgr.common.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum ExceptionCode {

    /**
     *
     */
    PARAM_LOST(901, "参数丢失"),
    SECRET_EXPIRE(902, "授权信息无效"),
    TIMESTAMP_EXPIRE(903, "报文过期"),
    REQUEST_NONCE(906, "报文重复请求"),
    VERIFY_FAIL(905, "鉴权失败"),
    JWT_FAIL(907, "JWT token校验失败"),
    JWT_EXPIRE(908, "JWT token过期"),
    REQUEST_DEBOUNCE(909, "请求过于频繁，请稍后再试");


    private final Integer code;
    private final String message;
}
