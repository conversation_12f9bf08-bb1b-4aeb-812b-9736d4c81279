package com.ruijie.nse.mgr.common.handler;

import com.ruijie.nse.mgr.common.dto.event.payload.ResourceReleaseEventPayload;
import com.ruijie.nse.mgr.common.service.EventPublishService;
import com.ruijie.nse.mgr.repository.entity.enums.EventTypeEnum;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.net.URI;
import java.util.Collection;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.*;

@Slf4j
@Component
public class WebSocketHandler extends TextWebSocketHandler {

    @Resource
    private EventPublishService eventPublishService;

    private static final ConcurrentHashMap<String, Set<WebSocketSession>> userSessionMap = new ConcurrentHashMap<>();

    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(10);

    private static final ConcurrentHashMap<String, ScheduledFuture<?>> pendingReleaseTasks = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        URI uri = session.getUri();
        if (Objects.nonNull(uri)) {
            String path = uri.getPath();
            String userId = path.substring(path.lastIndexOf('/') + 1);

            // 如果有正在进行的释放任务，取消它
            ScheduledFuture<?> scheduledTask = pendingReleaseTasks.get(userId);
            if (scheduledTask != null && !scheduledTask.isDone()) {
                scheduledTask.cancel(false);
                pendingReleaseTasks.remove(userId);
                log.info("用户 {} 重新连接，取消资源释放任务", userId);
            }

            userSessionMap.computeIfAbsent(userId, k -> new CopyOnWriteArraySet<>()).add(session);
            log.info("用户连接：userId:" + userId);
        }
    }

    @Override
    public void handleTextMessage(WebSocketSession session, TextMessage message) {
        log.info("收到用户发来的消息：{}", message.getPayload());
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        String path = session.getUri().getPath();
        String userId = path.substring(path.lastIndexOf('/') + 1);
        log.info("用户断开了 {} 连接", userId);
        Set<WebSocketSession> sessions = userSessionMap.get(userId);
        if (CollUtil.isEmpty(sessions)) {
            return;
        }
        sessions.remove(session);
        if (CollUtil.isNotEmpty(sessions)) {
            return;
        }
        userSessionMap.remove(userId);
        // 启动延迟释放任务
        ScheduledFuture<?> scheduledTask = scheduler.schedule(() -> {
            if (!userSessionMap.containsKey(userId)) {
                eventPublishService.publishWithPayload(EventTypeEnum.EVT_PY3_SERVER_RELEASE,
                        "用户断开连接释放资源",
                        "用户断开连接释放资源",
                        ResourceReleaseEventPayload.builder()
                                .userId(userId)
                                .releaseReason("用户断开连接")
                                .sessionId(session.getId())
                                .build()
                );
            }
            pendingReleaseTasks.remove(userId);
        }, 10, TimeUnit.SECONDS);
        pendingReleaseTasks.put(userId, scheduledTask);
    }


    /**
     * 获取连接数
     * @return
     */
    public static int getLinkNum() {
        Collection<Set<WebSocketSession>> values = userSessionMap.values();
        return values.stream().mapToInt(Set::size).sum();
    }

    /**
     * 发送消息给指定用户
     * @param userId
     * @param message
     * @throws Exception
     */
    public static boolean sendMsgToUser(String userId, String message) {
        Set<WebSocketSession> sessions = userSessionMap.get(userId);
        if (sessions == null) {
            return false;
        }
        for (WebSocketSession session : sessions) {
            if (session.isOpen()) {
                try {
                    session.sendMessage(new TextMessage(message));
                } catch (Exception e) {
                    log.error("发送消息给用户失败：{}", userId, e);
                }
            }
        }
        return true;
    }


    /**
     * 发送给所有用户
     * @param message
     * @throws Exception
     */
    public static void sendMsg(String message) {
        Collection<Set<WebSocketSession>> sessionsSet = userSessionMap.values();
        if (sessionsSet.isEmpty()) {
            return;
        }
        for (Set<WebSocketSession> webSocketSessions : sessionsSet) {
            for (WebSocketSession session : webSocketSessions) {
                if (session.isOpen()) {
                    try {
                        session.sendMessage(new TextMessage(message));
                    } catch (Exception e) {
                        log.error("发送消息给用户失败：{}", session.getId(), e);
                    }
                }
            }
        }
    }

}
