package com.ruijie.nse.mgr.common.interceptors;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.text.StrUtil;
import org.dromara.hutool.extra.spring.SpringUtil;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.time.Instant;

/**
 * 抽象类
 */
@Slf4j
public abstract class BaseInterceptorAdapter implements HandlerInterceptor {
    public BaseInterceptorAdapter() {
        super();
    }

    /**
     * 是否是方法映射
     * @param handler
     * @return
     */
    protected boolean isHandlerMethod(Object handler){
        return handler instanceof HandlerMethod;
    }

    /**
     * 验证请求有效期
     * @param timeStamp
     * @return
     */
    protected boolean isExpire(String timeStamp, long validTime){
        long ts = Long.parseLong(timeStamp);
        long now = Instant.now().getEpochSecond();
        // 超过有效期，则失效
        return Math.abs(now - ts) > validTime;
    }

    /**
     * 是个要忽略该次验证
     * @param request
     * @return
     */
    protected boolean isIgnore(HttpServletRequest request) {
        if (SpringUtil.getActiveProfile() != null && (!SpringUtil.getActiveProfile().equalsIgnoreCase("prod"))) {
            String ignore = request.getHeader("IGNORE_CHECK");
            if (StrUtil.isNotEmpty(ignore) && Boolean.TRUE.toString().equals(ignore)) {
                log.warn("IGNORE_CHECK 为true，不进行校验");
                return true;
            }
        }
        return false;
    }
}
