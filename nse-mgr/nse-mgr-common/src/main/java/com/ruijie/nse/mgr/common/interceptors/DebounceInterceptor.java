package com.ruijie.nse.mgr.common.interceptors;

import com.ruijie.nse.common.exception.DebounceException;
import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.common.config.DebounceProperties;
import com.ruijie.nse.mgr.common.exception.ExceptionCode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.cache.CacheUtil;
import org.dromara.hutool.core.cache.impl.TimedCache;
import org.dromara.hutool.core.text.StrValidator;
import org.dromara.hutool.crypto.digest.DigestUtil;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

import java.nio.charset.StandardCharsets;

/**
 * 全局接口防抖拦截器
 * 在指定时间窗口内，同一用户的相同请求只允许执行一次
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class DebounceInterceptor extends BaseInterceptorAdapter {
    
    private final DebounceProperties debounceProperties;
    private final AntPathMatcher pathMatcher = new AntPathMatcher();
    
    /**
     * 防抖缓存，key为请求标识，value为请求时间戳
     */
    private final TimedCache<String, Long> debounceCache = CacheUtil.newTimedCache(10 * 1000L);
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        // 检查是否启用防抖功能
        if (!debounceProperties.isEnabled()) {
            return true;
        }
        // 检查是否为HandlerMethod
        if (!isHandlerMethod(handler)) {
            return true;
        }
        // 检查是否需要忽略
        if (isIgnore(request)) {
            return true;
        }
        // 检查是否在排除路径中
        if (isExcludedPath(request.getRequestURI())) {
            return true;
        }
        try {
            String requestKey = generateRequestKey(request);
            if (isDuplicateRequest(requestKey)) {
                log.warn("检测到重复请求，触发防抖机制: {}", requestKey);
                throw new DebounceException(ExceptionCode.REQUEST_DEBOUNCE.getCode(), ExceptionCode.REQUEST_DEBOUNCE.getMessage());
            }
            // 记录请求
            recordRequest(requestKey);
            return true;
        } catch (DebounceException e) {
            throw e;
        } catch (Exception e) {
            log.error("防抖拦截器处理异常", e);
            return true;
        }
    }
    
    /**
     * 生成请求唯一标识
     * 格式：用户ID + 请求URI + 请求方法 + 请求体MD5
     */
    private String generateRequestKey(HttpServletRequest request) {
        StringBuilder keyBuilder = new StringBuilder();
        
        try {
            String userId = SecurityUtils.getUserId();
            if (userId != null && !userId.trim().isEmpty()) {
                keyBuilder.append("user:").append(userId);
            } else {
                String clientIp = getClientIpAddress(request);
                keyBuilder.append("ip:").append(clientIp);
            }
        } catch (Exception e) {
            String clientIp = getClientIpAddress(request);
            keyBuilder.append("ip:").append(clientIp);
        }
        
        // 添加请求URI
        keyBuilder.append(":").append(request.getRequestURI());
        
        // 添加请求方法
        keyBuilder.append(":").append(request.getMethod());
        
        // 添加请求参数
        String queryString = request.getQueryString();
        if (StrValidator.isNotBlank(queryString)) {
            keyBuilder.append("?").append(queryString);
        }
        
        // 添加请求体MD5（对于POST/PUT请求）
        if ("POST".equalsIgnoreCase(request.getMethod()) || 
            "PUT".equalsIgnoreCase(request.getMethod()) ||
            "PATCH".equalsIgnoreCase(request.getMethod())) {
            
            String requestBody = getRequestBody(request);
            if (StrValidator.isNotBlank(requestBody)) {
                String bodyMd5 = DigestUtil.md5Hex(requestBody, StandardCharsets.UTF_8);
                keyBuilder.append(":body:").append(bodyMd5);
            }
        }
        
        String finalKey = keyBuilder.toString();
        return DigestUtil.md5Hex(finalKey, StandardCharsets.UTF_8);
    }

    /**
     * 获取客户端真实IP地址
     * 考虑代理、负载均衡等情况
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String ip;

        // 检查是否通过代理
        ip = request.getHeader("X-Forwarded-For");
        if (isValidIpAddress(ip)) {
            // X-Forwarded-For可能包含多个IP，取第一个
            int index = ip.indexOf(',');
            if (index != -1) {
                ip = ip.substring(0, index);
            }
            return ip.trim();
        }

        // 检查Apache代理
        ip = request.getHeader("Proxy-Client-IP");
        if (isValidIpAddress(ip)) {
            return ip.trim();
        }

        // 检查WebLogic代理
        ip = request.getHeader("WL-Proxy-Client-IP");
        if (isValidIpAddress(ip)) {
            return ip.trim();
        }

        // 检查HTTP_CLIENT_IP
        ip = request.getHeader("HTTP_CLIENT_IP");
        if (isValidIpAddress(ip)) {
            return ip.trim();
        }

        // 检查HTTP_X_FORWARDED_FOR
        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (isValidIpAddress(ip)) {
            return ip.trim();
        }

        // 检查X-Real-IP（Nginx代理）
        ip = request.getHeader("X-Real-IP");
        if (isValidIpAddress(ip)) {
            return ip.trim();
        }

        // 最后获取远程地址
        ip = request.getRemoteAddr();

        // 如果是本地回环地址，尝试获取本机真实IP
        if ("127.0.0.1".equals(ip) || "0:0:0:0:0:0:0:1".equals(ip)) {
            try {
                java.net.InetAddress addr = java.net.InetAddress.getLocalHost();
                ip = addr.getHostAddress();
            } catch (Exception e) {
                log.debug("获取本机IP失败", e);
            }
        }

        return ip != null ? ip.trim() : "unknown";
    }

    /**
     * 检查IP地址是否有效
     * @param ip IP地址
     * @return true表示IP有效，false表示IP无效
     */
    private boolean isValidIpAddress(String ip) {
        return StrValidator.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip);
    }

    
    /**
     * 获取请求体内容
     */
    private String getRequestBody(HttpServletRequest request) {
        try {
            if (request instanceof RequestWrapper requestWrapper) {
                return requestWrapper.getBody();
            }
            return StrValidator.EMPTY;
        } catch (Exception e) {
            log.debug("获取请求体失败", e);
            return "";
        }
    }
    
    /**
     * 检查是否为重复请求
     */
    private boolean isDuplicateRequest(String requestKey) {
        Long lastRequestTime = debounceCache.get(requestKey);
        if (lastRequestTime == null) {
            return false;
        }

        long currentTime = System.currentTimeMillis();
        long timeDiff = currentTime - lastRequestTime;
        
        return timeDiff < debounceProperties.getTimeWindow();
    }
    
    /**
     * 记录请求
     */
    private void recordRequest(String requestKey) {
        long currentTime = System.currentTimeMillis();
        debounceCache.put(requestKey, currentTime, debounceProperties.getTimeWindow());
    }
    
    /**
     * 检查是否在排除路径中
     */
    private boolean isExcludedPath(String requestPath) {
        for (String pattern : debounceProperties.getExcludePatterns()) {
            if (pathMatcher.match(pattern, requestPath)) {
                return true;
            }
        }
        return false;
    }
}