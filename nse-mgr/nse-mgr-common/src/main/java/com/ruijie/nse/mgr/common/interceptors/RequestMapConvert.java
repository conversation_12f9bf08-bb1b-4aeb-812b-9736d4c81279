package com.ruijie.nse.mgr.common.interceptors;


import org.dromara.hutool.core.map.MapUtil;
import org.dromara.hutool.json.JSONUtil;

import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date 2022/9/16
 */
public class RequestMapConvert {

    private RequestMapConvert(){}
    public static String toJsonStr(Map<String, String[]> parameterMap){
        if(MapUtil.isEmpty(parameterMap)){
            return null;
        }
        Map<String, Object> map = new TreeMap<>();
        parameterMap.forEach((key, value) -> map.put(key, String.join(" ", value)));
        return JSONUtil.toJsonStr(map);
    }
}
