package com.ruijie.nse.mgr.common.interceptors;

import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;

/**
 * 用于request流传递
 * <AUTHOR>
 * @date 2021/11/18
 */
@Slf4j
@Component
public class RequestWrapperFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // Do nothing
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response,
                         FilterChain chain) throws IOException, ServletException {
        ServletRequest requestWrapper = null;

        // 跳过multipart请求
        if (request.getContentType() != null
                && request.getContentType().startsWith(MediaType.MULTIPART_FORM_DATA_VALUE)) {
            chain.doFilter(request, response);
            return;
        }

        if (request instanceof HttpServletRequest req) {
            requestWrapper = new RequestWrapper(req);
        }
        // 获取请求中的流，将取出来的字符串，再次转换成流，然后把它放入到新request对象中。
        // 在chain.doFiler方法中传递新的request对象
        chain.doFilter(requestWrapper != null ? requestWrapper : request, response);
    }

    @Override
    public void destroy() {
        // Do nothing
    }
}
