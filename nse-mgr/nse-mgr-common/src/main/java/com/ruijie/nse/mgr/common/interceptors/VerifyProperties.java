package com.ruijie.nse.mgr.common.interceptors;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 读取yml配置文件
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "nse.mgr.verify")
public class VerifyProperties {

    private String appid;
    private String appsecret;
    private Long expire;

}
