package com.ruijie.nse.mgr.common.interceptors;

import com.ruijie.nse.common.exception.InvalidAccessException;
import com.ruijie.nse.mgr.common.annotation.VerifyRequest;
import com.ruijie.nse.mgr.common.exception.ExceptionCode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.cache.CacheUtil;
import org.dromara.hutool.core.cache.impl.TimedCache;
import org.dromara.hutool.core.text.StrUtil;
import org.dromara.hutool.crypto.SecureUtil;
import org.dromara.hutool.extra.spring.SpringUtil;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;

/**
 * 用于验证接口签名拦截器
 *
 * <AUTHOR>
 * @date 2021/11/16
 */
@Slf4j
@Component
public class VerifySignInterceptor extends BaseInterceptorAdapter {

    private final VerifyProperties apiSecretProperties = SpringUtil.getBean(VerifyProperties.class);


    /**
     * 30s过期
     */
    private static final TimedCache<String, Object> NONCE_TIMED_CACHE = CacheUtil.newTimedCache(30 * 1000);


    @SuppressWarnings("all")
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 忽略此次验证
        if (!isHandlerMethod(handler) || isIgnore(request)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        if (isVerifySign(handlerMethod)) {
            // 校验签名
            verifySign(request);
        }

        return true;
    }

    /**
     * 验证接口是否需要签名
     * 如果是注解方式，那么验证加了@VerifySign注解的接口才需要鉴权。
     * 如果是uri方式，那么通过过滤器addUrl控制，进入该拦截器的都需要鉴权。
     *
     * @param handlerMethod
     * @return
     */
    private boolean isVerifySign(HandlerMethod handlerMethod) {

        // 获取方法上的注解
        VerifyRequest verifyRequest = handlerMethod.getMethod().getAnnotation(VerifyRequest.class);
        // 如果方法上的注解为空 则获取类的注解
        if (verifyRequest == null) {
            verifyRequest = handlerMethod.getMethod().getDeclaringClass().getAnnotation(VerifyRequest.class);
        }
        // 注解方式，如果没添加注解就不拦截
        if (verifyRequest == null) {
            return false;
        }
        return verifyRequest.sign();

    }

    /**
     * 验证签名
     *
     * @param request
     */
    private void verifySign(HttpServletRequest request) {
        String appId = request.getHeader("X-API-App-Id");
        String nonce = request.getHeader("X-API-Nonce");
        String timeStamp = request.getHeader("X-API-Timestamp");
        String signature = request.getHeader("X-API-Signature");
        if (StrUtil.isBlank(appId) || StrUtil.isBlank(timeStamp) || StrUtil.isBlank(nonce) || StrUtil.isBlank(signature)) {
            throw new InvalidAccessException(ExceptionCode.PARAM_LOST.getCode(), "报文丢失必要参数，该报文不合法");
        }
        if (!StrUtil.equals(appId, apiSecretProperties.getAppid())) {
            throw new InvalidAccessException(ExceptionCode.SECRET_EXPIRE.getCode(), "X-API-App-Id无效或已过期，请联系管理员");
        }

        // 验证请求有效期
        if (isExpire(timeStamp, apiSecretProperties.getExpire())) {
            throw new InvalidAccessException(ExceptionCode.TIMESTAMP_EXPIRE.getCode(), "该报文已过期，请重新封装报文");
        }

        // 验证nonce
        if (NONCE_TIMED_CACHE.containsKey(nonce)) {
            throw new InvalidAccessException(ExceptionCode.REQUEST_NONCE.getCode(), "该报文请求已完成，请勿重复请求");
        }

        String verifySign = SecureUtil.hmacMd5(apiSecretProperties.getAppsecret()).digestHex(appId + nonce + timeStamp);
        log.info("接口实际签名：{}，传输签名：{} URL:{}", verifySign, signature, request.getRequestURI());
        if (!signature.equalsIgnoreCase(verifySign)) {
            throw new InvalidAccessException(ExceptionCode.VERIFY_FAIL.getCode(), "签名验证失败，请重新尝试");
        }

        // 添加到缓存
        NONCE_TIMED_CACHE.put(nonce, nonce, 30 * 1000);
    }

}
