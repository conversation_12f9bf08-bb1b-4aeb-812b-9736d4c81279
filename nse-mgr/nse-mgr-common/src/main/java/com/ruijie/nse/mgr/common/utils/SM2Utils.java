package com.ruijie.nse.mgr.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.asn1.ASN1InputStream;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x509.SubjectPublicKeyInfo;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.crypto.params.ECPublicKeyParameters;
import org.bouncycastle.crypto.params.ParametersWithRandom;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPublicKey;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.dromara.hutool.core.io.IoUtil;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.function.Supplier;

/**
 * SM2工具类
 * 国密SM2非对称加密算法实现
 */
@Slf4j
public class SM2Utils {

    private static final String ALGORITHM = "EC";
    private static final String PROVIDER = "BC";
    private static final String CURVE_NAME = "sm2p256v1";

    private static final String publicKey2 = """
            MIIBMzCB7AYHKoZIzj0CATCB4AIBATAsBgcqhkjOPQEBAiEA/////v////////////////////8A
            AAAA//////////8wRAQg/////v////////////////////8AAAAA//////////wEICjp+p6dn140
            TVqeS89lCafzl4n1FauPkt28vUFNlA6TBEEEMsSuLB8ZgRlfmQRGajnJlI/jC7/yZgvhcVpFiTNM
            dMe8Nzai9PZ3nFm9zuNraSFT0KmHfMYqR0AC3zLlITnwoAIhAP////7///////////////9yA99r
            IcYFK1O79Ak51UEjAgEBA0IABB9PCw3alhaQSmpizF0nIBF9GYrtNsPa9hV2sAX//dDeNXKUlD2q
            C7bg2p16TetGjK8bq4GkoqbHjh6msPTJKys=
            """;
    private static final String privateKey2 = """
            MIICSwIBADCB7AYHKoZIzj0CATCB4AIBATAsBgcqhkjOPQEBAiEA/////v//////////////////
            //8AAAAA//////////8wRAQg/////v////////////////////8AAAAA//////////wEICjp+p6d
            n140TVqeS89lCafzl4n1FauPkt28vUFNlA6TBEEEMsSuLB8ZgRlfmQRGajnJlI/jC7/yZgvhcVpF
            iTNMdMe8Nzai9PZ3nFm9zuNraSFT0KmHfMYqR0AC3zLlITnwoAIhAP////7///////////////9y
            A99rIcYFK1O79Ak51UEjAgEBBIIBVTCCAVECAQEEIKuhBR3J7Kv6kSJOXsWvmc/2vgso/Ih//hGH
            oNFCjhwToIHjMIHgAgEBMCwGByqGSM49AQECIQD////+/////////////////////wAAAAD/////
            /////zBEBCD////+/////////////////////wAAAAD//////////AQgKOn6np2fXjRNWp5Lz2UJ
            p/OXifUVq4+S3by9QU2UDpMEQQQyxK4sHxmBGV+ZBEZqOcmUj+MLv/JmC+FxWkWJM0x0x7w3NqL0
            9necWb3O42tpIVPQqYd8xipHQALfMuUhOfCgAiEA/////v///////////////3ID32shxgUrU7v0
            CTnVQSMCAQGhRANCAAQfTwsN2pYWkEpqYsxdJyARfRmK7TbD2vYVdrAF//3Q3jVylJQ9qgu24Nqd
            ek3rRoyvG6uBpKKmx44eprD0ySsr
            """;

    static {
        // 添加BouncyCastle作为安全提供者
        if (Security.getProvider(PROVIDER) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    /**
     * 从PEM格式文件加载SM2私钥
     * @param supplier 输入流提供者
     * @return SM2私钥
     * @throws Exception 加载私钥异常
     */
    public static PrivateKey getPrivateKeyBase64(Supplier<InputStream> supplier) throws Exception {
        String pemContent = IoUtil.readUtf8(supplier.get())
                .replaceAll("-----.*-----", "").replaceAll("\\s", "");
        byte[] keyBytes = Base64.getDecoder().decode(pemContent);
        return KeyFactory.getInstance(ALGORITHM, PROVIDER)
                .generatePrivate(new PKCS8EncodedKeySpec(keyBytes));
    }

    /**
     * 使用SM2私钥解密数据
     * @param encryptedBase64 Base64编码的加密内容
     * @param privateKey SM2私钥
     * @return 解密后的明文
     */
    public static String decrypt(String encryptedBase64, PrivateKey privateKey) {
        try {
            if (privateKey == null) {
                throw new RuntimeException("未配置SM2私钥，无法解密内容");
            }

            X9ECParameters ecParameters = GMNamedCurves.getByName(CURVE_NAME);
            ECDomainParameters domainParameters = new ECDomainParameters(
                    ecParameters.getCurve(),
                    ecParameters.getG(),
                    ecParameters.getN(),
                    ecParameters.getH());

            // 转换为BC的私钥格式
            BCECPrivateKey bcecPrivateKey = (BCECPrivateKey) privateKey;
            ECPrivateKeyParameters privateKeyParameters = new ECPrivateKeyParameters(
                    bcecPrivateKey.getD(),
                    domainParameters);

            // 初始化SM2解密引擎
            SM2Engine engine = new SM2Engine(SM2Engine.Mode.C1C3C2);
            engine.init(false, privateKeyParameters);

            // 解码Base64密文
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedBase64);

            // 执行解密
            byte[] decryptedBytes = engine.processBlock(encryptedBytes, 0, encryptedBytes.length);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("SM2解密失败", e);
            throw new RuntimeException("License数据内容有误，请确认", e);
        }
    }

    public static String decrypt(String encryptedBase64) throws NoSuchAlgorithmException, NoSuchProviderException, InvalidKeySpecException {

        byte[] keyBytes = Base64.getDecoder().decode(privateKey2.replaceAll("\\s", ""));
        PrivateKey privateKey = KeyFactory.getInstance(ALGORITHM, PROVIDER).generatePrivate(new PKCS8EncodedKeySpec(keyBytes));
        return decrypt(encryptedBase64, privateKey);
    }

    /**
     * 使用SM2公钥加密数据
     * @param content 待加密内容
     * @return Base64编码的加密结果
     */
    public static String encrypt(String content) {
        try {
            byte[] keyBytes = Base64.getDecoder().decode(publicKey2.replaceAll("\\s", ""));
            PublicKey publicKey = KeyFactory.getInstance(ALGORITHM, PROVIDER)
                    .generatePublic(new X509EncodedKeySpec(keyBytes));

            X9ECParameters ecParameters = GMNamedCurves.getByName(CURVE_NAME);
            ECDomainParameters domainParameters = new ECDomainParameters(
                    ecParameters.getCurve(),
                    ecParameters.getG(),
                    ecParameters.getN(),
                    ecParameters.getH());

            // 转换为BC的公钥格式
            BCECPublicKey bcecPublicKey = (BCECPublicKey) publicKey;
            ECPublicKeyParameters publicKeyParameters = new ECPublicKeyParameters(
                    bcecPublicKey.getQ(),
                    domainParameters);

            // 初始化SM2加密引擎
            SM2Engine engine = new SM2Engine(SM2Engine.Mode.C1C3C2);
            engine.init(true, new ParametersWithRandom(publicKeyParameters, new SecureRandom()));

            // 执行加密
            byte[] inputBytes = content.getBytes(StandardCharsets.UTF_8);
            byte[] encryptedBytes = engine.processBlock(inputBytes, 0, inputBytes.length);

            // 返回Base64编码的加密结果
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            log.error("License数字签名失败", e);
            throw new RuntimeException("License数字签名失败: " + e.getMessage(), e);
        }
    }
}