package com.ruijie.nse.mgr.course.controller;

import com.ruijie.nse.common.dto.PageInput;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.dto.R;
import com.ruijie.nse.mgr.course.dto.input.AssignHomeworkInput;
import com.ruijie.nse.mgr.course.dto.input.CourseInput;
import com.ruijie.nse.mgr.course.dto.output.*;
import com.ruijie.nse.mgr.course.service.CourseService;
import com.ruijie.nse.mgr.repository.dto.input.CourseQueryInput;
import com.ruijie.nse.mgr.repository.dto.input.CourseStudentQueryInput;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;

/**
 * 课程控制器
 */
@RestController
@RequestMapping("/api/course")
public class CourseController {

    @Autowired
    private CourseService courseService;

    /**
     * 分页查询课程（简单条件）
     *
     * @param queryInput
     * @return 分页结果
     */
    @GetMapping("/page")
    public R<PageOutput<CourseOutput>> page(CourseQueryInput queryInput) {
        return R.success(courseService.findByPage(queryInput));
    }

    /**
     * 保存课程
     *
     * @param courseInput 课程信息
     * @return 操作结果
     */
    @PostMapping
    public R<Void> save(@RequestBody CourseInput courseInput) {
        courseService.create(courseInput);
        return R.success();
    }

    /**
     * 根据ID获取课程详情
     *
     * @param id 课程ID
     * @return 课程详情
     */
    @GetMapping("/{id}")
    public R<CourseOutput> get(@PathVariable String id) {
        return R.success(courseService.getById(id));
    }

    /**
     * 根据ID删除课程
     *
     * @param id 课程ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public R<Void> delete(@PathVariable String id) {
        courseService.deleteBatch(Collections.singletonList(id));
        return R.success();
    }

    /**
     * 批量删除课程
     *
     * @param ids 课程ID列表
     * @return 操作结果
     */
    @DeleteMapping("/batch")
    public R<Void> deleteBatch(@RequestBody List<String> ids) {
        courseService.deleteBatch(ids);
        return R.success();
    }

    /**
     * 分页查询课程学生
     *
     * @param queryInput
     * @return 分页结果
     */
    @GetMapping("/student/page")
    public R<CourseStudentPageOutput> studentPage(CourseStudentQueryInput queryInput) {
        return R.success(courseService.findCourseStudentByPage(queryInput));
    }

    /**
     * 获取课程学生ID列表
     *
     * @param courseId 课程ID
     * @return 学生ID列表
     */
    @GetMapping("/student/{courseId}")
    public R<List<String>> studentIds(@PathVariable("courseId") String courseId) {
        return R.success(courseService.getStudentIds(courseId));
    }

    /**
     * 新增课程学生
     *
     * @param courseInput 课程信息
     * @return 操作结果
     */
    @PostMapping("/student")
    public R<Void> addStudent(@RequestBody CourseInput courseInput) {
        courseService.addStudent(courseInput);
        return R.success();
    }

    /**
     * 删除课程学生
     *
     * @param ids
     * @return
     */
    @DeleteMapping("/student")
    public R<Void> deleteStudent(@RequestBody List<String> ids) {
        courseService.deleteStudent(ids);
        return R.success();
    }

    /**
     * 导出课程数据
     *
     * @param courseId 课程ID
     * @param response
     */
    @GetMapping("/export/{courseId}")
    public void exportCourseData(@PathVariable String courseId, HttpServletResponse response) {
        courseService.exportCourseData(courseId, response);
    }

    /**
     * 上课校验
     *
     * @param courseId 课程ID
     * @return 操作结果
     */
    @GetMapping("/startCheck/{courseId}")
    public R<CourseStartCheckResultOutput> startCheck(@PathVariable String courseId) {
        return R.success(courseService.checkBeforeStartCourse(courseId));
    }

    /**
     * 开始上课
     *
     * @param courseId 课程ID
     * @return 操作结果
     */
    @PostMapping("/start/{courseId}")
    public R<Void> startCourse(@PathVariable String courseId) {
        courseService.startCourse(courseId);
        return R.success();
    }

    /**
     * 结束上课
     *
     * @param courseId 课程ID
     * @return 操作结果
     */
    @PostMapping("/end/{courseId}")
    public R<Void> endCourse(@PathVariable String courseId) {
        courseService.endCourse(courseId);
        return R.success();
    }

    /**
     * 布置作业
     *
     * @param input 布置作业参数
     * @return 操作结果
     */
    @PostMapping("/assignHomework")
    public R<Void> assignHomework(@RequestBody AssignHomeworkInput input) {
        courseService.assignHomework(input);
        return R.success();
    }

    /**
     * 获取正在上课的课程列表
     *
     * @return 正在上课的课程列表
     */
    @GetMapping("/ongoing")
    public R<List<OngoingCourseOutput>> getOngoingCourses() {
        return R.success(courseService.getOngoingCourses());
    }

    /**
     * 获取正在上课的课程的账号登录情况
     *
     * @param courseId 课程ID
     * @return 学生登录情况列表
     */
    @GetMapping("/ongoing/account/{courseId}")
    public R<List<LoginUserOutput>> getOngoingCourseAccount(@PathVariable String courseId) {
        return R.success(courseService.getOngoingCourseAccount(courseId));
    }

    /**
     * 获取正在练习的账号登录情况
     *
     * @return
     */
    @GetMapping("/exercise/account")
    public R<PageOutput<LoginUserOutput>> getExerciseAccount(PageInput pageInput) {
        return R.success(courseService.getExerciseAccount(pageInput));
    }

}