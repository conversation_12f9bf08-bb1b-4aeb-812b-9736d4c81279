package com.ruijie.nse.mgr.course.controller;

import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.dto.R;
import com.ruijie.nse.common.monitor.annotation.MemoryLimit;
import com.ruijie.nse.mgr.course.dto.ExperimentUrlDto;
import com.ruijie.nse.mgr.course.dto.input.ExperimentCreateInput;
import com.ruijie.nse.mgr.course.dto.input.ExperimentPreviewInput;
import com.ruijie.nse.mgr.course.dto.input.ExperimentQueryInput;
import com.ruijie.nse.mgr.course.dto.input.ExperimentRenameInput;
import com.ruijie.nse.mgr.course.dto.output.ExperimentPageOutput;
import com.ruijie.nse.mgr.course.service.ExperimentService;
import com.ruijie.nse.mgr.license.annotation.VerifyLicense;
import com.ruijie.nse.mgr.py3server.annotation.ValidateReachable;
import jakarta.annotation.Nonnull;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 实验管理
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/mgr/experiment")
public class ExperimentController {

    private final ExperimentService experimentService;

    /**
     * @param input
     * @return
     */
    @PostMapping("my/page")
    @VerifyLicense
    public R<PageOutput<ExperimentPageOutput>> getMyExperimentPage(@RequestBody ExperimentQueryInput input) {
        return R.success(experimentService.findPage(input));
    }

    /**
     * 创建实验
     *
     * @param input
     * @return
     */
    @PostMapping("create")
    @PreAuthorize("hasAuthority('course:experiment:create')")
    @ValidateReachable(autoStart = true, message = "创建实验前需要确保NSE Server服务可达")
    @VerifyLicense
    public R<String> createExperiment(@RequestBody @Valid @Nonnull ExperimentCreateInput input) throws IOException {
        log.info("创建实验，输入参数：{}", input);
        input.checkParamValidation();
        return R.success(experimentService.createExperiment(input));
    }


    /**
     * 查看实验
     * @param input
     * @return
     * @throws IOException
     */
    @PostMapping("preview")
//    @PreAuthorize("hasAuthority('course:experiment:preview')")
    @ValidateReachable(autoStart = true, message = "查看实验前需要确保NSE Server服务可达")
    @VerifyLicense
    public R<ExperimentUrlDto> previewExperiment(@RequestBody @Valid @Nonnull ExperimentPreviewInput input) throws IOException {
        log.info("查看实验，输入参数：{}", input);
        input.checkParamValidation();
        return R.success(experimentService.previewExperiment(input));
    }

    /**
     * 删除实验
     *
     * @param ids
     * @return
     */
    @DeleteMapping("delete")
    @PreAuthorize("hasAuthority('course:experiment:delete')")
    @ValidateReachable(autoStart = true, message = "删除实验前需要确保NSE Server服务可达")
    @VerifyLicense
    public R<Void> deleteExperiment(@RequestBody @Valid @Nonnull List<String> ids) {
        log.info("删除实验，输入参数：{}", ids);
        experimentService.deleteExperiment(ids);
        return R.success();
    }

    /**
     * 复制实验
     *
     * @param id
     * @return
     */
    @GetMapping("duplicate/{id}")
    @PreAuthorize("hasAuthority('course:experiment:duplicate')")
    @ValidateReachable(autoStart = true, message = "复制实验前需要确保NSE Server服务可达")
    @VerifyLicense
    public R<Void> duplicate(@PathVariable String id) {
        log.info("复制实验，输入参数：{}", id);
        experimentService.duplicateExperiment(id);
        return R.success();
    }


    /**
     * 重命名实验
     *
     * @param input
     * @return
     */
    @PutMapping("rename/{id}")
    @PreAuthorize("hasAuthority('course:experiment:rename')")
    @VerifyLicense
    public R<Void> rename(@PathVariable("id") String id, @RequestBody @Valid @Nonnull ExperimentRenameInput input) {
        experimentService.renameExperiment(id, input);
        return R.success();
    }

    /**
     * 开始实验，获取gns3服务的实验地址和主机信息
     *
     * @param id 实验ID
     * @return 实验URL信息DTO
     */
    @GetMapping("project/url/sign/{id}")
    @PreAuthorize("hasAuthority('course:experiment:open')")
    @ValidateReachable(autoStart = true, message = "开始实验前需要确保NSE Server服务可达")
    @VerifyLicense
    @MemoryLimit(threshold = 0.85)
    public R<ExperimentUrlDto> buildProjectSignUrl(@PathVariable("id") String id) {
        return R.success(experimentService.buildProjectSignUrl(id));
    }

    /**
     * 下载实验
     *
     * @param id 实验ID
     * @return 实验文件
     */
    @GetMapping("project/export/{id}")
    @PreAuthorize("hasAuthority('course:experiment:export')")
    @ValidateReachable(autoStart = true, message = "导出实验前需要确保NSE Server服务可达")
    @VerifyLicense
    public void exportProject(@PathVariable("id") String id, HttpServletResponse response) {
        experimentService.exportExperiment(id, response);
    }


    @PostMapping("/submit/{id}")
    @VerifyLicense
    public R<Void> submitExperiment(@PathVariable("id") String id,
                                    @RequestParam("expResult") MultipartFile file) throws IOException {
        experimentService.submitExperiment(id, file);
        return R.success();
    }


    /**
     * 预览实验作业
     *
     * @param id 主键id
     */
    @GetMapping("/preview/{id}")
    @VerifyLicense
    public void previewExpResult(@PathVariable("id") String id,
                            HttpServletResponse response) throws IOException {

        experimentService.previewExpResult(id,response);

    }

//    /**
//     * 获取实验详情
//     *
//     * @param id 实验ID
//     * @return 实验详情信息
//     */
//    @GetMapping("/detail/{id}")
//    @VerifyLicense
//    public R<ExperimentPageOutput> getExperimentDetail(@PathVariable("id") String id) {
//        return R.success(experimentService.getExperimentDetail(id));
//    }

//    /**
//     * 预览实验手册PDF
//     *
//     * @param experimentId 实验ID
//     * @param response HTTP响应对象
//     */
//    @GetMapping("/manual/preview")
//    @VerifyLicense
//    public void previewExperimentManual(@RequestParam("experimentId") String experimentId,
//                                      HttpServletResponse response) throws IOException {
//        experimentService.previewExperimentManual(experimentId, response);
//    }


}
