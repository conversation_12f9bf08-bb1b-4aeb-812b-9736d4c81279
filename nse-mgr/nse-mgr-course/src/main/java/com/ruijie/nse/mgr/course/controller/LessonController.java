package com.ruijie.nse.mgr.course.controller;


import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.dto.R;
import com.ruijie.nse.mgr.course.dto.input.ExperimentDataExportInput;
import com.ruijie.nse.mgr.course.dto.input.ExperimentDataQueryInput;
import com.ruijie.nse.mgr.course.dto.input.LessonAssignQueryInput;
import com.ruijie.nse.mgr.course.dto.input.LessonScoreInput;
import com.ruijie.nse.mgr.course.dto.output.LessonAssignPageOutput;
import com.ruijie.nse.mgr.course.service.LessonService;
import com.ruijie.nse.mgr.license.annotation.VerifyLicense;
import com.ruijie.nse.mgr.repository.dto.input.ExperimentLessonDataInput;
import com.ruijie.nse.mgr.repository.dto.output.ExperimentLessonDataOutput;
import com.ruijie.nse.mgr.repository.entity.view.ViewExperimentDataStatistic;
import jakarta.annotation.Nonnull;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;


/**
 * 学生上课，作业管理
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/mgr/lesson")
public class LessonController {

    private final LessonService lessonService;

    /**
     * 老师布置的作业
     * @param input
     * @return
     */
    @PostMapping("assigned/page")
    @VerifyLicense
    public R<PageOutput<LessonAssignPageOutput>> getAssignedPage(@RequestBody LessonAssignQueryInput input) {
        return R.success(lessonService.findAssignedPage(input));
    }

    /**
     * 实验数据
     * @param input
     * @return
     */
    @PostMapping("experiment/data")
    @VerifyLicense
    public R<PageOutput<ViewExperimentDataStatistic>> getExperimentDataPage(@RequestBody ExperimentDataQueryInput input) {
        return R.success(lessonService.findExperimentDataPage(input));
    }

    /**
     * 导出实验数据Excel
     * @param input
     * @param response
     */
    @PostMapping("experiment/data/export")
    @VerifyLicense
    public void exportExperimentData(@RequestBody ExperimentDataExportInput input, HttpServletResponse response) throws IOException {
        lessonService.exportExperimentDataToExcel(input, response);
    }

    /**
     * 实验数据-详情
     * @param input
     * @return
     */
    @PostMapping("experiment/data/detail")
    @VerifyLicense
    public R<PageOutput<ExperimentLessonDataOutput>> getExperimentDataDetail(@RequestBody @Valid ExperimentLessonDataInput input) {
        return R.success(lessonService.findExperimentDataDetail(input));
    }


    /**
     * 提交评分
     * @param input
     * @return
     */
    @PutMapping("score")
    @VerifyLicense
    public R<Void> submitScore(@RequestBody LessonScoreInput input) {
        lessonService.submitScore(input);
        return R.success();
    }


    /**
     * 根据lesson批次号  删除实验数据
     * @param lessonBatchNos
     * @return
     */
    @DeleteMapping("experiment/data/delete")
    @PreAuthorize("hasAuthority('course:experiment:delete')")
    @VerifyLicense
    public R<Void> deleteLessonsBatchNo(@RequestBody @Valid @Nonnull List<String> lessonBatchNos) {
        log.info("根据lesson 批次号 删除实验，输入参数：{}", lessonBatchNos);
        lessonService.deleteLessonsBatchNo(lessonBatchNos);
        return R.success();
    }


    /**
     * 根据lesson id  删除实验数据
     * @param lessonIds
     * @return
     */
    @DeleteMapping("experiment/data/detail/delete")
    @PreAuthorize("hasAuthority('course:experiment:delete')")
    @VerifyLicense
    public R<Void> deleteLesson(@RequestBody @Valid @Nonnull List<String> lessonIds) {
        log.info("根据lesson ids 删除实验，输入参数：{}", lessonIds);
        lessonService.deleteLessons(lessonIds);
        return R.success();
    }

}
