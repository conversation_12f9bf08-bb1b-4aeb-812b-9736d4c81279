package com.ruijie.nse.mgr.course.dto.input;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 课程输入实体类
 */
@Data
@Accessors(chain = true)
public class CourseInput {

    private String id;

    /**
     * 用户ID
     */
    private List<String> userIds;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 班级名称
     */
    private String className;
}