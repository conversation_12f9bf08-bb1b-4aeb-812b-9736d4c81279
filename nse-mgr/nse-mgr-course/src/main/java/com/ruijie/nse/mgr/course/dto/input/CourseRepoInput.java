package com.ruijie.nse.mgr.course.dto.input;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.web.multipart.MultipartFile;

/**
 * 课程库输入实体类
 */
@Data
@Accessors(chain = true)
public class CourseRepoInput {


    private String id;

    /**
     * 课程包
     */
    private String coursePkg;

    /**
     * 实验名称
     */
    private String expName;

    /**
     * 实验手册文件
     */
    private MultipartFile expManualFile;

    /**
     * 实验工程文件
     */
    private MultipartFile expProjectFile;

    /**
     * 实验手册路径
     */
    private String expManualPath;

    /**
     * 实验工程文件路径
     */
    private String expProjectPath;

    /**
     * 实验类型
     */
    private String expType;

    /**
     * 备注
     */
    private String remark;

}
