package com.ruijie.nse.mgr.course.dto.input;


import com.ruijie.nse.mgr.course.constants.ExperimentConstants;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.dromara.hutool.core.text.StrUtil;

@Data
public class ExperimentCreateInput {

    /**
     * 实验类型， 空白实验， 课程库实验
     */
    @NotBlank(message = "实验类型不能为空")
    @Pattern(regexp = "^(空白实验|课程库实验|布置作业)$", message = "实验类型错误")
    private String experimentType;

    /**
     * 实验名称
     */
    private String experimentName;

    /**
     * 课程库ID
     */
    private String courseRepoId;

    /**
     * 上课/作业ID
     */
    private String lessonId;

    public boolean isCourseRepoExp() {
        return ExperimentConstants.ExpCreateType.COURSE_REPO.equals(experimentType);
    }

    public boolean isBlankExp() {
        return ExperimentConstants.ExpCreateType.BLANK.equals(experimentType);
    }

    public boolean isLessonExp() {
        return ExperimentConstants.ExpCreateType.LESSON.equals(experimentType);
    }

    public void checkParamValidation() {
        if (isCourseRepoExp() && StrUtil.isBlankIfStr(courseRepoId)) {
            throw new IllegalArgumentException("课程库实验类型，课程库ID不能为空");
        }
        if (isBlankExp() && StrUtil.isBlankIfStr(experimentName)) {
            throw new IllegalArgumentException("空白实验类型，请输入实验名称");
        }
        if (isLessonExp() && StrUtil.isBlankIfStr(lessonId)) {
            throw new IllegalArgumentException("作业实验类型，作业ID不能为空");
        }
    }
}
