package com.ruijie.nse.mgr.course.dto.input;

import com.ruijie.nse.common.dto.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 实验数据查询input
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExperimentDataQueryInput extends PageInput {

    private String courseId;
    private String expName;
    private List<Integer> totals;
    private List<Integer> submits;
    private List<Integer> percents;
}
