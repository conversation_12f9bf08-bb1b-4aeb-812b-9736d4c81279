package com.ruijie.nse.mgr.course.dto.input;


import lombok.Data;
import org.dromara.hutool.core.text.StrUtil;

/**
 * 实验预览input
 */
@Data
public class ExperimentPreviewInput {


    /**
     * 实验id
     */
    private String experimentId;

    /**
     * 上课/作业ID
     */
    private String lessonId;



    public void checkParamValidation() {
        if (StrUtil.isBlankIfStr(experimentId) && StrUtil.isBlankIfStr(lessonId)) {
            throw new IllegalArgumentException("查看实验，关联ID不能为空");
        }
    }
}
