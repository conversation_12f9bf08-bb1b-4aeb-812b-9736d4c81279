package com.ruijie.nse.mgr.course.dto.input;


import com.ruijie.nse.common.dto.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.hutool.core.collection.CollUtil;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class ExperimentQueryInput extends PageInput {

    private String experimentName;
    private String experimentType;
    private List<String> createdDate;
    private List<String> modifiedDate;


    public String getCreatedDate(int index) {
        if (CollUtil.isEmpty(createdDate) || createdDate.size() < 2) {
            return null;
        }
        return createdDate.get(index);
    }

    public String getModifiedDate(int index) {
        if (CollUtil.isEmpty(modifiedDate) || modifiedDate.size() < 2) {
            return null;
        }
        return modifiedDate.get(index);
    }
}
