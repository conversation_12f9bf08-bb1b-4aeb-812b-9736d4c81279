package com.ruijie.nse.mgr.course.dto.input;


import com.ruijie.nse.common.dto.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.hutool.core.collection.CollUtil;

import java.util.List;

/**
 * 老师布置的实验，列表input
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LessonAssignQueryInput extends PageInput {
    private String courseName;
    private String experimentName;
    private String teacherName;
    private List<String> createdDate;
    private List<String> submitDate;
    private String submitStatus;
    private List<Integer> score;

    public String getCreatedDate(int index) {
        if (CollUtil.isEmpty(createdDate) || createdDate.size() < 2) {
            return null;
        }
        return createdDate.get(index);
    }

    public String getSubmitDate(int index) {
        if (CollUtil.isEmpty(submitDate) || submitDate.size() < 2) {
            return null;
        }
        return submitDate.get(index);
    }

    public Integer getScore(int index) {
        if (CollUtil.isEmpty(score) || score.size() < 2) {
            return null;
        }
        return score.get(index);
    }
}
