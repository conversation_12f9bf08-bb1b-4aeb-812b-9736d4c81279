package com.ruijie.nse.mgr.course.dto.output;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 课程输出实体类
 */
@Data
@Accessors(chain = true)
public class CourseOutput {

    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 班级ID
     */
    private String classId;

    /**
     * 状态
     */
    private String status;

    /**
     * 学生数量
     */
    private Integer studentCnt;

    /**
     * 实验数量
     */
    private Integer expCnt;

    /**
     * 课程资源ID
     */
    private String courseRepoId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 创建人
     */
    private String createdBy;
}