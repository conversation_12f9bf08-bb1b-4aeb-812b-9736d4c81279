package com.ruijie.nse.mgr.course.dto.output;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 课程库输出实体类
 */
@Data
@Accessors(chain = true)
public class CourseRepoOutput {

    private String id;

    /**
     * 课程包
     */
    private String coursePkg;

    /**
     * 实验名称
     */
    private String expName;

    /**
     * 实验手册路径
     */
    private String expManualPath;

    /**
     * 实验手册名称
     */
    private String expManualName;

    /**
     * 实验工程文件路径
     */
    private String expProjectPath;

    /**
     * 实验工程文件名称
     */
    private String expProjectName;

    /**
     * 实验类型
     */
    private String expType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 创建人
     */
    private String createdBy;

}
