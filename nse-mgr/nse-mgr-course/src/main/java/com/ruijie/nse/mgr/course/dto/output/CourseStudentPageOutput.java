package com.ruijie.nse.mgr.course.dto.output;

import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.mgr.repository.dto.output.CourseStudentOutput;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class CourseStudentPageOutput extends PageOutput<CourseStudentOutput>  {

    private String courseName;

    private String className;

    private String teacherName;

    private Integer studentCnt;
}
