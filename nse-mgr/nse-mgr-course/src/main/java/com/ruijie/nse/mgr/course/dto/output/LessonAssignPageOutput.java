package com.ruijie.nse.mgr.course.dto.output;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 布置作业dto
 */
@Data
public class LessonAssignPageOutput {

    private String id;
    private String courseName;
    private String experimentName;

    /**
     * 实验手册
     */
    private String experimentManualPath;
    private String teacherName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createdDate;
    private Date submitDate;
    private String submitStatus;
    private Double score;
    private String expResultFilename;
    private String expResultPath;

    /**
     * 课程库id
     */
    private String courseRepoId;
}
