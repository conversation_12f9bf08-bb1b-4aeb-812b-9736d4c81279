package com.ruijie.nse.mgr.course.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.exception.BusinessException;
import com.ruijie.nse.mgr.common.constants.CommonConstants;
import com.ruijie.nse.mgr.course.dto.input.CourseRepoInput;
import com.ruijie.nse.mgr.course.dto.output.CourseRepoOutput;
import com.ruijie.nse.mgr.repository.dto.input.CourseRepoQueryInput;
import com.ruijie.nse.mgr.repository.entity.CourseRepo;
import com.ruijie.nse.mgr.repository.entity.Lesson;
import com.ruijie.nse.mgr.repository.mapper.CourseRepoDao;
import com.ruijie.nse.mgr.repository.mapper.LessonDao;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.hutool.core.bean.BeanUtil;
import org.dromara.hutool.core.collection.CollUtil;
import org.dromara.hutool.core.data.id.IdUtil;
import org.dromara.hutool.core.io.file.FileUtil;
import org.dromara.hutool.core.text.StrValidator;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 课程库服务类
 */
@Service
public class CourseRepoService extends ServiceImpl<CourseRepoDao, CourseRepo> {

    @Resource
    private LessonDao lessonDao;

    /**
     * 分页查询课程库
     * @param queryInput
     * @return 分页结果
     */
    public PageOutput<CourseRepoOutput> findByPage(CourseRepoQueryInput queryInput) {
        Page<CourseRepo> page = baseMapper.findPage(new Page<>(queryInput.getPageNumber(), queryInput.getPageSize()), queryInput);
        List<CourseRepoOutput> records = BeanUtil.copyToList(page.getRecords(), CourseRepoOutput.class);
        return new PageOutput<>(page.getTotal(), records);
    }

    /**
     * 保存或更新课程库
     * @param courseRepoInput 课程库输入对象
     * @return 保存结果
     */
    public CourseRepoOutput saveOrUpdate(CourseRepoInput courseRepoInput) {
        CourseRepo courseRepo = BeanUtil.toBean(courseRepoInput, CourseRepo.class);
        // 处理实验手册文件
        if (courseRepoInput.getExpManualFile() != null && !courseRepoInput.getExpManualFile().isEmpty()) {
            String manualPath = saveFile(courseRepoInput.getExpManualFile(), "exp-manual");
            courseRepo.setExpManualPath(manualPath);
            courseRepo.setExpManualName(courseRepoInput.getExpManualFile().getOriginalFilename());
        }
        // 处理实验工程文件
        if (courseRepoInput.getExpProjectFile() != null && !courseRepoInput.getExpProjectFile().isEmpty()) {
            String projectPath = saveFile(courseRepoInput.getExpProjectFile(), "exp-project");
            courseRepo.setExpProjectPath(projectPath);
            courseRepo.setExpProjectName(courseRepoInput.getExpProjectFile().getOriginalFilename());
        }
        if (courseRepoInput.getId() == null) {
            baseMapper.insert(courseRepo);
        } else {
            baseMapper.updateById(courseRepo);
        }
        return BeanUtil.toBean(courseRepo, CourseRepoOutput.class);
    }

    /**
     * 保存文件到服务器并返回相对路径
     * @param file 上传的文件
     * @return 文件存储路径
     */
    private String saveFile(MultipartFile file, String directory) {
        try {
            Path uploadPath = CommonConstants.Paths.COURSE_REPO_PATH.resolve(directory);
            if (!Files.exists(uploadPath)) {
                Files.createDirectories(uploadPath);
            }
            String fileName = IdUtil.getSnowflakeNextIdStr() + "_" + file.getOriginalFilename();
            Path filePath = uploadPath.resolve(fileName);
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
            // 返回相对于CourseConstants.COURSE_REPO_PATH路径的相对路径
            return CommonConstants.Paths.COURSE_REPO_PATH.relativize(filePath).toString();
        } catch (IOException e) {
            throw BusinessException.errorByMessage("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取课程库详情
     * @param id 课程库ID
     * @return 课程库详情
     */
    public CourseRepoOutput getById(String id) {
        CourseRepo courseRepo = baseMapper.selectById(id);
        if (courseRepo == null) {
            throw BusinessException.errorByMessage("课程库不存在");
        }
        return BeanUtil.toBean(courseRepo, CourseRepoOutput.class);
    }

     /**
     * 批量删除课程库
     * @param ids 课程库ID列表
     */
    public void deleteBatch(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            throw BusinessException.errorByMessage("请选择要删除的数据");
        }
        baseMapper.deleteByIds(ids);
        List<Lesson> lessonList = lessonDao.selectList(Wrappers.lambdaQuery(Lesson.class).in(Lesson::getCourseRepoId, ids));
        if (CollUtil.isNotEmpty(lessonList)) {
            Set<String> repoIds = lessonList.stream().map(Lesson::getCourseRepoId).collect(Collectors.toSet());
            ids = ids.stream().filter(repoId -> !repoIds.contains(repoId)).toList();
        }
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        List<CourseRepo> courseRepos = baseMapper.selectByIds(ids);
        courseRepos.forEach(courseRepo -> {
            FileUtil.del(CommonConstants.Paths.COURSE_REPO_PATH.resolve(courseRepo.getExpProjectPath()).toString());
            FileUtil.del(CommonConstants.Paths.COURSE_REPO_PATH.resolve(courseRepo.getExpManualPath()).toString());
        });
    }

    /**
     * 预览实验手册
     * @param filePath 文件路径
     * @param response 响应对象
     */
    public void previewManual(String filePath, HttpServletResponse response) {
        if (StrValidator.isBlank(filePath)) {
            throw BusinessException.errorByMessage("文件路径不能为空");
        }
        Path path = CommonConstants.Paths.COURSE_REPO_PATH.resolve(filePath);
        if (!Files.exists(path)) {
            throw BusinessException.errorByMessage("文件不存在");
        }
        // 设置响应头
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            String fileName = path.getFileName().toString();
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "inline; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
            Files.copy(path, outputStream);
            outputStream.flush();
        } catch (IOException e) {
            throw BusinessException.errorByMessage("文件读取失败: " + e.getMessage());
        }
    }
}
