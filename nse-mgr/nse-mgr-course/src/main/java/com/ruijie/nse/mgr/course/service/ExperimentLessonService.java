package com.ruijie.nse.mgr.course.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ruijie.nse.mgr.repository.entity.ExperimentLesson;
import com.ruijie.nse.mgr.repository.mapper.ExperimentLessonDao;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

@Service
public class ExperimentLessonService extends MPJBaseServiceImpl<ExperimentLessonDao, ExperimentLesson> {

    public void deletePhysically(LambdaQueryWrapper<ExperimentLesson> wrapper) {
        this.baseMapper.deletePhysically(wrapper);
    }

}
