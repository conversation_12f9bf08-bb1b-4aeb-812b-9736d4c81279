package com.ruijie.nse.mgr.course.service;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.exception.BusinessException;
import com.ruijie.nse.common.utils.bean.BeanCopierUtils;
import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.common.constants.CommonConstants;
import com.ruijie.nse.mgr.course.constants.CourseConstants;
import com.ruijie.nse.mgr.course.constants.ExperimentConstants;
import com.ruijie.nse.mgr.course.dto.ExperimentUrlDto;
import com.ruijie.nse.mgr.course.dto.input.ExperimentCreateInput;
import com.ruijie.nse.mgr.course.dto.input.ExperimentPreviewInput;
import com.ruijie.nse.mgr.course.dto.input.ExperimentQueryInput;
import com.ruijie.nse.mgr.course.dto.input.ExperimentRenameInput;
import com.ruijie.nse.mgr.course.dto.output.CourseRepoOutput;
import com.ruijie.nse.mgr.course.dto.output.ExperimentPageOutput;
import com.ruijie.nse.mgr.py3server.config.Py3Constants;
import com.ruijie.nse.mgr.py3server.config.Py3ServerProperties;
import com.ruijie.nse.mgr.py3server.dto.ProjectDto;
import com.ruijie.nse.mgr.py3server.feign.adaptor.handler.FeignForwardAdaptorHandler;
import com.ruijie.nse.mgr.py3server.launcher.context.Python3ServerContext;
import com.ruijie.nse.mgr.repository.entity.*;
import com.ruijie.nse.mgr.repository.entity.enums.UserType;
import com.ruijie.nse.mgr.repository.mapper.ExperimentDao;
import com.ruijie.nse.mgr.repository.mapper.LessonDao;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.dromara.hutool.core.collection.CollUtil;
import org.dromara.hutool.core.data.id.IdUtil;
import org.dromara.hutool.core.io.file.FileUtil;
import org.dromara.hutool.core.io.file.PathUtil;
import org.dromara.hutool.core.text.StrUtil;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.rmi.NoSuchObjectException;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * 实验管理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExperimentService extends ServiceImpl<ExperimentDao, Experiment> {

    private final CourseRepoService courseRepoService;
    private final FeignForwardAdaptorHandler feignForwardAdaptorHandler;
    private final Python3ServerContext python3ServerContext;
    private final LessonDao lessonDao;
    private final ExperimentLessonService experimentLessonService;
    private final Py3ServerProperties py3ServerProperties;

    public PageOutput<ExperimentPageOutput> findPage(ExperimentQueryInput pageInput) {
        Page<Experiment> pageParam = new Page<>(pageInput.getPageNumber(), pageInput.getPageSize());

        // 这里获取的是 不在exp_experiment_lesson表中的实验
        LambdaQueryChainWrapper<Experiment> lambdaQueryWrapper = this.lambdaQuery()
                .eq(Experiment::getUserId, SecurityUtils.getUserId())
                // 实验类型不为空，筛选实验类型
                .eq(StrUtil.isNotBlank(pageInput.getExperimentType()), Experiment::getExpType, pageInput.getExperimentType())
                .like(StrUtil.isNotBlank(pageInput.getExperimentName()), Experiment::getExpName, pageInput.getExperimentName())
                .between(CollUtil.isNotEmpty(pageInput.getCreatedDate()), Experiment::getCreatedDate,
                        pageInput.getCreatedDate(0),
                        pageInput.getCreatedDate(1)
                )
                .between(CollUtil.isNotEmpty(pageInput.getModifiedDate()), Experiment::getModifiedDate,
                        pageInput.getModifiedDate(0),
                        pageInput.getModifiedDate(1)
                )
                .notExists("SELECT 1 FROM exp_experiment_lesson eel WHERE eel.exp_id = id")
                .orderByDesc(Experiment::getCreatedDate);

        Page<Experiment> page = lambdaQueryWrapper.page(pageParam);
        if (page == null || page.getRecords().isEmpty()) {
            return new PageOutput<>();
        }

        List<ExperimentPageOutput> infoOutputs = BeanCopierUtils.copyListProperties(page.getRecords(), ExperimentPageOutput::new, (s, t) -> {
            t.setExperimentName(s.getExpName());
            t.setExperimentType(s.getExpType());
        });
        return new PageOutput<>(page.getTotal(), infoOutputs);
    }

    /**
     * 新建实验
     *
     * @param input
     */
    public String createExperiment(ExperimentCreateInput input) throws IOException {
        // 课程库实验
        if (input.isCourseRepoExp()) {
            return copyCourseRepoExp(input.getCourseRepoId());
        }
        // 老师布置的作业
        if (input.isLessonExp()) {
            return copyLessonExp(input.getLessonId());
        }

        // 自建空白实验
        return this.createBlankExp(input);
    }

    /**
     * 查看实验
     * @param input
     * @return
     * @throws IOException
     */
    public ExperimentUrlDto previewExperiment(ExperimentPreviewInput input) throws IOException {
        // 如果有实验id，直接获取实验id对应实验
        if(StrUtil.isNotBlank(input.getExperimentId())) {
            return buildProjectSignUrl(input.getExperimentId());
        }


        // 有lessonId 获取对应的expId
        if(StrUtil.isNotBlank(input.getLessonId())) {
            // 如果该作业，已经有了实验，直接返回
            Optional<ExperimentLesson> experimentLesson = experimentLessonService.lambdaQuery()
                    .eq(ExperimentLesson::getLessonId, input.getLessonId()).oneOpt();
            if(experimentLesson.isEmpty()) {
                throw new NoSuchObjectException("该课程实验不存在，请重新确认");
            }
            return buildProjectSignUrl(experimentLesson.get().getExpId());
        }

        throw new IllegalCallerException("请检查实验是否存在，请确认");
    }


    /**
     * 删除实验
     *
     * @param ids
     */
    public void deleteExperiment(List<String> ids) {
        // 校验当前实验是否属于自己的
        ids.forEach(this::checkExperimentBelongToUser);
        // 删除实验，先删除实验数据，再删除数据库记录
        this.lambdaQuery().in(Experiment::getId, ids).list().forEach(experiment -> feignForwardAdaptorHandler.deleteProject(experiment.getExpProjectId()));
        // 删除数据库记录
        this.removeBatchByIds(ids);
    }


    /**
     * 获取项目
     * @param projectId
     * @return
     */
    public ProjectDto getProject(String projectId) {
        if(StrUtil.isBlankIfStr(projectId)) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        return feignForwardAdaptorHandler.getProject(projectId);
    }


    /**
     * 根据用户id删除数据
     * @param userId
     * @param isPhysically  是否物理删除
     */
    public void deleteExperimentByUserId(String userId, boolean isPhysically) {
        // 获取该学生的实验数据
        List<Experiment> experiments = this.lambdaQuery().eq(Experiment::getUserId, userId)
                .in(Experiment::getIsDeleted, List.of(0, 1)).list();
        if(CollUtil.isEmpty(experiments)) {
            return ;
        }

        if(isPhysically) {
            // 获取实验id
            List<String> expIds = experiments.stream().map(Experiment::getId).toList();
            this.baseMapper.deletePhysically(Wrappers.lambdaQuery(Experiment.class).in(Experiment::getId, expIds));

            // 在删除experiment_lesson
            experimentLessonService.deletePhysically(Wrappers.lambdaQuery(ExperimentLesson.class).in(ExperimentLesson::getExpId, expIds));

            // 删除实验数据
            experiments.forEach(exp -> feignForwardAdaptorHandler.deleteProject(exp.getExpProjectId()));
            return ;
        }

        // 逻辑删除，只是删除experiment表
        this.baseMapper.delete(Wrappers.lambdaQuery(Experiment.class).eq(Experiment::getUserId, userId));
    }

    /**
     * 复制实验
     *
     * @param id
     */
    public void duplicateExperiment(String id) {
        // 校验当前实验是否属于自己的
        Experiment experiment = checkExperimentBelongToUser(id);

        String newFileName = experiment.getExpName() + "-副本";
        ProjectDto projectDto = feignForwardAdaptorHandler.duplicateProject(experiment.getExpProjectId(), newFileName);
        if (projectDto == null) {
            throw new RuntimeException("实验复制失败.");
        }
        log.info("项目复制成功，响应：{}", projectDto);
        String userId = SecurityUtils.getUserId();
        Path projectPath = Path.of("projects", userId, projectDto.getProjectId(), projectDto.getFileName());
        Experiment copied = Experiment.builder()
                .expName(newFileName)
                .expType(experiment.getExpType())
                .expProjectId(projectDto.getProjectId())
                .expProjectPath(projectPath.toString())
                .userId(userId)
                .source("复制实验（原实验ID: " + id + "）")
                .build();
        this.save(copied);
    }


    /**
     * 重命名实验名称
     *
     * @param id
     * @param input
     */
    public void renameExperiment(String id, ExperimentRenameInput input) {
        // 校验当前实验是否属于自己的
        checkExperimentBelongToUser(id);
        // 更新实验名称
        LambdaUpdateWrapper<Experiment> updateWrapper = Wrappers.lambdaUpdate(Experiment.class)
                .set(Experiment::getExpName, input.getExperimentName())
                .eq(Experiment::getId, id);
        this.update(updateWrapper);
    }


    /**
     * 打开实验
     *
     * @param id
     * @return 实验URL信息DTO
     */
    public ExperimentUrlDto buildProjectSignUrl(String id) {
        // 校验当前实验是否属于自己的
        Experiment experiment = checkExperimentBelongToUser(id);
        // 获取主机信息
        String userId = SecurityUtils.getUserId();
        SerHosts host = python3ServerContext.getHost(userId);

        // 需要open当前实验
//        boolean flag = feignForwardAdaptorHandler.openProject(experiment.getExpProjectId());
//        if(!flag) {
//            throw new RuntimeException("实验启动失败， 请联系管理员");
//        }

        /**
         * http://localhost:20000/static/web-ui/server/577138975685357569/project/8a44521c-d088-67d7-8a44-521cd08867d7
         */
        String signUrl = String.format(Py3Constants.Project.PROJECT_SIGN_URL,
                py3ServerProperties.getSsl() != null && py3ServerProperties.getSsl().isEnable() ? "https" : "http",
                host.getServerIp(), host.getServerPort(), host.getId(), experiment.getExpProjectId());
//        String signUrl = String.format(Py3Constants.Project.PROJECT_SIGN_URL, host.getId(), experiment.getExpProjectId());
        log.info("实验实验签名url: {}", signUrl);

        // 构建返回DTO
        return ExperimentUrlDto.builder()
                .signUrl(signUrl)
                .hostId(host.getId())
                .serverIp(host.getServerIp())
                .serverPort(host.getServerPort())
                .userId(userId)
                .build();
    }

    /**
     * 导出项目，这里先用gns3接口导出临时文件。在借用java调用临时文件进行下载
     *
     * @param id
     * @param response
     */
    public void exportExperiment(String id, HttpServletResponse response) {
        // 校验项目归属
        Experiment experiment = checkExperimentBelongToUser(id);

        ProjectDto projectDto = feignForwardAdaptorHandler.exportProject(experiment.getExpProjectId());
        if (projectDto == null) {
            throw new RuntimeException("实验导出失败. 项目数据为空");
        }
        // 下载文件。如果包含中文，encode下
        String encodedFileName = URLEncoder.encode(projectDto.getName(), StandardCharsets.UTF_8).replaceAll("\\+", "%20");
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=" + encodedFileName + ".nseproject");
        response.setContentType(ContentType.APPLICATION_OCTET_STREAM.getMimeType());
        Path exportTempPath = Path.of(projectDto.getProjectPath());
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            IOUtils.copy(Files.newInputStream(exportTempPath), outputStream);
        } catch (IOException e) {
            throw new RuntimeException("实验导出失败.");
        } finally {
            try {
                Files.deleteIfExists(exportTempPath);
                Files.deleteIfExists(exportTempPath.getParent());
            } catch (IOException ignored) {
                // ignore
            }
        }
    }

    private String copyCourseRepoExp(String courseRepoId) throws FileNotFoundException {
        CourseRepoOutput courseRepo = courseRepoService.getById(courseRepoId);
        Objects.requireNonNull(courseRepo, "该课程库实验不存在，请重新确认");

        // 如果该课程库，已经有了实验，直接返回
        Optional<Experiment> experimentOptional = this.lambdaQuery().eq(Experiment::getExpType, ExperimentConstants.ExpType.COURSE_REPO)
                .eq(Experiment::getUserId, SecurityUtils.getUserId())
                .eq(Experiment::getSource, "课程库实验（课程库ID：" + courseRepoId + "）")
                .oneOpt();
        if(experimentOptional.isPresent()) {
            return experimentOptional.get().getId();
        }

        Experiment experiment = new Experiment();
        String experimentId = IdUtil.getSnowflakeNextIdStr();
        experiment.setId(experimentId);
        experiment.setExpName(courseRepo.getExpName() + " - 课程库实验");
        experiment.setExpType(ExperimentConstants.ExpType.COURSE_REPO);
        experiment.setUserId(SecurityUtils.getUserId());
        experiment.setTags(List.of("课程库实验"));

        String projectId = IdUtil.getSeataSnowflakeNextIdStr();
        experiment.setExpProjectId(projectId);
        if (!courseRepo.getExpProjectPath().endsWith(CourseConstants.CourseRepo.COURSE_REPO_EXTENSION)) {
            throw new FileNotFoundException("课程库实验文件格式错误");
        }

        // 将课程库实验，同步一份到gns3服务
        Path courseRepoPath = CommonConstants.Paths.COURSE_REPO_PATH.resolve(courseRepo.getExpProjectPath());
        if(!PathUtil.exists(courseRepoPath, false)) {
            throw new FileNotFoundException("该课程库实验项目不存在");
        }
        ProjectDto projectDto = feignForwardAdaptorHandler.importProject(projectId, experiment.getExpName(), courseRepoPath);
        experiment.setExpProjectPath(projectDto.getProjectPath());
        experiment.setSource("课程库实验（课程库ID：" + courseRepoId + "）");

        save(experiment);
        return experimentId;
    }


    /**
     * 作业实验复制
     * @param lessonId
     * @return
     * @throws FileNotFoundException
     */
    private String copyLessonExp(String lessonId) throws FileNotFoundException {
        Lesson lesson = lessonDao.selectById(lessonId);
        Objects.requireNonNull(lesson, "该作业不存在，请重新确认");

        // 如果该作业，已经有了实验，直接返回
        Optional<ExperimentLesson> experimentLesson = experimentLessonService.lambdaQuery().eq(ExperimentLesson::getLessonId, lessonId).oneOpt();
        if(experimentLesson.isPresent()) {
            return experimentLesson.get().getExpId();
        }

        CourseRepo courseRepo = lesson.getCourseRepoSnapshot();
        Objects.requireNonNull(courseRepo, "该作业项目模版不存在，请重新确认");


        Experiment experiment = new Experiment();
        String experimentId = IdUtil.getSnowflakeNextIdStr();
        experiment.setId(experimentId);
        experiment.setExpName(lesson.getExpName() + " - 作业实验");
        // 老师布置的作业，也属于课程库实验
        experiment.setExpType(ExperimentConstants.ExpType.COURSE_REPO);
        experiment.setUserId(SecurityUtils.getUserId());

        String projectId = IdUtil.fastUUID();
        experiment.setExpProjectId(projectId);
        if (!courseRepo.getExpProjectPath().endsWith(CourseConstants.CourseRepo.COURSE_REPO_EXTENSION)) {
            throw new FileNotFoundException("课程库实验文件格式错误");
        }

        // 将课程库实验，同步一份到gns3服务
        Path courseRepoPath = CommonConstants.Paths.COURSE_REPO_PATH.resolve(courseRepo.getExpProjectPath());
        if(!PathUtil.exists(courseRepoPath, false)) {
            throw new FileNotFoundException("该课程库实验项目不存在");
        }
        ProjectDto projectDto = feignForwardAdaptorHandler.importProject(projectId, experiment.getExpName(), courseRepoPath);
        experiment.setExpProjectPath(projectDto.getProjectPath());
        experiment.setSource("布置作业实验（作业ID：" + lessonId + "）");
        experiment.setTags(List.of("布置作业实验"));

        this.saveExperiment(experiment, lessonId);
        return experimentId;
    }

    private void saveExperiment(Experiment experiment, String lessonId) {
        // 保存入库
        save(experiment);

        // 新增exp_experiment_lesson表
        experimentLessonService.save(ExperimentLesson.builder()
                .lessonId(lessonId)
                .expId(experiment.getId())
                .build());
    }

    private String createBlankExp(ExperimentCreateInput input) {
        String userId = SecurityUtils.getUserId();

        Experiment experiment = new Experiment();
        String experimentId = IdUtil.getSnowflakeNextIdStr();
        experiment.setId(experimentId);
        experiment.setExpName(input.getExperimentName());
        experiment.setExpType(ExperimentConstants.ExpType.CUSTOM);
        experiment.setUserId(userId);

        checkExperimentNameDuplicate(userId, input.getExperimentName());

        /**
         * 注意这里project_id必须使用uuid，gns3服务对这个ID的长度有要求
         */
        String projectId = IdUtil.fastUUID();
        experiment.setExpProjectId(projectId);

        // 创建py3 server服务实验
        ProjectDto projectDto = feignForwardAdaptorHandler.createProject(projectId, experiment.getExpName());
        experiment.setExpProjectPath(projectDto.getProjectPath());
        experiment.setSource("自建实验");
        experiment.setTags(List.of("自建空白实验"));
        // 保存入库
        save(experiment);
        return experimentId;
    }

    private void checkExperimentNameDuplicate(String userId, String experimentName) {
        if (StringUtils.isEmpty(experimentName)) {
            throw new IllegalArgumentException("实验名称不能为空！");
        }
        Long count = this.lambdaQuery().eq(Experiment::getUserId, userId)
                .eq(Experiment::getExpName, experimentName)
                .count();

        if (count > 0) {
            throw new IllegalArgumentException("实验名称已存在，不能重复创建！");
        }
    }


    private Experiment checkExperimentBelongToUser(String id) {
        Experiment experiment = baseMapper.selectById(id);
        if (experiment == null) {
            throw new IllegalArgumentException("实验不存在");
        }
        if (SecurityUtils.getUserType().equals(UserType.TEACHER.getValue())) {
            return experiment;
        }
        // 如果账号类型是学生的话
        if (!experiment.getUserId().equals(SecurityUtils.getUserId())) {
            throw new IllegalArgumentException("您当前没有权限访问该实验");
        }
        return experiment;
    }

    /**
     * 提交实验作业
     *
     * @param id            主键id
     * @param multipartFile 作业文件
     */
    @Transactional(rollbackFor = Exception.class)
    public void submitExperiment(String id, MultipartFile multipartFile) throws IOException {
        if (StringUtils.isEmpty(id) || multipartFile == null) {
            throw BusinessException.error("500", "参数不合法！");
        }
        String parentPath = "./workspace/nse-storage/experiment-assignment/" + id;
        FileUtils.deleteDirectory(new File(parentPath));
        Path filePath = Paths.get(parentPath, multipartFile.getOriginalFilename());
        lessonDao.update(Wrappers.lambdaUpdate(Lesson.class)
                .set(Lesson::getExpResultPath, filePath.toString())
                .set(Lesson::getExpResultFilename, multipartFile.getOriginalFilename())
                .set(Lesson::getSubmitStatus, "已提交")
                .set(Lesson::getSubmitDate, new Date())
                .eq(Lesson::getId, id));
        FileUtil.writeFromStream(multipartFile.getInputStream(), filePath.toFile());
    }

    /**
     * 预览实验作业
     *
     * @param id 主键id
     */
    public void previewExpResult(String id, HttpServletResponse response) throws IOException {
        Lesson lesson = lessonDao.selectOne(Wrappers.lambdaQuery(Lesson.class)
                        .select(Lesson::getExpResultPath)
                        .eq(Lesson::getId, id));
        if (lesson == null || StringUtils.isEmpty(lesson.getExpResultPath())) {
            throw BusinessException.errorByMessage("无作业提交记录！");
        }
        Path filePath = Paths.get(lesson.getExpResultPath()).normalize();
        Resource resource = new FileSystemResource(filePath);
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            IOUtils.copy(resource.getInputStream(), outputStream);
        }
    }

}
