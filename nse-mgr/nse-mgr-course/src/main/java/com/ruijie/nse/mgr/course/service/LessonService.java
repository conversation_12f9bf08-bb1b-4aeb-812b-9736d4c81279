package com.ruijie.nse.mgr.course.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.course.constants.LessonConstants;
import com.ruijie.nse.mgr.course.dto.input.ExperimentDataExportInput;
import com.ruijie.nse.mgr.course.dto.input.ExperimentDataQueryInput;
import com.ruijie.nse.mgr.course.dto.input.LessonAssignQueryInput;
import com.ruijie.nse.mgr.course.dto.input.LessonScoreInput;
import com.ruijie.nse.mgr.course.dto.output.LessonAssignPageOutput;
import com.ruijie.nse.mgr.py3server.feign.adaptor.handler.FeignForwardAdaptorHandler;
import com.ruijie.nse.mgr.repository.dto.input.ExperimentLessonDataInput;
import com.ruijie.nse.mgr.repository.dto.output.ExperimentLessonDataOutput;
import com.ruijie.nse.mgr.repository.entity.CourseLesson;
import com.ruijie.nse.mgr.repository.entity.ExperimentLesson;
import com.ruijie.nse.mgr.repository.entity.Lesson;
import com.ruijie.nse.mgr.repository.entity.User;
import com.ruijie.nse.mgr.repository.entity.enums.UserType;
import com.ruijie.nse.mgr.repository.entity.view.ViewExperimentDataStatistic;
import com.ruijie.nse.mgr.repository.mapper.ExperimentDao;
import com.ruijie.nse.mgr.repository.mapper.ExperimentLessonDao;
import com.ruijie.nse.mgr.repository.mapper.LessonDao;
import com.ruijie.nse.mgr.repository.mapper.view.ViewExperimentDataStatisticDao;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.dromara.hutool.core.collection.CollUtil;
import org.dromara.hutool.core.text.StrUtil;
import org.dromara.hutool.core.text.StrValidator;
import org.springframework.dao.PermissionDeniedDataAccessException;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.naming.NoPermissionException;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class LessonService extends MPJBaseServiceImpl<LessonDao, Lesson> {

    private final ViewExperimentDataStatisticDao viewExperimentDataStatisticDao;
    private final ExperimentLessonDao experimentLessonDao;
    private final ExperimentDao experimentDao;
    private final FeignForwardAdaptorHandler feignForwardAdaptorHandler;

    /**
     * 获取老师布置给我的作业
     *
     * @param pageInput
     * @return
     */
    public PageOutput<LessonAssignPageOutput> findAssignedPage(LessonAssignQueryInput pageInput) {
        Page<LessonAssignPageOutput> pageParam = new Page<>(pageInput.getPageNumber(), pageInput.getPageSize());
        MPJLambdaWrapper<Lesson> lambdaWrapper = JoinWrappers.lambda(Lesson.class)
                .selectAll(Lesson.class)
                .selectAs(Lesson::getExpName, LessonAssignPageOutput::getExperimentName)
                .selectAs(User::getName, LessonAssignPageOutput::getTeacherName)
                .select("course_repo_snapshot->>'expManualPath' AS experimentManualPath")
                .leftJoin(User.class, User::getId, Lesson::getTeacherId)
                .like(StringUtils.isNotEmpty(pageInput.getCourseName()), Lesson::getCourseName, pageInput.getCourseName())
                .like(StringUtils.isNotEmpty(pageInput.getExperimentName()), Lesson::getExpName, pageInput.getExperimentName())
                .like(StringUtils.isNotEmpty(pageInput.getTeacherName()), User::getName, pageInput.getTeacherName())
                .eq(StringUtils.isNotEmpty(pageInput.getSubmitStatus()), Lesson::getSubmitStatus, pageInput.getSubmitStatus())
                .eq(Lesson::getLessonType, LessonConstants.LessonType.HOMEWORK)
                .eq(Lesson::getStudentId, SecurityUtils.getUserId())
                .between(CollUtil.isNotEmpty(pageInput.getCreatedDate()), Lesson::getCreatedDate,
                        pageInput.getCreatedDate(0),
                        pageInput.getCreatedDate(1)
                )
                .between(CollUtil.isNotEmpty(pageInput.getSubmitDate()), Lesson::getSubmitDate,
                        pageInput.getSubmitDate(0),
                        pageInput.getSubmitDate(1)
                )
                .between(CollUtil.isNotEmpty(pageInput.getScore()), Lesson::getScore,
                        pageInput.getScore(0),
                        pageInput.getScore(1)
                );
        Page<LessonAssignPageOutput> page = this.selectJoinListPage(pageParam, LessonAssignPageOutput.class, lambdaWrapper);

        if (page == null || page.getRecords().isEmpty()) {
            return new PageOutput<>();
        }
        return new PageOutput<>(page.getTotal(), page.getRecords());
    }

    /**
     * 获取课程的Lesson
     * @param courseId
     * @return
     */
    public List<Lesson> getLessonsByCourseId(String courseId, String lessonType)  {
        MPJLambdaWrapper<Lesson> lambdaWrapper = JoinWrappers.lambda(Lesson.class)
                .selectAll(Lesson.class)
                .leftJoin(CourseLesson.class, CourseLesson::getLessonId, Lesson::getId)
                .eq(CourseLesson::getCourseId, courseId)
                .eq(StrValidator.isNotBlank(lessonType), Lesson::getLessonType, lessonType);
        return baseMapper.selectJoinList(lambdaWrapper);
    }


    public PageOutput<ViewExperimentDataStatistic> findExperimentDataPage(ExperimentDataQueryInput input) {
        Page<ViewExperimentDataStatistic> pageParam = new Page<>(input.getPageNumber(), input.getPageSize());
        Page<ViewExperimentDataStatistic> page = viewExperimentDataStatisticDao.selectPage(pageParam, Wrappers.<ViewExperimentDataStatistic>lambdaQuery()
                .eq(ViewExperimentDataStatistic::getCourseId, input.getCourseId())
                .eq(ViewExperimentDataStatistic::getTeacherId, SecurityUtils.getUserId())
                .like(StringUtils.isNotBlank(input.getExpName()), ViewExperimentDataStatistic::getExpName, input.getExpName())

                // 这里一定要支持单个和多个，所以不要用between。测试说的
                .ge(CollUtil.isNotEmpty(input.getTotals()), ViewExperimentDataStatistic::getTotals, CollUtil.get(input.getTotals(), 0))
                .le(CollUtil.isNotEmpty(input.getTotals()) && input.getTotals().size() > 1, ViewExperimentDataStatistic::getTotals, CollUtil.get(input.getTotals(), 1))
                // 这里一定要支持单个和多个，所以不要用between。测试说的
                .ge(CollUtil.isNotEmpty(input.getSubmits()), ViewExperimentDataStatistic::getSubmits, CollUtil.get(input.getSubmits(), 0))
                .le(CollUtil.isNotEmpty(input.getSubmits()) && input.getSubmits().size() > 1, ViewExperimentDataStatistic::getSubmits, CollUtil.get(input.getSubmits(), 1))
                // 这里一定要支持单个和多个，所以不要用between。测试说的
                .ge(CollUtil.isNotEmpty(input.getPercents()), ViewExperimentDataStatistic::getSubmitRatePercent, CollUtil.get(input.getPercents(), 0))
                .le(CollUtil.isNotEmpty(input.getPercents()) && input.getPercents().size() > 1, ViewExperimentDataStatistic::getSubmitRatePercent, CollUtil.get(input.getPercents(), 1)));

        if (page == null || page.getRecords().isEmpty()) {
            return new PageOutput<>();
        }
        return new PageOutput<>(page.getTotal(), page.getRecords());
    }


    /**
     * 获取实验数据详情
     * @param input
     * @return
     */
    public PageOutput<ExperimentLessonDataOutput> findExperimentDataDetail(ExperimentLessonDataInput input) {
        Page<ExperimentLessonDataOutput> pageParam = new Page<>(input.getPageNumber(), input.getPageSize());

        String userId = SecurityUtils.getUserId();
        Page<ExperimentLessonDataOutput> page = this.baseMapper.listLessonData(pageParam, input, userId);

        if (page == null || page.getRecords().isEmpty()) {
            return new PageOutput<>();
        }
        return new PageOutput<>(page.getTotal(), page.getRecords());
    }

    /**
     * 提交评分
     * @param input
     */
    public void submitScore(LessonScoreInput input) {
        String userId = SecurityUtils.getUserId();

        checkHasPermission(List.of(input.getLessonId()));

        // 评分
        this.lambdaUpdate()
                .set(Lesson::getScore, input.getScore())
                .eq(Lesson::getId, input.getLessonId())
                .eq(Lesson::getTeacherId, userId)
                .update();
    }


    /**
     * 根据lesson id删除作业数据
     * 这里老师操作，不删除学生实验数据
     *
     * @param lessonIds
     * @throws NoPermissionException
     */
    @Transactional
    public void deleteLessons(List<String> lessonIds) {
        // 校验是否有权限
        if(CollUtil.isEmpty(lessonIds)) {
            return ;
        }
        checkHasPermission(lessonIds);

        // 删除lesson
        this.removeByIds(lessonIds);
        // 删除exp_experiment_lesson
        LambdaQueryWrapper<ExperimentLesson> queryWrapper = Wrappers.lambdaQuery(ExperimentLesson.class)
                .in(ExperimentLesson::getLessonId, lessonIds);
        experimentLessonDao.delete(queryWrapper);
    }

    public void deleteLessonsBatchNo(List<String> lessonBatchNos) {
        if(CollUtil.isEmpty(lessonBatchNos)) {
            throw new IllegalCallerException("作业参数有误，请确认");
        }
        // 根据批次号获取lesson
        List<String> lessonIds = this.lambdaQuery().in(Lesson::getRemark, lessonBatchNos).list().stream().map(Lesson::getId).toList();

        // 删除作业
        deleteLessons(lessonIds);
    }


    /**
     * 导出实验数据到Excel
     * @param input 查询条件
     * @param response HTTP响应
     */
    public void exportExperimentDataToExcel(ExperimentDataExportInput input, HttpServletResponse response) throws IOException {
        // 获取实验数据
        String currentUserId = SecurityUtils.getUserId();
        List<ViewExperimentDataStatistic> statisticList = viewExperimentDataStatisticDao.selectList(Wrappers.lambdaQuery(ViewExperimentDataStatistic.class)
                .eq(ViewExperimentDataStatistic::getLessonBatchNo, input.getLessonBatchNo())
                .eq(ViewExperimentDataStatistic::getTeacherId, currentUserId)
        );
        if(CollUtil.isEmpty(statisticList)) {
            throw new IllegalCallerException("该实验统计数据不存在");
        }

        try (Workbook workbook = new XSSFWorkbook();
                    ServletOutputStream outputStream = response.getOutputStream()) {

            // 实验数据
            ViewExperimentDataStatistic experimentData = statisticList.getFirst();

            ExperimentLessonDataInput dataInput = new ExperimentLessonDataInput();
            dataInput.setLessonBatchNo(input.getLessonBatchNo());
            List<ExperimentLessonDataOutput> lessonData = this.baseMapper.listLessonData(dataInput, currentUserId);

            // 创建工作簿
            Sheet sheet = workbook.createSheet("实验数据");

            // 创建样式
            CellStyle titleStyle = createTitleStyle(workbook);
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);

            int rowIndex = 0;

            // 第1行：实验名称
            Row titleRow = sheet.createRow(rowIndex++);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue("实验名称: ");
            titleCell.setCellStyle(titleStyle);
            Cell titleNameCell = titleRow.createCell(1);
            titleNameCell.setCellValue(experimentData.getExpName());
            titleNameCell.setCellStyle(titleStyle);
            sheet.addMergedRegion(new CellRangeAddress(
                    rowIndex - 1, rowIndex - 1, 1, 5
            ));

            // 第2行：统计信息
            Row statsRow = sheet.createRow(rowIndex++);

            statsRow.createCell(0).setCellValue("总人数");
            statsRow.createCell(1).setCellValue(experimentData.getTotals());
            statsRow.createCell(2).setCellValue("提交人数");
            statsRow.createCell(3).setCellValue(experimentData.getSubmits());
            statsRow.createCell(4).setCellValue("提交人数占比");
            statsRow.createCell(5).setCellValue(experimentData.getSubmitRatePercent() + "%");

            // 第3行：表头
            Row headerRow = sheet.createRow(rowIndex++);
            String[] headers = {"学号", "姓名", "班级", "提交状态", "提交时间", "分数"};
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // 数据行
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            for (ExperimentLessonDataOutput data : lessonData) {
                Row dataRow = sheet.createRow(rowIndex++);

                dataRow.createCell(0).setCellValue(data.getAccount());
                dataRow.createCell(1).setCellValue(data.getStudentName());
                dataRow.createCell(2).setCellValue(data.getOfficialName());
                dataRow.createCell(3).setCellValue(data.getSubmitStatus());
                dataRow.createCell(4).setCellValue(data.getSubmitDate() != null ? formatter.format(data.getSubmitDate()) : "");
                dataRow.createCell(5).setCellValue(data.getScore() != null ? String.valueOf(data.getScore()) : "-");

                // 应用数据样式
                for (int i = 0; i < 6; i++) {
                    dataRow.getCell(i).setCellStyle(dataStyle);
                }
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                // 设置列宽最小宽度为150px
                sheet.setColumnWidth(i, Math.max(256 * 15, sheet.getColumnWidth(i)));
            }

            // 设置响应头
            String fileName = URLEncoder.encode(experimentData.getExpName() + "_实验数据_" +
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".xlsx", StandardCharsets.UTF_8);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);

            // 写入响应流
            workbook.write(outputStream);
            outputStream.flush();
        }
    }
    
    /**
     * 创建标题样式
     */
    private CellStyle createTitleStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 14);
        style.setFont(font);
        return style;
    }
    
    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.LIGHT_YELLOW.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }
    
    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }

    private void checkHasPermission(List<String> lessonIds) {
        String userId = SecurityUtils.getUserId();
        String userType = SecurityUtils.getUserType();
        if(!userType.equalsIgnoreCase(UserType.TEACHER.getValue())) {
            throw new PermissionDeniedDataAccessException("只有老师才能进行操作", null);
        }

        Long count = this.lambdaQuery().in(Lesson::getId, lessonIds).eq(Lesson::getTeacherId, userId).count();
        if(count != lessonIds.size()) {
            throw new PermissionDeniedDataAccessException("当前有部分数据，您没有操作权限！请检查！", null);
        }
    }
}
