package com.ruijie.nse.mgr.jobs.job;

import com.ruijie.nse.mgr.jobs.config.JobsConfig;
import com.ruijie.nse.mgr.py3server.service.SerHostsService;
import com.ruijie.nse.mgr.repository.entity.SerHosts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * PID校验定时任务
 * 定时校验ser_hosts表中的pid是否存在，如果不存在则清除该条记录
 * 校验规则：
 * 1. 检查ser_hosts表中的pid是否存在
 * 2. 如果不存在，则清除该条记录
 * 3. 如果存在，则检查进程是否存在
 * 4. 如果进程不存在，则清除该条记录
 * 5. 如果进程存在，则不做处理
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "nse.jobs.pid-validation", name = "enabled", havingValue = "true", matchIfMissing = true)
public class PidValidationJob {

    private final SerHostsService serHostsService;
    private final JobsConfig jobsConfig;

    /**
     * 定时校验PID任务
     * 使用配置文件中的cron表达式执行
     */
    @Scheduled(cron = "#{@jobsConfig.pidValidation.cron}")
    public void validateAndCleanupPids() {
        if (!jobsConfig.getPidValidation().isEnabled()) {
            log.debug("PID校验任务已禁用，跳过执行");
            return;
        }

        log.info("开始执行PID校验任务...");
        
        try {
            // 获取所有ser_hosts记录
            List<SerHosts> allHosts = serHostsService.getAllHosts();
            
            if (allHosts.isEmpty()) {
                log.info("ser_hosts表中没有记录，任务结束");
                return;
            }
            
            log.info("找到 {} 条ser_hosts记录，开始校验PID", allHosts.size());
            
            int cleanedCount = 0;
            
            for (SerHosts host : allHosts) {
                long pid = host.getPid();
                String userId = host.getUserId();
                
                if (pid <= 0) {
                    log.warn("用户 {} 的记录中PID无效: {}, 跳过校验", userId, pid);
                    continue;
                }
                
                // 检查进程是否存在
                if (!isProcessAlive(pid)) {
                    log.info("用户 {} 的进程 {} 不存在，清理记录", userId, pid);
                    
                    try {
                        serHostsService.removeByUserId(userId);
                        cleanedCount++;
                        log.info("成功清理用户 {} 的无效记录，PID: {}", userId, pid);
                    } catch (Exception e) {
                        log.error("清理用户 {} 的记录失败，PID: {}", userId, pid, e);
                    }
                } else {
                    log.debug("用户 {} 的进程 {} 正常运行", userId, pid);
                }
            }
            
            log.info("PID校验任务完成，共清理 {} 条无效记录", cleanedCount);
            
        } catch (Exception e) {
            log.error("PID校验任务执行失败", e);
        }
    }

    /**
     * 检查进程是否存在
     * 
     * @param pid 进程ID
     * @return true表示进程存在，false表示进程不存在
     */
    private boolean isProcessAlive(long pid) {
        try {
            // 使用ProcessHandle检查进程是否存在
            return ProcessHandle.of(pid)
                    .map(ProcessHandle::isAlive)
                    .orElse(false);
        } catch (Exception e) {
            log.debug("检查进程 {} 状态时发生异常: {}", pid, e.getMessage());
            return false;
        }
    }

    /**
     * 获取任务配置信息
     * 
     * @return 任务配置描述
     */
    public String getJobInfo() {
        JobsConfig.PidValidation config = jobsConfig.getPidValidation();
        return String.format("PID校验任务 - 启用: %s, Cron: %s, 描述: %s", 
                config.isEnabled(), config.getCron(), config.getDescription());
    }
}