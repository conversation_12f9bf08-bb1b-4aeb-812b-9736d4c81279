package com.ruijie.nse.mgr.jobs.job;

import com.ruijie.nse.mgr.jobs.config.JobsConfig;
import com.ruijie.nse.mgr.py3server.service.SerHostsService;
import com.ruijie.nse.mgr.py3server.util.PythonCommandUtil;
import com.ruijie.nse.mgr.repository.entity.SerHosts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Python进程清理定时任务
 * 定时清理不在ser_hosts表中的Python NSE服务进程
 * 清理规则：
 * 1. 通过ps命令查找所有python3 -m nseserver.main进程
 * 2. 获取ser_hosts表中所有有效的PID
 * 3. 对比找出不在表中的进程PID
 * 4. 清理这些孤立的进程
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "nse.jobs.python-process-cleanup", name = "enabled", havingValue = "true", matchIfMissing = true)
public class PythonProcessCleanupJob {

    private final SerHostsService serHostsService;
    private final JobsConfig jobsConfig;

    /**
     * 定时清理Python进程任务
     * 使用配置文件中的cron表达式执行
     */
    @Scheduled(cron = "#{@jobsConfig.pythonProcessCleanup.cron}")
    public void cleanupOrphanedPythonProcesses() {
        if (!jobsConfig.getPythonProcessCleanup().isEnabled()) {
            log.debug("Python进程清理任务已禁用，跳过执行");
            return;
        }

        log.info("开始执行Python进程清理任务...");
        
        try {
            // 1. 获取所有Python NSE服务进程
            List<ProcessInfo> pythonProcesses = findPythonNseProcesses();
            
            if (pythonProcesses.isEmpty()) {
                log.info("未找到Python NSE服务进程，任务结束");
                return;
            }
            
            log.info("找到 {} 个Python NSE服务进程", pythonProcesses.size());
            
            // 2. 获取ser_hosts表中所有有效的PID
            List<SerHosts> allHosts = serHostsService.getAllHosts();
            Set<Long> validPids = allHosts.stream()
                    .map(SerHosts::getPid)
                    .filter(pid -> pid > 0)
                    .collect(Collectors.toSet());
            
            log.info("ser_hosts表中有效PID数量: {}", validPids.size());
            
            // 3. 找出孤立的进程（不在ser_hosts表中的进程）
            List<ProcessInfo> orphanedProcesses = pythonProcesses.stream()
                    .filter(process -> !validPids.contains(process.getPid()))
                    .toList();
            
            if (orphanedProcesses.isEmpty()) {
                log.info("未发现孤立的Python NSE服务进程，任务结束");
                return;
            }
            
            log.info("发现 {} 个孤立的Python NSE服务进程，开始清理", orphanedProcesses.size());
            
            // 4. 清理孤立的进程
            int cleanedCount = 0;
            for (ProcessInfo process : orphanedProcesses) {
                try {
                    if (killProcess(process.getPid())) {
                        cleanedCount++;
                        log.info("成功清理孤立进程 - PID: {}, 命令: {}", 
                                process.getPid(), process.getCommand());
                    } else {
                        log.warn("清理孤立进程失败 - PID: {}, 命令: {}", 
                                process.getPid(), process.getCommand());
                    }
                } catch (Exception e) {
                    log.error("清理孤立进程异常 - PID: {}, 命令: {}", 
                            process.getPid(), process.getCommand(), e);
                }
            }
            
            log.info("Python进程清理任务完成，共清理 {} 个孤立进程", cleanedCount);
            
        } catch (Exception e) {
            log.error("Python进程清理任务执行失败", e);
        }
    }

    /**
     * 查找所有Python NSE服务进程
     * 
     * @return Python NSE服务进程列表
     */
    private List<ProcessInfo> findPythonNseProcesses() {
        List<ProcessInfo> processes = new ArrayList<>();
        
        try {
            // 执行ps命令查找python3 -m nseserver.main进程
            ProcessBuilder processBuilder = new ProcessBuilder("ps", "-ef");
            Process process = processBuilder.start();
            
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream()))) {
                
                String line;
                while ((line = reader.readLine()) != null) {
                    // 使用PythonCommandUtil检查是否为NSE Python进程
                    if (PythonCommandUtil.isNsePythonProcess(line)) {
                        ProcessInfo processInfo = parseProcessLine(line);
                        if (processInfo != null) {
                            processes.add(processInfo);
                            log.debug("发现Python NSE进程: PID={}, 命令={}",
                                    processInfo.getPid(), processInfo.getCommand());
                        }
                    }
                }
            }
            
            int exitCode = process.waitFor();
            if (exitCode != 0) {
                log.warn("ps命令执行异常，退出码: {}", exitCode);
            }
            
        } catch (IOException | InterruptedException e) {
            log.error("执行ps命令失败", e);
        }
        
        return processes;
    }

    /**
     * 解析ps命令输出的进程行
     * 
     * @param line ps命令输出的一行
     * @return 进程信息，解析失败返回null
     */
    private ProcessInfo parseProcessLine(String line) {
        try {
            // ps -ef输出格式: UID PID PPID C STIME TTY TIME CMD
            String[] parts = line.trim().split("\\s+", 8);
            
            if (parts.length >= 8) {
                long pid = Long.parseLong(parts[1]);
                String command = parts[7]; // CMD部分
                
                return new ProcessInfo(pid, command);
            }
        } catch (NumberFormatException e) {
            log.debug("解析进程行失败: {}", line, e);
        }
        
        return null;
    }

    /**
     * 终止指定PID的进程
     * 
     * @param pid 进程ID
     * @return 是否成功终止
     */
    private boolean killProcess(long pid) {
        try {
            // 首先尝试优雅终止 (SIGTERM)
            ProcessBuilder processBuilder = new ProcessBuilder("kill", String.valueOf(pid));
            Process process = processBuilder.start();
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                log.debug("成功发送SIGTERM信号到进程 {}", pid);
                
                // 等待一段时间让进程优雅退出
                Thread.sleep(2000);
                
                // 检查进程是否还存在
                if (!isProcessAlive(pid)) {
                    return true;
                }
                
                // 如果进程仍然存在，使用强制终止 (SIGKILL)
                log.warn("进程 {} 未响应SIGTERM，使用SIGKILL强制终止", pid);
                processBuilder = new ProcessBuilder("kill", "-9", String.valueOf(pid));
                process = processBuilder.start();
                exitCode = process.waitFor();
                
                return exitCode == 0;
            } else {
                log.warn("发送SIGTERM信号到进程 {} 失败，退出码: {}", pid, exitCode);
                return false;
            }
            
        } catch (IOException | InterruptedException e) {
            log.error("终止进程 {} 时发生异常", pid, e);
            return false;
        }
    }

    /**
     * 检查进程是否存活
     * 
     * @param pid 进程ID
     * @return true表示进程存在，false表示进程不存在
     */
    private boolean isProcessAlive(long pid) {
        try {
            // 使用ProcessHandle检查进程是否存在
            return ProcessHandle.of(pid)
                    .map(ProcessHandle::isAlive)
                    .orElse(false);
        } catch (Exception e) {
            log.debug("检查进程 {} 状态时发生异常: {}", pid, e.getMessage());
            return false;
        }
    }

    /**
     * 获取任务配置信息
     * 
     * @return 任务配置描述
     */
    public String getJobInfo() {
        JobsConfig.PythonProcessCleanup config = jobsConfig.getPythonProcessCleanup();
        return String.format("Python进程清理任务 - 启用: %s, Cron: %s, 描述: %s", 
                config.isEnabled(), config.getCron(), config.getDescription());
    }

    /**
     * 进程信息内部类
     */
    private static class ProcessInfo {
        private final long pid;
        private final String command;

        public ProcessInfo(long pid, String command) {
            this.pid = pid;
            this.command = command;
        }

        public long getPid() {
            return pid;
        }

        public String getCommand() {
            return command;
        }
    }
}
