package com.ruijie.nse.mgr.jobs.job;

import com.ruijie.nse.common.utils.system.OSUtil;
import com.ruijie.nse.mgr.jobs.config.JobsConfig;
import com.ruijie.nse.mgr.py3server.service.SerHostsService;
import com.ruijie.nse.mgr.py3server.util.PythonCommandUtil;
import com.ruijie.nse.mgr.repository.entity.SerHosts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import oshi.SystemInfo;
import oshi.software.os.OSProcess;
import oshi.software.os.OperatingSystem;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Python进程清理定时任务
 * 定时清理不在ser_hosts表中的Python NSE服务进程
 * 支持Windows和Linux/Mac系统的跨平台进程管理
 * 清理规则：
 * 1. 通过OSHI库查找所有python3 -m nseserver.main进程（跨平台）
 * 2. 获取ser_hosts表中所有有效的PID
 * 3. 对比找出不在表中的进程PID
 * 4. 使用ProcessHandle API清理这些孤立的进程（跨平台）
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "nse.jobs.python-process-cleanup", name = "enabled", havingValue = "true", matchIfMissing = true)
public class PythonProcessCleanupJob {

    private final SerHostsService serHostsService;
    private final JobsConfig jobsConfig;

    /**
     * 定时清理Python进程任务
     * 使用配置文件中的cron表达式执行
     */
    @Scheduled(cron = "#{@jobsConfig.pythonProcessCleanup.cron}")
    public void cleanupOrphanedPythonProcesses() {
        if (!jobsConfig.getPythonProcessCleanup().isEnabled()) {
            log.debug("Python进程清理任务已禁用，跳过执行");
            return;
        }

        log.info("开始执行Python进程清理任务... (操作系统: {})", OSUtil.getOSType().getDisplayName());

        try {
            // 1. 获取所有Python NSE服务进程
            List<ProcessInfo> pythonProcesses = findPythonNseProcesses();

            if (pythonProcesses.isEmpty()) {
                log.info("未找到Python NSE服务进程，任务结束");
                return;
            }

            log.info("找到 {} 个Python NSE服务进程", pythonProcesses.size());
            
            // 2. 获取ser_hosts表中所有有效的PID
            List<SerHosts> allHosts = serHostsService.getAllHosts();
            Set<Long> validPids = allHosts.stream()
                    .map(SerHosts::getPid)
                    .filter(pid -> pid > 0)
                    .collect(Collectors.toSet());
            
            log.info("ser_hosts表中有效PID数量: {}", validPids.size());
            
            // 3. 找出孤立的进程（不在ser_hosts表中的进程）
            List<ProcessInfo> orphanedProcesses = pythonProcesses.stream()
                    .filter(process -> !validPids.contains(process.getPid()))
                    .toList();
            
            if (orphanedProcesses.isEmpty()) {
                log.info("未发现孤立的Python NSE服务进程，任务结束");
                return;
            }
            
            log.info("发现 {} 个孤立的Python NSE服务进程，开始清理", orphanedProcesses.size());
            
            // 4. 清理孤立的进程
            int cleanedCount = 0;
            for (ProcessInfo process : orphanedProcesses) {
                try {
                    if (killProcess(process.getPid())) {
                        cleanedCount++;
                        log.info("成功清理孤立进程 - PID: {}, 命令: {}", 
                                process.getPid(), process.getCommand());
                    } else {
                        log.warn("清理孤立进程失败 - PID: {}, 命令: {}", 
                                process.getPid(), process.getCommand());
                    }
                } catch (Exception e) {
                    log.error("清理孤立进程异常 - PID: {}, 命令: {}", 
                            process.getPid(), process.getCommand(), e);
                }
            }
            
            log.info("Python进程清理任务完成，共清理 {} 个孤立进程", cleanedCount);
            
        } catch (Exception e) {
            log.error("Python进程清理任务执行失败", e);
        }
    }

    /**
     * 查找所有Python NSE服务进程
     * 使用OSHI库实现跨平台进程查找
     *
     * @return Python NSE服务进程列表
     */
    private List<ProcessInfo> findPythonNseProcesses() {
        List<ProcessInfo> processes = new ArrayList<>();

        try {
            log.debug("使用OSHI库查找Python NSE进程...");

            // 使用OSHI获取系统信息
            SystemInfo systemInfo = new SystemInfo();
            OperatingSystem os = systemInfo.getOperatingSystem();

            // 获取所有进程
            List<OSProcess> allProcesses = os.getProcesses();
            log.debug("系统中共有 {} 个进程", allProcesses.size());

            // 筛选Python NSE进程
            for (OSProcess osProcess : allProcesses) {
                try {
                    String commandLine = osProcess.getCommandLine();

                    // 使用PythonCommandUtil检查是否为NSE Python进程
                    if (PythonCommandUtil.isNsePythonProcess(commandLine)) {
                        long pid = osProcess.getProcessID();
                        ProcessInfo processInfo = new ProcessInfo(pid, commandLine);
                        processes.add(processInfo);

                        log.debug("发现Python NSE进程: PID={}, 命令={}",
                                processInfo.getPid(), processInfo.getCommand());
                    }
                } catch (Exception e) {
                    // 某个进程信息获取失败，继续处理其他进程
                    log.debug("获取进程信息失败: {}", e.getMessage());
                }
            }

            log.debug("OSHI库查找完成，找到 {} 个Python NSE进程", processes.size());

        } catch (Exception e) {
            log.error("使用OSHI库查找Python进程失败", e);
        }

        return processes;
    }



    /**
     * 终止指定PID的进程
     * 使用Java ProcessHandle API实现跨平台进程终止
     *
     * @param pid 进程ID
     * @return 是否成功终止
     */
    private boolean killProcess(long pid) {
        try {
            // 使用ProcessHandle获取进程
            ProcessHandle processHandle = ProcessHandle.of(pid).orElse(null);

            if (processHandle == null) {
                log.debug("进程 {} 不存在或已终止", pid);
                return true;
            }

            if (!processHandle.isAlive()) {
                log.debug("进程 {} 已经不存在", pid);
                return true;
            }

            log.debug("尝试优雅终止进程 {} (操作系统: {})", pid, OSUtil.getOSType().getDisplayName());

            // 首先尝试优雅终止
            boolean destroyed = processHandle.destroy();

            if (destroyed) {
                log.debug("成功发送终止信号到进程 {}", pid);

                // 等待一段时间让进程优雅退出
                Thread.sleep(2000);

                // 检查进程是否还存在
                if (!processHandle.isAlive()) {
                    log.debug("进程 {} 已优雅退出", pid);
                    return true;
                }

                // 如果进程仍然存在，使用强制终止
                log.warn("进程 {} 未响应优雅终止信号，使用强制终止", pid);
                boolean forceDestroyed = processHandle.destroyForcibly();

                if (forceDestroyed) {
                    // 再次等待确认进程终止
                    Thread.sleep(1000);
                    boolean stillAlive = processHandle.isAlive();

                    if (!stillAlive) {
                        log.debug("进程 {} 已强制终止", pid);
                        return true;
                    } else {
                        log.warn("进程 {} 强制终止失败，进程仍然存在", pid);
                        return false;
                    }
                } else {
                    log.warn("无法强制终止进程 {}", pid);
                    return false;
                }
            } else {
                log.warn("无法发送终止信号到进程 {}", pid);
                return false;
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("终止进程 {} 时被中断", pid, e);
            return false;
        } catch (Exception e) {
            log.error("终止进程 {} 时发生异常", pid, e);
            return false;
        }
    }

    /**
     * 检查进程是否存活
     * 使用ProcessHandle API实现跨平台进程状态检查
     *
     * @param pid 进程ID
     * @return true表示进程存在，false表示进程不存在
     */
    private boolean isProcessAlive(long pid) {
        try {
            // 使用ProcessHandle检查进程是否存在
            return ProcessHandle.of(pid)
                    .map(ProcessHandle::isAlive)
                    .orElse(false);
        } catch (Exception e) {
            log.debug("检查进程 {} 状态时发生异常: {}", pid, e.getMessage());
            return false;
        }
    }

    /**
     * 获取任务配置信息
     * 
     * @return 任务配置描述
     */
    public String getJobInfo() {
        JobsConfig.PythonProcessCleanup config = jobsConfig.getPythonProcessCleanup();
        return String.format("Python进程清理任务 - 启用: %s, Cron: %s, 描述: %s", 
                config.isEnabled(), config.getCron(), config.getDescription());
    }

    /**
     * 进程信息内部类
     */
    private static class ProcessInfo {
        private final long pid;
        private final String command;

        public ProcessInfo(long pid, String command) {
            this.pid = pid;
            this.command = command;
        }

        public long getPid() {
            return pid;
        }

        public String getCommand() {
            return command;
        }
    }
}
