//package com.ruijie.nse.mgr.jobs.job;
//
//import com.ruijie.nse.mgr.jobs.config.JobsConfig;
//import com.ruijie.nse.mgr.py3server.service.SerHostsService;
//import com.ruijie.nse.mgr.repository.entity.SerHosts;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.List;
//
//import static org.mockito.Mockito.*;
//
///**
// * PidValidationJob 测试类
// *
// * <AUTHOR> Team
// * @since 2025-01-06
// */
//@ExtendWith(MockitoExtension.class)
//class PidValidationJobTest {
//
//    @Mock
//    private SerHostsService serHostsService;
//
//    @Mock
//    private JobsConfig jobsConfig;
//
//    @Mock
//    private JobsConfig.PidValidation pidValidationConfig;
//
//    @InjectMocks
//    private PidValidationJob pidValidationJob;
//
//    @BeforeEach
//    void setUp() {
//        when(jobsConfig.getPidValidation()).thenReturn(pidValidationConfig);
//        when(pidValidationConfig.isEnabled()).thenReturn(true);
//        when(pidValidationConfig.getCron()).thenReturn("0 */5 * * * ?");
//        when(pidValidationConfig.getDescription()).thenReturn("测试任务");
//    }
//
//    @Test
//    void testValidateAndCleanupPids_WhenTaskDisabled() {
//        // Given
//        when(pidValidationConfig.isEnabled()).thenReturn(false);
//
//        // When
//        pidValidationJob.validateAndCleanupPids();
//
//        // Then
//        verify(serHostsService, never()).getAllHosts();
//    }
//
//    @Test
//    void testValidateAndCleanupPids_WhenNoHosts() {
//        // Given
//        when(serHostsService.getAllHosts()).thenReturn(Collections.emptyList());
//
//        // When
//        pidValidationJob.validateAndCleanupPids();
//
//        // Then
//        verify(serHostsService).getAllHosts();
//        verify(serHostsService, never()).removeById(any());
//    }
//
//    @Test
//    void testValidateAndCleanupPids_WithValidAndInvalidPids() {
//        // Given
//        SerHosts validHost = createSerHosts(1L, "user1", getCurrentProcessPid());
//        SerHosts invalidHost = createSerHosts(2L, "user2", 99999L); // 不存在的PID
//        SerHosts invalidPidHost = createSerHosts(3L, "user3", -1L); // 无效PID
//
//        List<SerHosts> hosts = Arrays.asList(validHost, invalidHost, invalidPidHost);
//        when(serHostsService.getAllHosts()).thenReturn(hosts);
//
//        // When
//        pidValidationJob.validateAndCleanupPids();
//
//        // Then
//        verify(serHostsService).getAllHosts();
//        verify(serHostsService).removeById(2L); // 只清理不存在的PID
//        verify(serHostsService, never()).removeById(1L); // 不清理有效PID
//        verify(serHostsService, never()).removeById(3L); // 无效PID跳过处理
//    }
//
//    @Test
//    void testGetJobInfo() {
//        // When
//        String jobInfo = pidValidationJob.getJobInfo();
//
//        // Then
//        assert jobInfo.contains("PID校验任务");
//        assert jobInfo.contains("true");
//        assert jobInfo.contains("0 */5 * * * ?");
//        assert jobInfo.contains("测试任务");
//    }
//
//    /**
//     * 创建SerHosts测试对象
//     */
//    private SerHosts createSerHosts(Long id, String userId, long pid) {
//        SerHosts host = new SerHosts();
//        host.setId(id);
//        host.setUserId(userId);
//        host.setPid(pid);
//        host.setServerIp("127.0.0.1");
//        host.setServerPort(8080);
//        return host;
//    }
//
//    /**
//     * 获取当前进程PID（用于测试有效PID）
//     */
//    private long getCurrentProcessPid() {
//        return ProcessHandle.current().pid();
//    }
//}