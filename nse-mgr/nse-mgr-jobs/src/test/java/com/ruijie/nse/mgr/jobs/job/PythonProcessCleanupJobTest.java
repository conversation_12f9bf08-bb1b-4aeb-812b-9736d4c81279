//package com.ruijie.nse.mgr.jobs.job;
//
//import com.ruijie.nse.mgr.jobs.config.JobsConfig;
//import com.ruijie.nse.mgr.py3server.service.SerHostsService;
//import com.ruijie.nse.mgr.repository.entity.SerHosts;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.util.Arrays;
//import java.util.Collections;
//import java.util.List;
//
//import static org.mockito.Mockito.*;
//
///**
// * Python进程清理定时任务测试类
// *
// * <AUTHOR> Team
// * @since 2025-01-06
// */
//@ExtendWith(MockitoExtension.class)
//class PythonProcessCleanupJobTest {
//
//    @Mock
//    private SerHostsService serHostsService;
//
//    @Mock
//    private JobsConfig jobsConfig;
//
//    @Mock
//    private JobsConfig.PythonProcessCleanup pythonProcessCleanupConfig;
//
//    @InjectMocks
//    private PythonProcessCleanupJob pythonProcessCleanupJob;
//
//    @BeforeEach
//    void setUp() {
//        when(jobsConfig.getPythonProcessCleanup()).thenReturn(pythonProcessCleanupConfig);
//    }
//
//    @Test
//    void testCleanupOrphanedPythonProcesses_WhenDisabled() {
//        // Given
//        when(pythonProcessCleanupConfig.isEnabled()).thenReturn(false);
//
//        // When
//        pythonProcessCleanupJob.cleanupOrphanedPythonProcesses();
//
//        // Then
//        verify(serHostsService, never()).getAllHosts();
//    }
//
//    @Test
//    void testCleanupOrphanedPythonProcesses_WhenEnabled() {
//        // Given
//        when(pythonProcessCleanupConfig.isEnabled()).thenReturn(true);
//
//        // 模拟ser_hosts表中的数据
//        SerHosts host1 = new SerHosts();
//        host1.setPid(12345L);
//        host1.setUserId("user1");
//
//        SerHosts host2 = new SerHosts();
//        host2.setPid(12346L);
//        host2.setUserId("user2");
//
//        List<SerHosts> hosts = Arrays.asList(host1, host2);
//        when(serHostsService.getAllHosts()).thenReturn(hosts);
//
//        // When
//        pythonProcessCleanupJob.cleanupOrphanedPythonProcesses();
//
//        // Then
//        verify(serHostsService).getAllHosts();
//    }
//
//    @Test
//    void testCleanupOrphanedPythonProcesses_WithEmptyHosts() {
//        // Given
//        when(pythonProcessCleanupConfig.isEnabled()).thenReturn(true);
//        when(serHostsService.getAllHosts()).thenReturn(Collections.emptyList());
//
//        // When
//        pythonProcessCleanupJob.cleanupOrphanedPythonProcesses();
//
//        // Then
//        verify(serHostsService).getAllHosts();
//    }
//
//    @Test
//    void testGetJobInfo() {
//        // Given
//        when(pythonProcessCleanupConfig.isEnabled()).thenReturn(true);
//        when(pythonProcessCleanupConfig.getCron()).thenReturn("0 */10 * * * ?");
//        when(pythonProcessCleanupConfig.getDescription()).thenReturn("定时清理不在ser_hosts表中的Python NSE服务进程");
//
//        // When
//        String jobInfo = pythonProcessCleanupJob.getJobInfo();
//
//        // Then
//        assert jobInfo.contains("Python进程清理任务");
//        assert jobInfo.contains("启用: true");
//        assert jobInfo.contains("Cron: 0 */10 * * * ?");
//        assert jobInfo.contains("描述: 定时清理不在ser_hosts表中的Python NSE服务进程");
//    }
//
//    @Test
//    void testCleanupOrphanedPythonProcesses_WithException() {
//        // Given
//        when(pythonProcessCleanupConfig.isEnabled()).thenReturn(true);
//        when(serHostsService.getAllHosts()).thenThrow(new RuntimeException("Database error"));
//
//        // When & Then - 应该不抛出异常，而是记录错误日志
//        pythonProcessCleanupJob.cleanupOrphanedPythonProcesses();
//
//        verify(serHostsService).getAllHosts();
//    }
//}
