package com.ruijie.nse.mgr.launcher;

import com.ruijie.nse.mgr.common.context.LicenseContext;
import com.ruijie.nse.mgr.py3server.launcher.context.EnvironmentContext;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.env.ConfigurableEnvironment;

import java.io.IOException;
import java.util.Arrays;

@Slf4j
@SpringBootApplication(scanBasePackages = "com.ruijie",
        exclude = {SecurityAutoConfiguration.class})
@MapperScan("com.ruijie.**.mapper")
@EnableAspectJAutoProxy
public class NseMgrApplication {


    public static void main(String[] args) throws IOException, InterruptedException {
        //关闭热部署
        System.setProperty("spring.devtools.restart.enabled","false");
        ConfigurableApplicationContext context = SpringApplication.run(NseMgrApplication.class, args);
        ConfigurableEnvironment environment = context.getEnvironment();
        String info = """

                ---------------------------------------------------------------------
                应用 '%-20s' 运行成功! 当前环境 '%-10s' !!! 端口 '[%s]' !!!
                ---------------------------------------------------------------------
                |  并发数（管理员） | 并发数（普通用户）  | 最大内存GB | CPU核心 | 最大磁盘GB |
                |----------------|------------------|----------|--------|----------|
                | %-14s | %-16s | %-8s | %-6s | %-8s |
                |------------------------------------------------------------------|
                """;
        info = info.formatted(environment.getProperty("spring.application.name"),
                Arrays.toString(environment.getActiveProfiles()),
                environment.getProperty("server.port"),
                LicenseContext.getLicense() != null ? LicenseContext.getLicense().getCdata().getPayload().getRestrictions().getMgr() : "N/A",
                LicenseContext.getLicense() != null ? LicenseContext.getLicense().getCdata().getPayload().getRestrictions().getUser() : "N/A",
                EnvironmentContext.getMaxMemory(),
                EnvironmentContext.getCpuCores(),
                EnvironmentContext.getMaxDisk()
                );
        log.info("\n{}", info);

    }
}
