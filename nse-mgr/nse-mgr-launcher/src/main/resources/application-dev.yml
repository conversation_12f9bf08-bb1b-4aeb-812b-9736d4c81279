spring:
  datasource:
    url: ****************************************************************************************************************
    username: sys_network_simulation_rw
    password: FS9jz%6KO5RRdcAA
    driver-class-name: org.postgresql.Driver
  
  # Flyway 数据库迁移配置
  flyway:
    enabled: true
    # 迁移脚本位置
    locations: classpath:/db/migration
    # 数据库schema
    default-schema: mgr
    # 基线版本
    baseline-version: 1
    # 启用基线
    baseline-on-migrate: true
    # 验证迁移脚本
    validate-on-migrate: true
    # 清理模式（生产环境必须true）
    clean-disabled: true
    # 输出详细信息
    out-of-order: false


nse:
  ehcache:
    storage-path: E:\GNS3_DATA\ehcache-storage-mgr
    heap: 50
    offheap: 100
    disk: 1024
    jwt-expire: 1440
    service-expire: 30
  jwt:
    expire: 7200
  py3:
    storage-path: F:\GNS3_DATA
    py3-path: C:\__ruijie_work_space\nse\nse-service\nse-mgr\installer\simple/dist
    ssl:
      enable: false

  jobs:
    # PID校验任务配置
    pid-validation:
      # 是否启用PID校验任务
      enabled: true
      # Cron表达式配置
      # 默认每5分钟执行一次: 0 */5 * * * ?
      # 每分钟执行一次: 0 * * * * ?
      # 每10分钟执行一次: 0 */10 * * * ?
      # 每小时执行一次: 0 0 * * * ?
      # 每天凌晨2点执行: 0 0 2 * * ?
      cron: "0 */5 * * * ?"
      # 任务描述
      description: "定时校验ser_hosts表中的pid是否存在，清理无效记录"

    # Python进程清理任务配置
    python-process-cleanup:
      # 是否启用Python进程清理任务
      enabled: true
      # Cron表达式配置
      # 默认每10分钟执行一次: 0 */10 * * * ?
      # 每5分钟执行一次: 0 */5 * * * ?
      # 每15分钟执行一次: 0 */15 * * * ?
      # 每小时执行一次: 0 0 * * * ?
      # 每天凌晨3点执行: 0 0 3 * * ?
      cron: "0 */10 * * * ?"
      # 任务描述
      description: "定时清理不在ser_hosts表中的Python NSE服务进程"

logging:
  level:
    com:
      ruijie:
        nse:
          mgr: debug

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
