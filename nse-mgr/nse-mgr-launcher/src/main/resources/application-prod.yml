spring:
  datasource:
    url: ********************************************************************************
    username: sys_nse_rw
    password: R<PERSON><PERSON><PERSON>@NSE20250902!!!
    driver-class-name: org.postgresql.Driver


  # Flyway 数据库迁移配置
  flyway:
    enabled: false
    # 迁移脚本位置
    locations: classpath:/db/migration
    # 数据库schema
    default-schema: public
    # 验证迁移脚本
    validate-on-migrate: true
    # 清理模式（生产环境必须true）
    clean-disabled: true

nse:
  ehcache:
    storage-path: /data/ruijie/nse/ehcache-storage-mgr
    heap: 50
    offheap: 100
    disk: 1024
    jwt-expire: 1440
    service-expire: 30
  jwt:
    expire: 7200
  py3:
    storage-path: /data/ruijie/nse/storage-data
    py3-path: /data/ruijie/nse/projects/
    ssl:
      enable: false

  jobs:
    pid-validation:
      enabled: true
      cron: "0 */5 * * * ?"
      description: "定时校验ser_hosts表中的pid是否存在，清理无效记录"
    python-process-cleanup:
      enabled: true
      cron: "0 */10 * * * ?"
      description: "定时清理不在ser_hosts表中的Python NSE服务进程"

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# 防抖配置
debounce:
  # 是否启用防抖功能
  enabled: true
  # 防抖时间窗口（毫秒），默认500ms
  time-window: 300
  # 需要排除的路径模式
  exclude-patterns:
    - "/api/auth/**"
    - "/api/public/**"
    - "/api/menu/routes"
    - "/ws/**"