-- Flyway 初始化脚本
-- 版本: V1
-- 描述: 初始化数据库架构
-- 作者: NSE Team
-- 创建时间: 2025-01-09

-- 创建序列（如果需要）
-- CREATE SEQUENCE IF NOT EXISTS seq_example START 1;

-- crs_course definition

-- Drop table

-- DROP TABLE crs_course;

CREATE TABLE IF NOT EXISTS crs_course (
	id varchar(32) NOT NULL,
	created_date timestamp(6) NOT NULL, -- 创建时间
	created_by varchar(50) NOT NULL, -- 创建人
	modified_date timestamp(6) NULL, -- 修改时间
	modified_by varchar(50) NULL, -- 修改人
	is_deleted int2 DEFAULT 0 NOT NULL, -- 是否删除
	remark varchar(300) NULL, -- 备注
	user_id varchar NOT NULL,
	course_name varchar NULL, -- 课程名称
	class_name varchar NULL, -- 班级名称
	class_id varchar NULL, -- 班级id，对应sys_official_id
	status varchar NULL, -- 课程状态：上课中，未上课
	student_cnt int4 DEFAULT 0 NOT NULL, -- 上课学生人数
	exp_cnt int4 DEFAULT 0 NOT NULL, -- 实验个数
	CONSTRAINT crs_course_pk PRIMARY KEY (id),
	CONSTRAINT crs_student_cnt_check CHECK ((student_cnt > 0))
);
COMMENT ON TABLE crs_course IS '课程数据管理';

-- Column comments

COMMENT ON COLUMN crs_course.created_date IS '创建时间';
COMMENT ON COLUMN crs_course.created_by IS '创建人';
COMMENT ON COLUMN crs_course.modified_date IS '修改时间';
COMMENT ON COLUMN crs_course.modified_by IS '修改人';
COMMENT ON COLUMN crs_course.is_deleted IS '是否删除';
COMMENT ON COLUMN crs_course.remark IS '备注';
COMMENT ON COLUMN crs_course.course_name IS '课程名称';
COMMENT ON COLUMN crs_course.class_name IS '班级名称';
COMMENT ON COLUMN crs_course.class_id IS '班级id，对应sys_official_id';
COMMENT ON COLUMN crs_course.status IS '课程状态：上课中，未上课';
COMMENT ON COLUMN crs_course.student_cnt IS '上课学生人数';
COMMENT ON COLUMN crs_course.exp_cnt IS '实验个数';


-- crs_course_lesson definition

-- Drop table

-- DROP TABLE crs_course_lesson;

CREATE TABLE IF NOT EXISTS crs_course_lesson (
	course_id varchar NULL,
	lesson_id varchar NULL
);


-- crs_course_repo definition

-- Drop table

-- DROP TABLE crs_course_repo;

CREATE TABLE IF NOT EXISTS crs_course_repo (
	id varchar(32) NOT NULL,
	course_pkg varchar NOT NULL, -- 课程包
	exp_name varchar NOT NULL, -- 实验名称
	exp_manual_path varchar NULL, -- 实验手册，服务器本地pdf路径
	exp_project_path varchar NOT NULL, -- 实验工程路径
	exp_type varchar NOT NULL, -- 实验类型，锐捷课程库，学校课程库
	created_date timestamp(6) NOT NULL, -- 创建时间
	created_by varchar(50) NOT NULL, -- 创建人
	modified_date timestamp(6) NULL, -- 修改时间
	modified_by varchar(50) NULL, -- 修改人
	is_deleted int2 DEFAULT 0 NOT NULL, -- 是否删除
	remark varchar(300) NULL, -- 备注
	exp_manual_name varchar NULL, -- 实验手册源文件名称
	exp_project_name varchar NULL, -- 实验工程源文件名称
	CONSTRAINT crs_course_repo_pk PRIMARY KEY (id)
);
COMMENT ON TABLE crs_course_repo IS '课程库';

-- Column comments

COMMENT ON COLUMN crs_course_repo.course_pkg IS '课程包';
COMMENT ON COLUMN crs_course_repo.exp_name IS '实验名称';
COMMENT ON COLUMN crs_course_repo.exp_manual_path IS '实验手册，服务器本地pdf路径';
COMMENT ON COLUMN crs_course_repo.exp_project_path IS '实验工程路径';
COMMENT ON COLUMN crs_course_repo.exp_type IS '实验类型，锐捷课程库，学校课程库';
COMMENT ON COLUMN crs_course_repo.created_date IS '创建时间';
COMMENT ON COLUMN crs_course_repo.created_by IS '创建人';
COMMENT ON COLUMN crs_course_repo.modified_date IS '修改时间';
COMMENT ON COLUMN crs_course_repo.modified_by IS '修改人';
COMMENT ON COLUMN crs_course_repo.is_deleted IS '是否删除';
COMMENT ON COLUMN crs_course_repo.remark IS '备注';
COMMENT ON COLUMN crs_course_repo.exp_manual_name IS '实验手册源文件名称';
COMMENT ON COLUMN crs_course_repo.exp_project_name IS '实验工程源文件名称';


-- crs_lesson definition

-- Drop table

-- DROP TABLE crs_lesson;

CREATE TABLE IF NOT EXISTS crs_lesson (
	id varchar(32) NOT NULL,
	created_date timestamp(6) NOT NULL, -- 创建时间
	created_by varchar(50) NOT NULL, -- 创建人
	modified_date timestamp(6) NULL, -- 修改时间
	modified_by varchar(50) NULL, -- 修改人
	is_deleted int2 DEFAULT 0 NOT NULL, -- 是否删除
	remark varchar(300) NULL, -- 备注
	student_id varchar NOT NULL, -- 学生id
	teacher_id varchar NOT NULL, -- 老师id
	lesson_type varchar NOT NULL, -- 上课类型：上课，作业
	course_name varchar NULL, -- 课程名称
	score numeric(4, 2) NULL, -- 分数
	exp_result_path varchar NULL, -- 实验结果文件上传路径
	exp_result_filename varchar NULL,
	submit_status varchar NULL, -- 提交状态：未提交，已提交
	submit_date timestamp NULL, -- 提交时间
	exp_name varchar NULL, -- 实验名称
	course_repo_id varchar NULL, -- 课程库id
	course_repo_snapshot jsonb NULL, -- 课程库快照数据
	CONSTRAINT crs_lesson_pk PRIMARY KEY (id)
);
COMMENT ON TABLE crs_lesson IS '学生上课 & 作业课';

-- Column comments

COMMENT ON COLUMN crs_lesson.created_date IS '创建时间';
COMMENT ON COLUMN crs_lesson.created_by IS '创建人';
COMMENT ON COLUMN crs_lesson.modified_date IS '修改时间';
COMMENT ON COLUMN crs_lesson.modified_by IS '修改人';
COMMENT ON COLUMN crs_lesson.is_deleted IS '是否删除';
COMMENT ON COLUMN crs_lesson.remark IS '备注';
COMMENT ON COLUMN crs_lesson.student_id IS '学生id';
COMMENT ON COLUMN crs_lesson.teacher_id IS '老师id';
COMMENT ON COLUMN crs_lesson.lesson_type IS '上课类型：上课，作业';
COMMENT ON COLUMN crs_lesson.course_name IS '课程名称';
COMMENT ON COLUMN crs_lesson.score IS '分数';
COMMENT ON COLUMN crs_lesson.exp_result_path IS '实验结果文件上传路径';
COMMENT ON COLUMN crs_lesson.submit_status IS '提交状态：未提交，已提交';
COMMENT ON COLUMN crs_lesson.submit_date IS '提交时间';
COMMENT ON COLUMN crs_lesson.exp_name IS '实验名称';
COMMENT ON COLUMN crs_lesson.course_repo_id IS '课程库id';
COMMENT ON COLUMN crs_lesson.course_repo_snapshot IS '课程库快照数据';


-- evt_verify_records definition

-- Drop table

-- DROP TABLE evt_verify_records;

CREATE TABLE IF NOT EXISTS evt_verify_records (
	id varchar NOT NULL,
	evt varchar NULL, -- 事件名称
	evt_details text NULL, -- 事件详情
	remark varchar(300) NULL, -- 备注
	created_date timestamp(6) NOT NULL, -- 创建时间
	modified_date timestamp(6) NULL, -- 修改时间
	evt_level varchar NULL, -- 事件等级
	evt_message varchar NULL,
	CONSTRAINT evt_verify_records_pk PRIMARY KEY (id)
);
COMMENT ON TABLE evt_verify_records IS '一些用于校验实践管理的数据表';

-- Column comments

COMMENT ON COLUMN evt_verify_records.evt IS '事件名称';
COMMENT ON COLUMN evt_verify_records.evt_details IS '事件详情';
COMMENT ON COLUMN evt_verify_records.remark IS '备注';
COMMENT ON COLUMN evt_verify_records.created_date IS '创建时间';
COMMENT ON COLUMN evt_verify_records.modified_date IS '修改时间';
COMMENT ON COLUMN evt_verify_records.evt_level IS '事件等级';


-- exp_experiment definition

-- Drop table

-- DROP TABLE exp_experiment;

CREATE TABLE IF NOT EXISTS exp_experiment (
	id varchar(32) NOT NULL,
	created_date timestamp(6) NOT NULL, -- 创建时间
	created_by varchar(50) NOT NULL, -- 创建人
	modified_date timestamp(6) NULL, -- 修改时间
	modified_by varchar(50) NULL, -- 修改人
	is_deleted int2 DEFAULT 0 NOT NULL, -- 是否删除
	remark varchar(300) NULL, -- 备注
	exp_name varchar NULL,
	user_id varchar NOT NULL,
	exp_type varchar NOT NULL, -- 实验类型，空白实验，导入实验，课程关联实验
	exp_project_id varchar NOT NULL, -- 实验项目id，由py3server给出
	exp_project_path varchar NOT NULL, -- 实验项目路径，本地服务器数据
	"source" varchar NULL, -- 项目来源：自建、课程库实验-id、复制-id
	tags _varchar NULL, -- 实验标签
	CONSTRAINT exp_experiment_pk PRIMARY KEY (id)
);
COMMENT ON TABLE exp_experiment IS '实验数据';

-- Column comments

COMMENT ON COLUMN exp_experiment.created_date IS '创建时间';
COMMENT ON COLUMN exp_experiment.created_by IS '创建人';
COMMENT ON COLUMN exp_experiment.modified_date IS '修改时间';
COMMENT ON COLUMN exp_experiment.modified_by IS '修改人';
COMMENT ON COLUMN exp_experiment.is_deleted IS '是否删除';
COMMENT ON COLUMN exp_experiment.remark IS '备注';
COMMENT ON COLUMN exp_experiment.exp_type IS '实验类型，空白实验，导入实验，课程关联实验';
COMMENT ON COLUMN exp_experiment.exp_project_id IS '实验项目id，由py3server给出';
COMMENT ON COLUMN exp_experiment.exp_project_path IS '实验项目路径，本地服务器数据';
COMMENT ON COLUMN exp_experiment."source" IS '项目来源：自建、课程库实验-id、复制-id';
COMMENT ON COLUMN exp_experiment.tags IS '实验标签';


-- exp_experiment_lesson definition

-- Drop table

-- DROP TABLE exp_experiment_lesson;

CREATE TABLE IF NOT EXISTS exp_experiment_lesson (
	exp_id varchar NULL,
	lesson_id varchar NULL,
	CONSTRAINT exp_experiment_lesson_unique UNIQUE (lesson_id, exp_id)
);


-- lic_activation_info definition

-- Drop table

-- DROP TABLE lic_activation_info;

CREATE TABLE IF NOT EXISTS lic_activation_info (
	id varchar(32) NOT NULL,
	remark varchar(300) NULL, -- 备注
	created_date timestamp(6) NOT NULL, -- 创建时间
	created_by varchar(50) NOT NULL, -- 创建人
	modified_date timestamp(6) NULL, -- 修改时间
	modified_by varchar(50) NULL, -- 修改人
	is_deleted int2 DEFAULT 0 NOT NULL, -- 是否删除
	product_info varchar NULL, -- 授权产品信息
	activation_code varchar NOT NULL, -- 授权码
	permit_mgr_cnt int4 DEFAULT 0 NOT NULL, -- 终端许可数量（管理员）
	permit_user_cnt int4 DEFAULT 0 NOT NULL, -- 终端许可数量（普通用户）
	status varchar NULL, -- 授权状态，已授权，已过期，已注销
	valid_from timestamp(6) NOT NULL, -- 有效期开始日期
	valid_to timestamp(6) NOT NULL, -- 有效期截止日期
	valid_type varchar NOT NULL, -- 有效期类型，永久、1年、2年、3年
	activation_time timestamp NULL, -- 激活时间
	cancel_time timestamp NULL, -- 注销时间
	cancel_code varchar NULL, -- 注销码
	"source" varchar NOT NULL,
	machine_code varchar NULL,
	signature varchar NULL,
	CONSTRAINT lic_activation_info_pk PRIMARY KEY (id),
	CONSTRAINT lic_activation_valid_check CHECK ((valid_to > valid_from))
);
COMMENT ON TABLE lic_activation_info IS 'license授权信息';

-- Column comments

COMMENT ON COLUMN lic_activation_info.remark IS '备注';
COMMENT ON COLUMN lic_activation_info.created_date IS '创建时间';
COMMENT ON COLUMN lic_activation_info.created_by IS '创建人';
COMMENT ON COLUMN lic_activation_info.modified_date IS '修改时间';
COMMENT ON COLUMN lic_activation_info.modified_by IS '修改人';
COMMENT ON COLUMN lic_activation_info.is_deleted IS '是否删除';
COMMENT ON COLUMN lic_activation_info.product_info IS '授权产品信息';
COMMENT ON COLUMN lic_activation_info.activation_code IS '授权码';
COMMENT ON COLUMN lic_activation_info.permit_mgr_cnt IS '终端许可数量（管理员）';
COMMENT ON COLUMN lic_activation_info.permit_user_cnt IS '终端许可数量（普通用户）';
COMMENT ON COLUMN lic_activation_info.status IS '授权状态，已授权，已过期，已注销';
COMMENT ON COLUMN lic_activation_info.valid_from IS '有效期开始日期';
COMMENT ON COLUMN lic_activation_info.valid_to IS '有效期截止日期';
COMMENT ON COLUMN lic_activation_info.valid_type IS '有效期类型，永久、1年、2年、3年';
COMMENT ON COLUMN lic_activation_info.activation_time IS '激活时间';
COMMENT ON COLUMN lic_activation_info.cancel_time IS '注销时间';
COMMENT ON COLUMN lic_activation_info.cancel_code IS '注销码';


-- machine_info definition

-- Drop table

-- DROP TABLE machine_info;

CREATE TABLE IF NOT EXISTS machine_info (
	id varchar(32) NOT NULL,
	created_date timestamp(6) NOT NULL, -- 创建时间
	cpu_id varchar NULL,
	serial_number varchar NULL,
	nif_mac varchar NULL, -- 网卡mac，通常获取第一张网卡
	disk_serial varchar NULL, -- 磁盘序列号
	combine varchar NULL, -- 硬件信息组合
	combine_hash varchar NULL, -- 硬件码hash
	machine_type varchar NULL, -- 机器类型，'physical', 'virtual', 'container'
	python_env varchar NULL, -- python环境信息
	free_memory varchar NULL,
	total_memory varchar NULL,
	free_disk varchar NULL,
	total_disk varchar NULL,
	nse_server_host varchar NULL,
	nse_server_reachabled bool NULL,
	CONSTRAINT machine_info_pk PRIMARY KEY (id)
);
COMMENT ON TABLE machine_info IS '当前机器信息';

-- Column comments

COMMENT ON COLUMN machine_info.created_date IS '创建时间';
COMMENT ON COLUMN machine_info.nif_mac IS '网卡mac，通常获取第一张网卡';
COMMENT ON COLUMN machine_info.disk_serial IS '磁盘序列号';
COMMENT ON COLUMN machine_info.combine IS '硬件信息组合';
COMMENT ON COLUMN machine_info.combine_hash IS '硬件码hash';
COMMENT ON COLUMN machine_info.machine_type IS '机器类型，''physical'', ''virtual'', ''container''';
COMMENT ON COLUMN machine_info.python_env IS 'python环境信息';


-- ser_clients definition

-- Drop table

-- DROP TABLE ser_clients;

CREATE TABLE IF NOT EXISTS ser_clients (
	id varchar(50) NOT NULL, -- 主键
	created_date timestamp(6) NULL, -- 创建时间
	created_by varchar(50) NULL,
	client_ip varchar NULL, -- 远程连接客户端IP
	user_id varchar NOT NULL, -- 用户ID
	host_id varchar NOT NULL, -- ser_host表id
	connect_time timestamp NOT NULL, -- 连接时间
	CONSTRAINT ser_clients_pk PRIMARY KEY (id)
);
COMMENT ON TABLE ser_clients IS '与ser_hosts连接的客户端信息';

-- Column comments

COMMENT ON COLUMN ser_clients.id IS '主键';
COMMENT ON COLUMN ser_clients.created_date IS '创建时间';
COMMENT ON COLUMN ser_clients.client_ip IS '远程连接客户端IP';
COMMENT ON COLUMN ser_clients.user_id IS '用户ID';
COMMENT ON COLUMN ser_clients.host_id IS 'ser_host表id';
COMMENT ON COLUMN ser_clients.connect_time IS '连接时间';


-- ser_hosts definition

-- Drop table

-- DROP TABLE ser_hosts;

CREATE TABLE IF NOT EXISTS ser_hosts (
	id varchar(50) NOT NULL, -- 主键
	remark varchar(300) NULL, -- 角色描述
	created_date timestamp(6) NULL, -- 创建时间
	created_by varchar(50) NULL,
	modified_date timestamp(6) NULL,
	modified_by varchar(50) NULL, -- 更新者
	is_deleted int2 DEFAULT 0 NOT NULL, -- 是否删除
	server_ip varchar NOT NULL, -- 服务主机ip
	server_port int4 NOT NULL, -- 服务port
	basic_auth jsonb NOT NULL, -- basicAuth信息
	pid int8 NOT NULL, -- 进程id
	max_memory numeric NULL, -- 最大内存，单位：GB
	max_disk numeric NULL, -- 最大磁盘空间，单位：GB
	cpu_core int8 NULL, -- 可用cpu核心数，单位：个数
	user_id varchar NULL,
	storage_path varchar NULL, -- 存储路径
	current_memory numeric NULL, -- 当前已用内存
	CONSTRAINT ser_hosts_pk PRIMARY KEY (id),
	CONSTRAINT ser_hosts_unique UNIQUE (user_id, is_deleted)
);
COMMENT ON TABLE ser_hosts IS 'python服务主机信息，每新增一台服务会多一条记录';

-- Column comments

COMMENT ON COLUMN ser_hosts.id IS '主键';
COMMENT ON COLUMN ser_hosts.remark IS '角色描述';
COMMENT ON COLUMN ser_hosts.created_date IS '创建时间';
COMMENT ON COLUMN ser_hosts.modified_by IS '更新者';
COMMENT ON COLUMN ser_hosts.is_deleted IS '是否删除';
COMMENT ON COLUMN ser_hosts.server_ip IS '服务主机ip';
COMMENT ON COLUMN ser_hosts.server_port IS '服务port';
COMMENT ON COLUMN ser_hosts.basic_auth IS 'basicAuth信息';
COMMENT ON COLUMN ser_hosts.pid IS '进程id';
COMMENT ON COLUMN ser_hosts.max_memory IS '最大内存，单位：GB';
COMMENT ON COLUMN ser_hosts.max_disk IS '最大磁盘空间，单位：GB';
COMMENT ON COLUMN ser_hosts.cpu_core IS '可用cpu核心数，单位：个数';
COMMENT ON COLUMN ser_hosts.storage_path IS '存储路径';
COMMENT ON COLUMN ser_hosts.current_memory IS '当前已用内存';


-- sys_audit_log definition

-- Drop table

-- DROP TABLE sys_audit_log;

CREATE TABLE IF NOT EXISTS sys_audit_log (
	id varchar(32) NOT NULL,
	created_date timestamp(6) NOT NULL, -- 创建时间
	created_by varchar(50) NOT NULL, -- 创建人
	request_body jsonb NULL, -- 请求参数
	response_body jsonb NULL, -- 响应数据
	method_name varchar(200) NULL,
	elapsed_milli_seconds int4 NULL, -- 耗时，单位是毫秒
	ip varchar(15) NULL,
	url varchar(100) NULL,
	mime varchar(200) NULL,
	request_headers jsonb NULL,
	audit_title varchar NULL,
	"module" varchar NULL -- 系统模块，比如sys，license，py3server等
);
COMMENT ON TABLE sys_audit_log IS '系统审计日志';

-- Column comments

COMMENT ON COLUMN sys_audit_log.created_date IS '创建时间';
COMMENT ON COLUMN sys_audit_log.created_by IS '创建人';
COMMENT ON COLUMN sys_audit_log.request_body IS '请求参数';
COMMENT ON COLUMN sys_audit_log.response_body IS '响应数据';
COMMENT ON COLUMN sys_audit_log.elapsed_milli_seconds IS '耗时，单位是毫秒';
COMMENT ON COLUMN sys_audit_log."module" IS '系统模块，比如sys，license，py3server等';


-- sys_menu definition

-- Drop table

-- DROP TABLE sys_menu;

CREATE TABLE IF NOT EXISTS sys_menu (
	id varchar(32) NOT NULL, -- ID
	parent_id varchar(32) NOT NULL, -- 父菜单ID
	tree_path varchar(255) NULL, -- 父节点ID路径
	"name" varchar(64) NOT NULL, -- 菜单名称
	"type" int2 NOT NULL, -- 菜单类型（1-菜单 2-目录 3-外链 4-按钮）
	route_name varchar(255) NULL, -- 路由名称（Vue Router 中用于命名路由）
	route_path varchar(128) NULL, -- 路由路径（Vue Router 中定义的 URL 路径）
	component varchar(128) NULL, -- 组件路径（组件页面完整路径，相对于 src/views/，缺省后缀 .vue）
	perm varchar(128) NULL, -- 【按钮】权限标识
	always_show int2 DEFAULT 0 NULL, -- 【目录】只有一个子路由是否始终显示（1-是 0-否）
	keep_alive int2 DEFAULT 0 NULL, -- 【菜单】是否开启页面缓存（1-是 0-否）
	visible int2 DEFAULT 1 NULL, -- 显示状态（1-显示 0-隐藏）
	sort int4 DEFAULT 0 NULL, -- 排序
	icon varchar(64) NULL, -- 菜单图标
	redirect varchar(128) NULL, -- 跳转路径
	params varchar(255) NULL, -- 路由参数
	remark varchar(300) NULL, -- 备注
	created_date timestamp(6) NULL, -- 创建时间
	created_by varchar(50) NULL, -- 创建人
	modified_date timestamp(6) NULL, -- 修改时间
	modified_by varchar(50) NULL, -- 修改人
	is_deleted int2 DEFAULT 0 NULL, -- 是否删除
	verify_license bool DEFAULT true NULL, -- 是否需要校验license
	CONSTRAINT sys_menu_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN sys_menu.id IS 'ID';
COMMENT ON COLUMN sys_menu.parent_id IS '父菜单ID';
COMMENT ON COLUMN sys_menu.tree_path IS '父节点ID路径';
COMMENT ON COLUMN sys_menu."name" IS '菜单名称';
COMMENT ON COLUMN sys_menu."type" IS '菜单类型（1-菜单 2-目录 3-外链 4-按钮）';
COMMENT ON COLUMN sys_menu.route_name IS '路由名称（Vue Router 中用于命名路由）';
COMMENT ON COLUMN sys_menu.route_path IS '路由路径（Vue Router 中定义的 URL 路径）';
COMMENT ON COLUMN sys_menu.component IS '组件路径（组件页面完整路径，相对于 src/views/，缺省后缀 .vue）';
COMMENT ON COLUMN sys_menu.perm IS '【按钮】权限标识';
COMMENT ON COLUMN sys_menu.always_show IS '【目录】只有一个子路由是否始终显示（1-是 0-否）';
COMMENT ON COLUMN sys_menu.keep_alive IS '【菜单】是否开启页面缓存（1-是 0-否）';
COMMENT ON COLUMN sys_menu.visible IS '显示状态（1-显示 0-隐藏）';
COMMENT ON COLUMN sys_menu.sort IS '排序';
COMMENT ON COLUMN sys_menu.icon IS '菜单图标';
COMMENT ON COLUMN sys_menu.redirect IS '跳转路径';
COMMENT ON COLUMN sys_menu.params IS '路由参数';
COMMENT ON COLUMN sys_menu.remark IS '备注';
COMMENT ON COLUMN sys_menu.created_date IS '创建时间';
COMMENT ON COLUMN sys_menu.created_by IS '创建人';
COMMENT ON COLUMN sys_menu.modified_date IS '修改时间';
COMMENT ON COLUMN sys_menu.modified_by IS '修改人';
COMMENT ON COLUMN sys_menu.is_deleted IS '是否删除';
COMMENT ON COLUMN sys_menu.verify_license IS '是否需要校验license';


-- sys_official definition

-- Drop table

-- DROP TABLE sys_official;

CREATE TABLE IF NOT EXISTS sys_official (
	id varchar(32) NOT NULL,
	created_date timestamp(6) NOT NULL, -- 创建时间
	created_by varchar(50) NOT NULL, -- 创建人
	modified_date timestamp(6) NULL, -- 修改时间
	modified_by varchar(50) NULL, -- 修改人
	is_deleted int2 DEFAULT 0 NOT NULL, -- 是否删除
	remark varchar(300) NULL, -- 备注
	"name" varchar NULL,
	code varchar NULL,
	"type" varchar NULL, -- 机构类型，in (专业-DEPARTMENT, 年级-GRADE, 班级-CLASS)
	CONSTRAINT sys_official_check CHECK (((type)::text = ANY ((ARRAY['DEPARTMENT'::character varying, 'GRADE'::character varying, 'CLASS'::character varying])::text[]))),
	CONSTRAINT sys_official_pk PRIMARY KEY (id)
);
COMMENT ON TABLE sys_official IS '系统组织机构';

-- Column comments

COMMENT ON COLUMN sys_official.created_date IS '创建时间';
COMMENT ON COLUMN sys_official.created_by IS '创建人';
COMMENT ON COLUMN sys_official.modified_date IS '修改时间';
COMMENT ON COLUMN sys_official.modified_by IS '修改人';
COMMENT ON COLUMN sys_official.is_deleted IS '是否删除';
COMMENT ON COLUMN sys_official.remark IS '备注';
COMMENT ON COLUMN sys_official."type" IS '机构类型，in (专业-DEPARTMENT, 年级-GRADE, 班级-CLASS)';


-- sys_role definition

-- Drop table

-- DROP TABLE sys_role;

CREATE TABLE IF NOT EXISTS sys_role (
	id varchar(50) NOT NULL, -- 主键
	"name" varchar(50) NOT NULL, -- 角色名称
	"level" int4 NULL, -- 角色等级
	remark varchar(300) NULL, -- 角色描述
	created_date timestamp(6) NULL, -- 创建时间
	created_by varchar(50) NULL,
	modified_date timestamp(6) NULL,
	modified_by varchar(50) NULL, -- 更新者
	is_deleted int2 DEFAULT 0 NULL, -- 是否删除
	CONSTRAINT sys_role_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN sys_role.id IS '主键';
COMMENT ON COLUMN sys_role."name" IS '角色名称';
COMMENT ON COLUMN sys_role."level" IS '角色等级';
COMMENT ON COLUMN sys_role.remark IS '角色描述';
COMMENT ON COLUMN sys_role.created_date IS '创建时间';
COMMENT ON COLUMN sys_role.modified_by IS '更新者';
COMMENT ON COLUMN sys_role.is_deleted IS '是否删除';


-- sys_role_menu definition

-- Drop table

-- DROP TABLE sys_role_menu;

CREATE TABLE IF NOT EXISTS sys_role_menu (
	role_id varchar(50) NOT NULL, -- 角色ID
	menu_id varchar(50) NOT NULL, -- 菜单ID
	CONSTRAINT sys_role_menu_unique UNIQUE (role_id, menu_id)
);

-- Column comments

COMMENT ON COLUMN sys_role_menu.role_id IS '角色ID';
COMMENT ON COLUMN sys_role_menu.menu_id IS '菜单ID';


-- sys_user definition

-- Drop table

-- DROP TABLE sys_user;

CREATE TABLE IF NOT EXISTS sys_user (
	id varchar(32) NOT NULL, -- id
	"name" varchar(50) NULL, -- 姓名
	"password" varchar(64) NULL, -- 密码
	salt varchar(32) NULL, -- 盐
	email varchar(50) NULL, -- 用户邮箱
	mobile_phone varchar(20) NULL, -- 手机号码
	status int2 NULL, -- 用户状态
	avatar varchar(200) NULL, -- 头像
	user_type varchar(32) NULL, -- 用户类型
	valid_date timestamp(6) NULL, -- 账号失效日期
	remark varchar(300) NULL, -- 备注
	created_date timestamp(6) NULL, -- 创建时间
	created_by varchar(50) NULL, -- 创建人
	modified_date timestamp(6) NULL, -- 修改时间
	modified_by varchar(50) NULL, -- 修改人
	login_ip varchar(15) NULL, -- 登录IP
	login_date timestamp(6) NULL, -- 登录时间
	is_deleted int2 DEFAULT 0 NULL, -- 是否删除
	account varchar NULL, -- 登录账号
	is_default_pwd bool DEFAULT true NULL, -- 是否默认密码，是的话需要提示他修改默认密码
	CONSTRAINT sys_user_pkey PRIMARY KEY (id),
	CONSTRAINT sys_user_unique UNIQUE (account)
);

-- Column comments

COMMENT ON COLUMN sys_user.id IS 'id';
COMMENT ON COLUMN sys_user."name" IS '姓名';
COMMENT ON COLUMN sys_user."password" IS '密码';
COMMENT ON COLUMN sys_user.salt IS '盐';
COMMENT ON COLUMN sys_user.email IS '用户邮箱';
COMMENT ON COLUMN sys_user.mobile_phone IS '手机号码';
COMMENT ON COLUMN sys_user.status IS '用户状态';
COMMENT ON COLUMN sys_user.avatar IS '头像';
COMMENT ON COLUMN sys_user.user_type IS '用户类型';
COMMENT ON COLUMN sys_user.valid_date IS '账号失效日期';
COMMENT ON COLUMN sys_user.remark IS '备注';
COMMENT ON COLUMN sys_user.created_date IS '创建时间';
COMMENT ON COLUMN sys_user.created_by IS '创建人';
COMMENT ON COLUMN sys_user.modified_date IS '修改时间';
COMMENT ON COLUMN sys_user.modified_by IS '修改人';
COMMENT ON COLUMN sys_user.login_ip IS '登录IP';
COMMENT ON COLUMN sys_user.login_date IS '登录时间';
COMMENT ON COLUMN sys_user.is_deleted IS '是否删除';
COMMENT ON COLUMN sys_user.account IS '登录账号';
COMMENT ON COLUMN sys_user.is_default_pwd IS '是否默认密码，是的话需要提示他修改默认密码';


-- sys_user_official definition

-- Drop table

-- DROP TABLE sys_user_official;

CREATE TABLE IF NOT EXISTS sys_user_official (
	user_id varchar NULL,
	official_id varchar NULL
);


-- sys_user_role definition

-- Drop table

-- DROP TABLE sys_user_role;

CREATE TABLE IF NOT EXISTS sys_user_role (
	user_id varchar(50) NOT NULL, -- 用户主键
	role_id varchar(50) NOT NULL, -- 角色主键
	CONSTRAINT sys_user_role_unique UNIQUE (user_id, role_id)
);

-- Column comments

COMMENT ON COLUMN sys_user_role.user_id IS '用户主键';
COMMENT ON COLUMN sys_user_role.role_id IS '角色主键';

-- v_experiment_data_statistic source

CREATE OR REPLACE VIEW v_experiment_data_statistic
AS SELECT ccl.course_id,
    max(cl.exp_name::text)::character varying AS exp_name,
    max(cl.course_repo_id::text)::character varying AS course_repo_id,
    count(cl.student_id) AS totals,
    count(cl.student_id) FILTER (WHERE cl.submit_status::text = '已提交'::text) AS submits,
        CASE
            WHEN count(cl.student_id) = 0 THEN 0::numeric
            ELSE round(count(cl.student_id) FILTER (WHERE cl.submit_status::text = '已提交'::text)::numeric / count(cl.student_id)::numeric * 100::numeric)
        END AS submit_rate_percent,
    max(cl.teacher_id::text)::character varying AS teacher_id,
    cl.remark AS lesson_batch_no
   FROM crs_lesson cl
     LEFT JOIN crs_course_lesson ccl ON cl.id::text = ccl.lesson_id::text
  WHERE ccl.course_id IS NOT NULL AND cl.is_deleted = 0 AND cl.remark IS NOT NULL
  GROUP BY ccl.course_id, cl.remark;

COMMENT ON VIEW v_experiment_data_statistic IS '实验数据统计';

INSERT INTO crs_course_repo (id,course_pkg,exp_name,exp_manual_path,exp_project_path,exp_type,created_date,created_by,modified_date,modified_by,is_deleted,remark,exp_manual_name,exp_project_name) VALUES
	 ('1952270524732747778','323','测试实验1','exp-manual\1952270524753719296_DCN高可靠性测试报告_G1MLC3D000091.pdf','builtin/test_repo.nseproject','锐捷课程库','2025-08-04 15:28:59.193','1945376569312702465','2025-08-04 15:28:59.193','1945376569312702465',0,NULL,'DCN高可靠性测试报告_G1MLC3D000091.pdf','v2_sxzr76.nse'),
	 ('1949724403657441282','434','1231','exp-manual/1949724403615498240_DCN高可靠性测试报告_G1MLC3D000091.pdf','builtin/test_repo.nseproject','锐捷课程库','2025-07-28 14:51:36.618','1','2025-07-28 14:51:36.618','1',0,NULL,'DCN高可靠性测试报告_G1MLC3D000091.pdf','v2_sxzr76.nse'),
	 ('1949725340211970049','323','43434','exp-manual/1949725340224552960_DCN高可靠性测试报告_G1MLC3D000091.pdf','builtin/test_repo.nseproject','锐捷课程库','2025-07-28 14:55:19.913','1','2025-07-28 14:55:19.913','1',0,NULL,'DCN高可靠性测试报告_G1MLC3D000091.pdf','v2_sxzr76.nse'),
	 ('1952268003121377281','323','程序','exp-manual/1952268003087822848_DCN高可靠性测试报告_G1MLC3D000091.pdf','builtin/test_repo.nseproject','学校课程库','2025-08-04 15:18:57.989','1945376569312702465','2025-08-04 15:18:57.989','1945376569312702465',0,NULL,'DCN高可靠性测试报告_G1MLC3D000091.pdf','v2_sxzr76.nse'),
	 ('1952973797634732034','434','32323','exp-manual\1952973797584400384_DCN高可靠性测试报告_G1MLC3D000091.pdf','builtin/test_repo.nseproject','锐捷课程库','2025-08-06 14:03:32.517','1945376569312702465','2025-08-06 14:03:32.517','1945376569312702465',0,NULL,'DCN高可靠性测试报告_G1MLC3D000091.pdf','v2_sxzr76.nse'),
	 ('1952975396553748481','434','2323','exp-manual\1952975396583108608_DCN高可靠性测试报告_G1MLC3D000091.pdf','builtin/test_repo.nseproject','锐捷课程库','2025-08-06 14:09:53.734','1945376569312702465','2025-08-06 14:09:53.734','1945376569312702465',0,NULL,'DCN高可靠性测试报告_G1MLC3D000091.pdf','v2_sxzr76.nse')
ON CONFLICT (id) DO NOTHING;

INSERT INTO sys_menu (id,parent_id,tree_path,"name","type",route_name,route_path,component,perm,always_show,keep_alive,visible,sort,icon,redirect,params,remark,created_date,created_by,modified_date,modified_by,is_deleted,verify_license) VALUES
	 ('70','3','0,1,3','角色新增',4,NULL,'',NULL,'sys:role:add',NULL,NULL,1,2,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('71','3','0,1,3','角色编辑',4,NULL,'',NULL,'sys:role:edit',NULL,NULL,1,3,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('72','3','0,1,3','角色删除',4,NULL,'',NULL,'sys:role:delete',NULL,NULL,1,4,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('73','4','0,1,4','菜单新增',4,NULL,'',NULL,'sys:menu:add',NULL,NULL,1,1,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('74','4','0,1,4','菜单编辑',4,NULL,'',NULL,'sys:menu:edit',NULL,NULL,1,3,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('75','4','0,1,4','菜单删除',4,NULL,'',NULL,'sys:menu:delete',NULL,NULL,1,3,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('89','0','0','功能演示',2,NULL,'/function','Layout',NULL,NULL,NULL,1,12,'menu','',NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('90','89','0,89','Websocket',1,NULL,'/function/websocket','demo/websocket',NULL,NULL,1,1,3,'','',NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('2','1','0,1','用户管理',1,'User','user','system/user/index',NULL,NULL,1,1,1,'el-icon-User',NULL,NULL,NULL,NULL,NULL,NULL,NULL,1,true),
	 ('139','3','0,1,3','角色查询',4,NULL,'',NULL,'sys:role:query',NULL,NULL,1,1,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('1948624228046114817','1947192763160793090','0,1947190419715067905,1947192763160793090','批量删除用户',4,NULL,NULL,NULL,'normal-user:batch:delete',0,1,1,3,NULL,NULL,NULL,NULL,'2025-07-25 13:59:54.319','1','2025-07-25 13:59:54.319','1',0,true),
	 ('1948333785175056385','0','0','授权管理',2,NULL,'/license','Layout',NULL,1,1,1,1,'java',NULL,NULL,NULL,'2025-07-24 18:45:47.358','1','2025-07-24 18:45:47.358','1',0,false),
	 ('21','20','0,20','菜单一级',1,NULL,'multi-level1','demo/multi-level/level1',NULL,1,NULL,1,1,'','',NULL,NULL,NULL,NULL,NULL,NULL,1,true),
	 ('22','21','0,20,21','菜单二级',1,NULL,'multi-level2','demo/multi-level/children/level2',NULL,0,NULL,1,1,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,1,true),
	 ('23','22','0,20,21,22','菜单三级-1',1,NULL,'multi-level3-1','demo/multi-level/children/children/level3-1',NULL,0,1,1,1,'','',NULL,NULL,NULL,NULL,NULL,NULL,1,true),
	 ('24','22','0,20,21,22','菜单三级-2',1,NULL,'multi-level3-2','demo/multi-level/children/children/level3-2',NULL,0,1,1,2,'','',NULL,NULL,NULL,NULL,NULL,NULL,1,true),
	 ('20','0','0','多级菜单',2,NULL,'/multi-level','Layout',NULL,1,NULL,1,9,'cascader','',NULL,NULL,NULL,NULL,NULL,NULL,1,true),
	 ('110','0','0','路由参数',2,NULL,'/route-param','Layout',NULL,1,1,1,11,'el-icon-ElementPlus',NULL,NULL,NULL,NULL,NULL,NULL,NULL,1,true),
	 ('111','110','0,110','参数(type=1)',1,NULL,'route-param-type1','demo/route-param',NULL,0,1,1,1,'el-icon-Star',NULL,NULL,NULL,NULL,NULL,NULL,NULL,1,true),
	 ('112','110','0,110','参数(type=2)',1,NULL,'route-param-type2','demo/route-param',NULL,0,1,1,2,'el-icon-StarFilled',NULL,NULL,NULL,NULL,NULL,NULL,NULL,1,true),
	 ('95','36','0,36','字典组件',1,NULL,'dict-demo','demo/dictionary',NULL,NULL,1,1,4,'','',NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('97','89','0,89','Icons',1,NULL,'icon-demo','demo/icons',NULL,NULL,1,1,2,'el-icon-Notification','',NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('108','36','0,36','增删改查',1,NULL,'curd','demo/curd/index',NULL,NULL,1,1,0,'','',NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('109','36','0,36','列表选择器',1,NULL,'table-select','demo/table-select/index',NULL,NULL,1,1,1,'','',NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('140','4','0,1,4','菜单查询',4,NULL,'',NULL,'sys:menu:query',NULL,NULL,1,1,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('1','0','0','系统管理',2,'','/system','Layout',NULL,NULL,NULL,1,1,'system','/system/user',NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('3','1','0,1','角色管理',1,'Role','role','system/role/index',NULL,NULL,1,1,2,'role',NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('4','1','0,1','菜单管理',1,'SysMenu','menu','system/menu/index',NULL,NULL,1,1,3,'menu',NULL,NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('36','0','0','组件封装',2,NULL,'/component','Layout',NULL,NULL,NULL,1,10,'menu','',NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('37','36','0,36','富文本编辑器',1,NULL,'wang-editor','demo/wang-editor',NULL,NULL,1,1,2,'','',NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('38','36','0,36','图片上传',1,NULL,'upload','demo/upload',NULL,NULL,1,1,3,'','',NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('39','36','0,36','图标选择器',1,NULL,'icon-selector','demo/icon-selector',NULL,NULL,1,1,4,'','',NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('146','36','0,36','拖拽组件',1,NULL,'drag','demo/drag',NULL,NULL,NULL,1,5,'','',NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('147','36','0,36','滚动文本',1,NULL,'text-scroll','demo/text-scroll',NULL,NULL,NULL,1,6,'','',NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('148','89','0,89','字典实时同步',1,NULL,'dict-sync','demo/dict-sync',NULL,NULL,NULL,1,3,'','',NULL,NULL,NULL,NULL,NULL,NULL,0,true),
	 ('88','2','0,1,2','重置密码',4,NULL,'',NULL,'sys:user:reset-password',NULL,NULL,1,4,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,1,true),
	 ('33','2','0,1,2','用户删除',4,NULL,'',NULL,'sys:user:delete',NULL,NULL,1,3,'','',NULL,NULL,NULL,NULL,NULL,NULL,1,true),
	 ('32','2','0,1,2','用户编辑',4,NULL,'',NULL,'sys:user:edit',NULL,NULL,1,2,'','',NULL,NULL,NULL,NULL,NULL,NULL,1,true),
	 ('31','2','0,1,2','用户新增',4,NULL,'',NULL,'sys:user:add',NULL,NULL,1,1,'','',NULL,NULL,NULL,NULL,NULL,NULL,1,true),
	 ('107','2','0,1,2','用户导出',4,NULL,'',NULL,'sys:user:export',NULL,NULL,1,6,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,1,true),
	 ('106','2','0,1,2','用户导入',4,NULL,'',NULL,'sys:user:import',NULL,NULL,1,5,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,1,true),
	 ('105','2','0,1,2','用户查询',4,NULL,'',NULL,'sys:user:query',0,0,1,0,'',NULL,NULL,NULL,NULL,NULL,NULL,NULL,1,true),
	 ('1947192344766386178','1947190419715067905','0,1947190419715067905','管理员账号',1,'Administrator','administrator','account/administrator/index',NULL,0,1,1,1,NULL,NULL,NULL,NULL,'2025-07-21 15:10:06.748','1','2025-07-21 15:10:46.104','1',0,true),
	 ('1948624054167048193','1947192763160793090','0,1947190419715067905,1947192763160793090','新增用户',4,NULL,NULL,NULL,'normal-user:add',0,1,1,1,NULL,NULL,NULL,NULL,'2025-07-25 13:59:12.863','1','2025-07-25 13:59:12.863','1',0,true),
	 ('1947493133101146113','0','0','GNS3测试',2,NULL,'/nse','Layout',NULL,0,1,1,1,'tree',NULL,NULL,NULL,'2025-07-22 11:05:20.324','1','2025-07-22 11:05:20.324','1',0,true),
	 ('1947493340710805505','1947493133101146113','0,1947493133101146113','项目相关',1,'/project','/project','project/test',NULL,0,1,1,1,'api',NULL,NULL,NULL,'2025-07-22 11:06:09.759','1','2025-07-22 11:06:09.759','1',0,true),
	 ('1948292384815058945','0','0','我的实验',2,NULL,'/experiment','Layout',NULL,1,1,1,1,'client',NULL,NULL,NULL,'2025-07-24 16:01:16.722','1','2025-07-24 16:01:16.722','1',0,true),
	 ('1948294624590172161','0','0','课程库',2,NULL,'/course','Layout',NULL,1,1,1,1,NULL,NULL,NULL,NULL,'2025-07-24 16:10:10.724','1','2025-07-24 16:10:10.724','1',0,true),
	 ('1948300766385016834','0','0','授课管理',2,NULL,'/teaching','Layout',NULL,1,1,1,1,'el-icon-ElementPlus',NULL,NULL,NULL,'2025-07-24 16:34:35.042','1','2025-07-24 16:40:31.389','1',1,true),
	 ('1948302036751659010','1948300766385016834','0,1948300766385016834','授课管理',1,'授课管理','index','teaching/index',NULL,1,1,1,1,'typescript',NULL,NULL,NULL,'2025-07-24 16:39:37.92','1','2025-07-24 16:39:37.92','1',1,true),
	 ('1948306748607606785','0','0','授课管理',2,NULL,'/teaching','Layout',NULL,1,1,1,1,'browser',NULL,NULL,NULL,'2025-07-24 16:58:21.327','1','2025-07-24 16:58:21.327','1',0,true),
	 ('1948292534094532610','1948292384815058945','0,1948292384815058945','我的实验',1,'我的实验','index','experiment/index',NULL,1,0,1,1,NULL,NULL,NULL,NULL,'2025-07-24 16:01:52.312','1','2025-07-24 17:11:56.973','1',0,true),
	 ('1948624150195638273','1947192763160793090','0,1947190419715067905,1947192763160793090','批量添加用户',4,NULL,NULL,NULL,'normal-user:batch:add',0,1,1,2,NULL,NULL,NULL,NULL,'2025-07-25 13:59:35.763','1','2025-07-25 13:59:35.763','1',0,true),
	 ('1948620557*********','1948294763480354817','0,1948294624590172161,1948294763480354817','新增课程实验',4,NULL,NULL,NULL,'course:add',0,1,1,1,NULL,NULL,NULL,NULL,'2025-07-25 13:45:19.323','1','2025-07-25 13:45:19.323','1',0,true),
	 ('1948306836448915457','1948306748607606785','0,1948306748607606785','授课管理',1,'Teaching','index','teaching/index',NULL,1,1,1,1,'gitcode',NULL,NULL,NULL,'2025-07-24 16:58:42.26','1','2025-07-28 16:56:04.797','1',0,true),
	 ('1947190419715067905','0','0','账号管理',2,NULL,'/account','Layout',NULL,0,1,1,9,'el-icon-User',NULL,NULL,NULL,'2025-07-21 15:02:27.768','1','2025-08-04 16:18:09.538','1945376569312702465',0,true),
	 ('1948294763480354817','1948294624590172161','0,1948294624590172161','课程库',1,'Course','index','course/index',NULL,1,1,1,1,'table',NULL,NULL,NULL,'2025-07-24 16:10:43.845','1','2025-07-25 14:28:16.84','1',0,true),
	 ('1948620647662391297','1948294763480354817','0,1948294624590172161,1948294763480354817','批量删除课程',4,NULL,NULL,NULL,'course:delete',0,1,1,2,NULL,NULL,NULL,NULL,'2025-07-25 13:45:40.681','1','2025-07-25 13:45:40.681','1',0,true),
	 ('1947192763160793090','1947190419715067905','0,1947190419715067905','普通用户账号',1,'NormalUser','normal-user','account/normal-user/index',NULL,0,1,1,2,NULL,NULL,NULL,NULL,'2025-07-21 15:11:46.493','1','2025-07-24 17:32:47.271','1',0,true),
	 ('1950483044484603906','1948306748607606785','0,1948306748607606785','实验数据详情',1,'ExperimentDataDetail','experiment-data-detail','teaching/experiment-data-detail',NULL,0,0,0,3,NULL,NULL,NULL,NULL,'2025-07-30 17:06:10.695','1','2025-07-30 17:06:10.695','1',0,true),
	 ('1951176748111044610','1947190419715067905','0,1947190419715067905','账号导入',1,'AccountImport','import','account/import/index',NULL,0,1,0,3,NULL,NULL,NULL,NULL,'2025-08-01 15:02:42.521','1945376569312702465','2025-08-01 15:02:42.521','1945376569312702465',0,true),
	 ('1949755908324626434','1948306836448915457','0,1948306748607606785,1948306836448915457','上课',4,NULL,NULL,NULL,'teaching:start',0,1,1,1,NULL,NULL,NULL,NULL,'2025-07-28 16:56:47.923','1','2025-07-28 16:56:47.923','1',0,true),
	 ('1949756122091524097','1948306836448915457','0,1948306748607606785,1948306836448915457','下课',4,NULL,NULL,NULL,'teaching:end',0,1,1,2,NULL,NULL,NULL,NULL,'2025-07-28 16:57:38.888','1','2025-07-28 16:57:38.888','1',0,true),
	 ('1949756232829538306','1948306836448915457','0,1948306748607606785,1948306836448915457','学生管理',4,NULL,NULL,NULL,'teaching:student',0,1,1,3,NULL,NULL,NULL,NULL,'2025-07-28 16:58:05.284','1','2025-07-28 16:58:05.284','1',0,true),
	 ('1948333857853956098','1948333785175056385','0,1948333785175056385','授权管理',1,'授权管理','index','license/index',NULL,1,1,1,1,'user',NULL,NULL,NULL,'2025-07-24 18:46:04.666','1','2025-07-24 18:46:04.666','1',0,false),
	 ('1949756309946011649','1948306836448915457','0,1948306748607606785,1948306836448915457','导出数据',4,NULL,NULL,NULL,'teaching:export',0,1,1,4,NULL,NULL,NULL,NULL,'2025-07-28 16:58:23.675','1','2025-07-28 16:58:23.675','1',0,true),
	 ('1949756363863789569','1948306836448915457','0,1948306748607606785,1948306836448915457','删除',4,NULL,NULL,NULL,'teaching:delete',0,1,1,5,NULL,NULL,NULL,NULL,'2025-07-28 16:58:36.53','1','2025-07-28 16:58:36.53','1',0,true),
	 ('1949757596347437058','1948306836448915457','0,1948306748607606785,1948306836448915457','新建课程',4,NULL,NULL,NULL,'teaching:add',0,1,1,6,NULL,NULL,NULL,NULL,'2025-07-28 17:03:30.382','1','2025-07-28 17:03:30.382','1',0,true),
	 ('1948631762484105218','1948294624590172161','0,1948294624590172161','新建实验课程',1,'AddCourse','add','course/add-course',NULL,0,0,0,1,NULL,NULL,NULL,NULL,'2025-07-25 14:29:50.66','1','2025-07-25 14:37:02.882','1',0,true),
	 ('1948625642667413505','1947192344766386178','0,1947190419715067905,1947192344766386178','新增用户',4,NULL,NULL,NULL,'administrator:add',0,1,1,1,NULL,NULL,NULL,NULL,'2025-07-25 14:05:31.583','1','2025-07-25 14:05:31.583','1',0,true),
	 ('1948625711072317442','1947192344766386178','0,1947190419715067905,1947192344766386178','批量添加用户',4,NULL,NULL,NULL,'administrator:batch:add',0,1,1,2,NULL,NULL,NULL,NULL,'2025-07-25 14:05:47.9','1','2025-07-25 14:05:47.9','1',0,true),
	 ('1950444495261491201','1948306748607606785','0,1948306748607606785','实验数据',1,'ExperimentData','experiment-data','teaching/experiment-data',NULL,0,0,0,2,NULL,NULL,NULL,NULL,'2025-07-30 14:32:59.84','1','2025-07-30 17:05:02.997','1',0,true),
	 ('1950474698838859778','1950444495261491201','0,1948306748607606785,1950444495261491201','布置实验作业',4,NULL,NULL,NULL,'expData:assignHomework',0,1,1,1,NULL,NULL,NULL,NULL,'2025-07-30 16:33:00.942','1','2025-07-30 16:33:00.942','1',0,true),
	 ('1950474749048872962','1950444495261491201','0,1948306748607606785,1950444495261491201','批量删除',4,NULL,NULL,NULL,'expData:batchDelete',0,1,1,2,NULL,NULL,NULL,NULL,'2025-07-30 16:33:12.91','1','2025-07-30 16:33:59.539','1',0,true),
	 ('1950475019375960065','1950444495261491201','0,1948306748607606785,1950444495261491201','导出数据',4,NULL,NULL,NULL,'expData:export',0,1,1,3,NULL,NULL,NULL,NULL,'2025-07-30 16:34:17.35','1','2025-07-30 16:34:17.35','1',0,true),
	 ('1950475106009309186','1950444495261491201','0,1948306748607606785,1950444495261491201','删除',4,NULL,NULL,NULL,'expData:delete',0,1,1,4,NULL,NULL,NULL,NULL,'2025-07-30 16:34:38.004','1','2025-07-30 16:34:38.004','1',0,true),
	 ('1950489139105841153','1948306748607606785','0,1948306748607606785','学生管理',1,'StudentManage','student','teaching/student-manage',NULL,0,1,0,3,NULL,NULL,NULL,NULL,'2025-07-30 17:30:23.76','1945376569312702465','2025-07-31 15:25:23.8','1945376569312702465',0,true),
	 ('1950494553809698818','1950489139105841153','0,1948306748607606785,1950489139105841153','删除学生',4,NULL,NULL,NULL,'teaching:student:delete',0,1,1,1,NULL,NULL,NULL,NULL,'2025-07-30 17:51:54.722','1945376569312702465','2025-07-30 17:51:54.722','1945376569312702465',0,true),
	 ('1950494664665153538','1950489139105841153','0,1948306748607606785,1950489139105841153','新增学生',4,NULL,NULL,NULL,'teaching:student:add',0,1,1,1,NULL,NULL,NULL,NULL,'2025-07-30 17:52:21.16','1945376569312702465','2025-07-30 17:52:21.16','1945376569312702465',0,true),
	 ('1949763015845347330','1948306748607606785','0,1948306748607606785','新建课程',1,'CreateCourse','create','teaching/create-course',NULL,0,1,0,4,NULL,NULL,NULL,NULL,'2025-07-28 17:25:02.483','1','2025-07-31 15:25:29.094','1945376569312702465',0,true),
	 ('1948625796287991810','1947192344766386178','0,1947190419715067905,1947192344766386178','批量删除用户',4,NULL,NULL,NULL,'administrator:batch:delete',0,1,1,3,NULL,NULL,NULL,NULL,'2025-07-25 14:06:08.218','1','2025-07-25 14:06:08.218','1',0,true),
	 ('1948625897291026433','1947192344766386178','0,1947190419715067905,1947192344766386178','编辑用户',4,NULL,NULL,NULL,'administrator:edit',0,1,1,4,NULL,NULL,NULL,NULL,'2025-07-25 14:06:32.3','1','2025-07-25 14:06:32.3','1',0,true),
	 ('1948625987409842178','1947192344766386178','0,1947190419715067905,1947192344766386178','重置密码',4,NULL,NULL,NULL,'administrator:reset:password',0,1,1,5,NULL,NULL,NULL,NULL,'2025-07-25 14:06:53.78','1','2025-07-25 14:06:53.78','1',0,true),
	 ('1948626056863322114','1947192344766386178','0,1947190419715067905,1947192344766386178','删除用户',4,NULL,NULL,NULL,'administrator:delete',0,1,1,6,NULL,NULL,NULL,NULL,'2025-07-25 14:07:10.349','1','2025-07-25 14:07:18.075','1',0,true),
	 ('1948624307029053442','1947192763160793090','0,1947190419715067905,1947192763160793090','设置失效时间',4,NULL,NULL,NULL,'normal-user:set:validDate',0,1,1,4,NULL,NULL,NULL,NULL,'2025-07-25 14:00:13.146','1','2025-07-25 14:00:13.146','1',0,true),
	 ('1948624423727173633','1947192763160793090','0,1947190419715067905,1947192763160793090','编辑用户',4,NULL,NULL,NULL,'normal-user:edit',0,1,1,5,NULL,NULL,NULL,NULL,'2025-07-25 14:00:40.972','1','2025-07-25 14:00:40.972','1',0,true),
	 ('1948624496997470210','1947192763160793090','0,1947190419715067905,1947192763160793090','重置密码',4,NULL,NULL,NULL,'normal-user:reset:password',0,1,1,6,NULL,NULL,NULL,NULL,'2025-07-25 14:00:58.434','1','2025-07-25 14:00:58.434','1',0,true),
	 ('1948624591696465921','1947192763160793090','0,1947190419715067905,1947192763160793090','删除用户',4,NULL,NULL,NULL,'normal-user:delete',0,1,1,7,NULL,NULL,NULL,NULL,'2025-07-25 14:01:21.015','1','2025-07-25 14:01:21.015','1',0,true),
	 ('1952292215027945473','1948292534094532610','0,1948292384815058945,1948292534094532610','新增实验',4,NULL,NULL,NULL,'course:experiment:create',0,1,1,1,NULL,NULL,NULL,NULL,'2025-08-04 16:55:10.561','1945376569312702465','2025-08-04 16:55:10.561','1945376569312702465',0,true),
	 ('1952292331944169474','1948292534094532610','0,1948292384815058945,1948292534094532610','删除实验',4,NULL,NULL,NULL,'course:experiment:delete',0,1,1,1,NULL,NULL,NULL,NULL,'2025-08-04 16:55:38.433','1945376569312702465','2025-08-04 16:55:38.433','1945376569312702465',0,true),
	 ('1952292682214690817','1948292534094532610','0,1948292384815058945,1948292534094532610','复制实验',4,NULL,NULL,NULL,'course:experiment:duplicate',0,1,1,1,NULL,NULL,NULL,NULL,'2025-08-04 16:57:01.946','1945376569312702465','2025-08-04 16:57:01.946','1945376569312702465',0,true),
	 ('1952292900503048193','1948292534094532610','0,1948292384815058945,1948292534094532610','开始实验',4,NULL,NULL,NULL,'course:experiment:open',0,1,1,1,NULL,NULL,NULL,NULL,'2025-08-04 16:57:53.987','1945376569312702465','2025-08-04 16:57:53.987','1945376569312702465',0,true),
	 ('1952293034771107841','1948292534094532610','0,1948292384815058945,1948292534094532610','导出实验',4,NULL,NULL,NULL,'course:experiment:export',0,1,1,1,NULL,NULL,NULL,NULL,'2025-08-04 16:58:26.002','1945376569312702465','2025-08-04 16:58:26.002','1945376569312702465',0,true),
	 ('1952292834446954497','1948292534094532610','0,1948292384815058945,1948292534094532610','重命名实验',4,NULL,NULL,NULL,'course:experiment:rename',0,1,1,1,NULL,NULL,NULL,NULL,'2025-08-04 16:57:38.245','1945376569312702465','2025-08-06 11:11:41.296','1945376569312702465',0,true)
	 ON CONFLICT (id) DO NOTHING;

INSERT INTO sys_official (id,created_date,created_by,modified_date,modified_by,is_deleted,remark,"name",code,"type") VALUES
	 ('1947574895393746945','2025-07-22 16:30:13.912','1','2025-07-22 16:30:13.912','1',0,NULL,'网络工程1班',NULL,'CLASS'),
	 ('1947863247443464193','2025-07-23 11:36:02.399','1','2025-07-23 11:36:02.399','1',0,NULL,'网络工程3班',NULL,'CLASS'),
	 ('1947574895498604545','2025-07-22 16:30:13.936','1','2025-07-22 16:30:13.936','1',0,NULL,'软件工程2班',NULL,'CLASS'),
	 ('1947864424931459073','2025-07-23 11:40:43.132','1','2025-07-23 11:40:43.132','1',0,NULL,'计算机科学4班',NULL,'CLASS'),
	 ('1948564755000651778','2025-07-25 10:03:34.842','1','2025-07-25 10:03:34.842','1',0,NULL,'网络工程2班',NULL,'CLASS')
	 ON CONFLICT (id) DO NOTHING;

INSERT INTO sys_role (id,"name","level",remark,created_date,created_by,modified_date,modified_by,is_deleted) VALUES
	 ('1','super_admin',1,'超级管理员','2025-07-21 09:39:07.168',NULL,'2025-07-21 09:39:07.168','1',0),
	 ('3','user',1,'普通用户','2025-07-21 09:40:07.168',NULL,'2025-07-21 09:39:07.168',NULL,0),
	 ('2','admin',1,'管理员','2025-07-21 09:41:07.168',NULL,'2025-07-21 09:39:07.168',NULL,0),
	 ('1947487022204145666','323',23,'32','2025-07-22 10:41:03.333','1','2025-07-22 10:55:48.215','1',1),
	 ('1947534882543067137','32',32,'32','2025-07-21 09:39:07.168','1','2025-07-22 13:51:33.617','1',1)
	 ON CONFLICT (id) DO NOTHING;

INSERT INTO sys_role_menu (role_id,menu_id) VALUES
	 ('2','1948294624590172161'),
	 ('2','1948294763480354817'),
	 ('2','1948620557*********'),
	 ('2','1948620647662391297'),
	 ('2','1948631762484105218'),
	 ('2','1948333785175056385'),
	 ('2','1948333857853956098'),
	 ('2','1948292384815058945'),
	 ('2','1948292534094532610'),
	 ('2','1952292834446954497'),
	 ('2','1952292682214690817'),
	 ('2','1952292900503048193'),
	 ('2','1952292331944169474'),
	 ('2','1952292215027945473'),
	 ('2','1952293034771107841'),
	 ('2','1948306748607606785'),
	 ('2','1948306836448915457'),
	 ('2','1949755908324626434'),
	 ('2','1949756122091524097'),
	 ('2','1949756232829538306'),
	 ('2','1949756309946011649'),
	 ('2','1949756363863789569'),
	 ('2','1949757596347437058'),
	 ('2','1950444495261491201'),
	 ('2','1950474698838859778'),
	 ('2','1950474749048872962'),
	 ('2','1950475019375960065'),
	 ('2','1950475106009309186'),
	 ('2','1950483044484603906'),
	 ('2','1950489139105841153'),
	 ('2','1950494553809698818'),
	 ('2','1950494664665153538'),
	 ('2','1949763015845347330'),
	 ('2','1'),
	 ('2','3'),
	 ('2','139'),
	 ('2','70'),
	 ('2','71'),
	 ('2','72'),
	 ('2','4'),
	 ('2','140'),
	 ('2','73'),
	 ('2','74'),
	 ('2','75'),
	 ('2','1947493133101146113'),
	 ('2','1947493340710805505'),
	 ('2','1947190419715067905'),
	 ('2','1947192344766386178'),
	 ('2','1948625642667413505'),
	 ('2','1948625711072317442'),
	 ('2','1948625796287991810'),
	 ('2','1948625897291026433'),
	 ('2','1948625987409842178'),
	 ('2','1948626056863322114'),
	 ('2','1947192763160793090'),
	 ('2','1948624054167048193'),
	 ('2','1948624150195638273'),
	 ('2','1948624228046114817'),
	 ('2','1948624307029053442'),
	 ('2','1948624423727173633'),
	 ('2','1948624496997470210'),
	 ('2','1948624591696465921'),
	 ('2','1951176748111044610'),
	 ('2','36'),
	 ('2','108'),
	 ('2','109'),
	 ('2','37'),
	 ('2','38'),
	 ('2','39'),
	 ('2','95'),
	 ('2','146'),
	 ('2','147'),
	 ('2','89'),
	 ('2','97'),
	 ('2','90'),
	 ('2','148')
	 ON CONFLICT (role_id, menu_id) DO NOTHING;

INSERT INTO sys_user (id,"name","password",salt,email,mobile_phone,status,avatar,user_type,valid_date,remark,created_date,created_by,modified_date,modified_by,login_ip,login_date,is_deleted,account,is_default_pwd) VALUES
	 ('1945781646794469377','王五','$2a$10$HKjGidptjqXabBNm3f.jFO3VO.egm457qtdd8xmHqJ5pV7GlLP8Oe','UF+isVESVbZbG7JZWyoumA==',NULL,'',1,NULL,'STUDENT','2024-08-21 00:00:00',NULL,'2025-07-17 17:44:30.128','1945376569312702465','2025-08-12 14:58:57.38','1945376569312702465',NULL,NULL,0,'32323',true),
	 ('1945760184972972033','张三','$2a$10$J8eHXIVBt/Rk912x0cWqw.5n1VedZtuYGRA/BRRDrWZVdKAmwqm.C','rs5RVh5wzSiJPYXd5JtAJw==',NULL,'***********',1,NULL,'TEACHER',NULL,NULL,'2025-07-17 16:19:13.239','1945376569312702465','2025-08-13 15:45:42.428','1945376569312702465',NULL,NULL,0,'23232',true),
	 ('1950070990376095746','张三','$2a$10$76NsJfr1JT7bXxGLoPNpQuMKQXRGbGpnNNkCwLptJF5tie1V/ldOq','9INJIeDqOg8yBXVU34K8Ug==',NULL,NULL,1,NULL,'STUDENT','2024-08-21 00:00:00',NULL,'2025-07-29 13:48:49.33','1','2025-08-13 16:36:15.895','1945376569312702465',NULL,NULL,0,'3324',true),
	 ('1948564754728022017','123','$2a$10$sgycWZ1Oi1KQtAowsNltEutScfjMrOb5HQSt4qXqibppJQQvoPA72','idw5XAuO73zQ7Z3m4PqkgQ==',NULL,'',1,NULL,'STUDENT','2025-08-30 00:00:00',NULL,'2025-07-25 10:03:34.768','1945376569312702465','2025-07-29 13:43:27.9','1',NULL,NULL,0,'测试人员',true),
	 ('19453765693127024651','肖斌','$2a$10$za5zi4DXcesG.SfP9Y10u.QtHzQVfZwMKzS4fWL0hNBSyHpyVEcgO','MZPYobGjbW8DLL7j14nD1Q==',NULL,NULL,NULL,'https://foruda.gitee.com/images/1723603502796844527/03cdca2a_716974.gif?imageView2/1/w/80/h/80','TEACHER',NULL,NULL,NULL,'1945376569312702465','2025-07-28 11:19:14.018','1945376569312702465',NULL,NULL,0,'xiaobin',false),
	 ('1947863247254720513','赵六','$2a$10$kk0j2NH2AkwxC.gFaCA.suuDUYiBx/xT.Bcg05pICYKRMnvF8z2XC','D0bX5wt6UpX72pYgZe0EUA==',NULL,NULL,1,NULL,'STUDENT','2025-07-31 08:00:00',NULL,'2025-07-23 11:36:02.352','1945376569312702465','2025-07-25 10:05:33.188','1945376569312702465',NULL,NULL,1,'322',true),
	 ('1947211129879597058','李四','$2a$10$.Axy1cQbEZ4f7hSIOzc4B.kJy5Q0BMh.nZrnFFrLlDP6pUKZlI6zO','mwEFku8Q7/bFh9LI5gB30g==',NULL,'18373833748',1,NULL,'TEACHER',NULL,NULL,'2025-07-21 16:24:45.46','1945376569312702465','2025-08-07 17:13:46.871','1945376569312702465',NULL,NULL,1,'323',true),
	 ('1945376569312702465','管理员','$2a$10$JH.vwq0jQQtMaxKSmp/Xlea94U7ozUNgSIZ8PCTHYWynXOOyM6jzm','MZPYobGjbW8DLL7j14nD1Q==',NULL,NULL,NULL,'https://foruda.gitee.com/images/1723603502796844527/03cdca2a_716974.gif?imageView2/1/w/80/h/80','TEACHER',NULL,NULL,NULL,'1945376569312702465','2025-07-28 10:01:04.783','1945376569312702465',NULL,NULL,0,'admin',false)
	 ON CONFLICT (id) DO NOTHING;

INSERT INTO sys_user_official (user_id,official_id) VALUES
	 ('1948564754728022017','1947574895393746945'),
	 ('1947863247254720513','1947574895393746945'),
	 ('1950070990376095746','1947574895393746945'),
	 ('1945781646794469377','1948564755000651778');

INSERT INTO sys_user_role (user_id,role_id) VALUES
	 ('1945376569312702465','1'),
	 ('1945760184972972033','2'),
	 ('1950070990376095746','3')
	 ON CONFLICT (user_id,role_id) DO NOTHING;
