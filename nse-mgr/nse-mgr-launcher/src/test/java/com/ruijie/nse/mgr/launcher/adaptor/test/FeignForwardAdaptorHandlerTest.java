package com.ruijie.nse.mgr.launcher.adaptor.test;


import com.ruijie.nse.common.config.security.UserPrincipal;
import com.ruijie.nse.mgr.course.constants.CourseConstants;
import com.ruijie.nse.mgr.launcher.NseMgrApplication;
import com.ruijie.nse.mgr.py3server.config.Py3Constants;
import com.ruijie.nse.mgr.py3server.dto.ProjectDto;
import com.ruijie.nse.mgr.py3server.feign.adaptor.Python3FeignForwardAdaptor;
import com.ruijie.nse.mgr.py3server.feign.adaptor.handler.FeignForwardAdaptorHandler;
import com.ruijie.nse.mgr.py3server.service.SerHostsService;
import com.ruijie.nse.mgr.repository.entity.SerHosts;
import com.ruijie.nse.mgr.repository.pojo.bo.BasicAuthBo;
import com.ruijie.nse.mgr.sys.service.LoginService;
import org.dromara.hutool.core.data.id.IdUtil;
import org.dromara.hutool.core.util.RandomUtil;
import org.dromara.hutool.http.client.Response;
import org.dromara.hutool.http.client.body.MultipartBody;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;

import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;

@SpringBootTest(classes = NseMgrApplication.class)
class FeignForwardAdaptorHandlerTest {

    @Autowired
    private FeignForwardAdaptorHandler feignForwardAdaptorHandler;
    @Autowired
    private LoginService loginService;
    @Autowired
    private SerHostsService serHostsService;

    @BeforeEach
    public void setup() {
        // 模拟gns3server环境变量
        System.setProperty(Py3Constants.Server.PY3_SERVER_BASIC_AUTH_USER_KEY, "nse");
        System.setProperty(Py3Constants.Server.PY3_SERVER_BASIC_AUTH_SECRET_KEY, "nse");
        // 模拟用户登录
        UserPrincipal userPrincipal = new UserPrincipal();
        userPrincipal.setUserId("1");
        userPrincipal.setAccount("pengyaohuang");
        userPrincipal.setUsername("彭耀煌");
        userPrincipal.setPermissions(List.of());

        SecurityContextHolder.getContext().setAuthentication(
                new UsernamePasswordAuthenticationToken(
                        userPrincipal,
                        null,
                        userPrincipal.getAuthorities()
                )
        );


        // mock一条ser_hosts
        SerHosts serHosts = new SerHosts();
        serHosts.setServerIp("127.0.0.1");
        serHosts.setServerPort(3088);
        serHosts.setUserId("1");
        serHosts.setStoragePath("./workspace/nse-storage");
        serHosts.setBasicAuth(new BasicAuthBo("nse", "nse"));
        serHostsService.save(serHosts);
    }


    @AfterEach
    public void afterEach() {
        serHostsService.removeByUserId("1");
    }

    @Test
//    @WithUserDetails(value = "admin", userDetailsServiceBeanName = "userDetailsService")
    void testCreateProject() {
        feignForwardAdaptorHandler.createProject(IdUtil.fastUUID(), "新建测试项目");
    }

    @Test
//    @WithUserDetails(value = "admin", userDetailsServiceBeanName = "userDetailsService")
    void testDeleteProject() {
        feignForwardAdaptorHandler.deleteProject("a7c7b173-5483-4e54-a7c7-b17354834e54");
    }

    @Test
    void testImportProject() {
        String projectId = IdUtil.fastUUID();
        System.out.println("项目ID=" + projectId);
        feignForwardAdaptorHandler.importProject(projectId, "1111111111111111", Path.of("F:\\GNS3_DATA\\course_repo\\test_repo.nseproject"));
    }

    @Test
    void duplicateProject() {
        feignForwardAdaptorHandler.duplicateProject("a6dd4869-3e18-a6e8-a6dd-48693e18a6e8", "1111111111111111");
    }

    @Test
    void exportProject() {
        ProjectDto projectDto = feignForwardAdaptorHandler.exportProject("a6dd4869-3e18-a6e8-a6dd-48693e18a6e8");
        String projectPath = projectDto.getProjectPath();
        System.out.println(projectPath);
    }



    @TestConfiguration
    static class MockConfig {
        @Bean
        public LoginService loginService() {
            return Mockito.mock(LoginService.class);
        }
    }

}
