package com.ruijie.nse.mgr.license.config;

import com.ruijie.nse.mgr.license.constants.LicenseConstants;
import com.ruijie.nse.mgr.common.context.LicenseContext;
import com.ruijie.nse.mgr.license.pojo.LicenseValidationResult;
import com.ruijie.nse.mgr.license.service.LicenseActivationInfoService;
import com.ruijie.nse.mgr.license.service.LicenseV1Service;
import com.ruijie.nse.mgr.repository.entity.LicenseActivationInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * License定时任务配置
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Configuration
@EnableScheduling
@RequiredArgsConstructor
public class LicenseSchedulerConfig {

    private final LicenseV1Service licenseService;
    private final LicenseActivationInfoService licenseActivationInfoService;

    /**
     * 定时验证License
     * 每5分钟执行一次License验证
     */
    @Scheduled(fixedRate = 5 * 60 * 1000)
    public void scheduledLicenseValidation() {
        try {
            log.debug("开始定时License验证");
            // 如果不存在已授权记录，那么直接返回
            LicenseActivationInfo currentLicense = licenseActivationInfoService.getCurrentLicense();
            if(currentLicense == null || !currentLicense.getStatus().equalsIgnoreCase(LicenseConstants.Status.ACTIVE)) {
                log.warn(" × 不存在已授权license，跳过验证");
                LicenseContext.setValid(false);
                return ;
            }
            LicenseValidationResult result = licenseService.validateCurrentLicense(LicenseContext.getLicense());
            log.info("license auth result: {}", result);
            LicenseContext.setValid(result != null ? result.getValid() : false);
            log.debug("定时License验证完成");
        } catch (Exception e) {
            log.error("定时License验证失败", e);
        }
    }
}
