package com.ruijie.nse.mgr.license.config;

import com.ruijie.nse.mgr.license.constants.LicenseConstants;
import com.ruijie.nse.mgr.common.context.LicenseContext;
import com.ruijie.nse.mgr.license.pojo.LicenseValidationResult;
import com.ruijie.nse.mgr.license.service.LicenseActivationInfoService;
import com.ruijie.nse.mgr.license.service.LicenseV1Service;
import com.ruijie.nse.mgr.repository.entity.LicenseActivationInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;


/**
 * 项目启动检测，校验license
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LicenseVerifyRunner implements CommandLineRunner {

    private final LicenseV1Service licenseService;
    private final LicenseActivationInfoService licenseActivationInfoService;

    @Override
    public void run(String... args) throws Exception {
        try {
            log.debug("程序启动，开始License验证");
            // 如果不存在已授权记录，那么直接返回
            LicenseActivationInfo currentLicense = licenseActivationInfoService.getCurrentLicense();
            if(currentLicense == null || !currentLicense.getStatus().equalsIgnoreCase(LicenseConstants.Status.ACTIVE)) {
                log.warn(" × 不存在已授权license，跳过验证");
                LicenseContext.setValid(false);
                return ;
            }
            LicenseValidationResult result = licenseService.validateCurrentLicense();
            log.info("license auth result: {}", result);
            LicenseContext.setValid(result != null ? result.getValid() : false);
            log.debug("程序启动License验证完成");
        } catch (Exception e) {
            log.error(" × 程序启动License验证失败", e);
        }
    }
}
