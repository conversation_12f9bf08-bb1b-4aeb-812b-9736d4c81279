package com.ruijie.nse.mgr.license.config;

import com.ruijie.nse.mgr.license.web.filter.LicenseThreadLocalInterceptor;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class LicenseWebConfig implements WebMvcConfigurer {

    @Resource
    private LicenseThreadLocalInterceptor licenseThreadLocalInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(licenseThreadLocalInterceptor);
    }

}
