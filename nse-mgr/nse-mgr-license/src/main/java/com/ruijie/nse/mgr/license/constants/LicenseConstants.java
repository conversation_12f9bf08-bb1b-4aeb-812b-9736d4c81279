package com.ruijie.nse.mgr.license.constants;

import java.nio.file.Path;
import java.nio.file.Paths;

public interface LicenseConstants {

    /**
     * 授权状态
     */
    interface Status {
        String INACTIVE = "未授权";
        String ACTIVE = "已授权";
        String EXPIRED = "已过期";
        String REVOKED = "已撤销";
        String CANCELED = "已注销";
        String VERIFY_FAILED = "LICENSE错误";
    }

    interface Format {
        String VERSION_1_0 = "NSE-LICENSE-1.0";
        String EXTENSION_1_0 = ".lic";
    }

    /**
     * 证书扩展文件
     */
    interface LicenseExt {
        Path LICD_PATH = Paths.get(System.getProperty("user.dir"),"workspace", ".nse", ".licd");
        Path LICF_PATH = Paths.get(System.getProperty("user.dir"),"workspace", ".nse", ".licf");
        Path SYS_UPTIME_PATH = Paths.get("/", "proc", "uptime");
    }

    interface Message {
        String LICENSE_EXPIRED_OR_NOT_FOUNT = "请检查License服务是否到期或License文件是否已经导入";
    }
}
