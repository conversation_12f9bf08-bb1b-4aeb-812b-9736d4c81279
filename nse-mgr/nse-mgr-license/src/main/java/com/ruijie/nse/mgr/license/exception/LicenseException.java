package com.ruijie.nse.mgr.license.exception;

import lombok.Getter;
import org.dromara.hutool.http.meta.HttpStatus;

/**
 * License相关异常
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Getter
public class LicenseException extends RuntimeException {

    private final Integer code;

    public LicenseException(Integer code) {
        this.code = code;
    }

    public LicenseException(Integer code, String message) {
        super(message);
        this.code = code;
    }

    public LicenseException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public LicenseException(Integer code, Throwable cause) {
        super(cause);
        this.code = code;
    }

    public LicenseException(Integer code, String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.code = code;
    }

    /**
     * License不存在异常
     */
    public static class LicenseNotFoundException extends LicenseException {
        public LicenseNotFoundException(String licenseId) {
            super(HttpStatus.HTTP_NOT_FOUND, licenseId);
        }
    }

    /**
     * License已过期异常
     */
    public static class LicenseExpiredException extends LicenseException {
        public LicenseExpiredException(String licenseId) {
            super(HttpStatus.HTTP_FORBIDDEN, licenseId);
        }
    }

    /**
     * License签名无效异常
     */
    public static class LicenseSignatureInvalidException extends LicenseException {
        public LicenseSignatureInvalidException(String licenseId) {
            super(HttpStatus.HTTP_UNAUTHORIZED, licenseId);
        }
    }

    /**
     * License格式错误异常
     */
    public static class LicenseFormatException extends LicenseException {
        public LicenseFormatException(String message) {
            super(HttpStatus.HTTP_BAD_REQUEST, message);
        }
    }

    /**
     * 硬件指纹不匹配异常
     */
    public static class HardwareFingerprintMismatchException extends LicenseException {
        public HardwareFingerprintMismatchException() {
            super(HttpStatus.HTTP_UNAUTHORIZED, "硬件指纹不匹配");
        }
    }

    /**
     * 用户数超限异常
     */
    public static class UserLimitExceededException extends LicenseException {
        public UserLimitExceededException(int current, int limit) {
            super(HttpStatus.HTTP_BANDWIDTH_LIMIT_EXCEEDED,
                  String.format("用户数超限: 当前%d, 限制%d", current, limit));
        }
    }

    /**
     * 模块未授权异常
     */
    public static class ModuleNotAuthorizedException extends LicenseException {
        public ModuleNotAuthorizedException(String module) {
            super(HttpStatus.HTTP_UNAUTHORIZED, "模块未授权: " + module);
        }
    }

    /**
     * License解析异常
     */
    public static class LicenseParseException extends LicenseException {
        public LicenseParseException(String message, Throwable cause) {
            super(HttpStatus.HTTP_INTERNAL_ERROR, message, cause);
        }

        public LicenseParseException(String message) {
            super(HttpStatus.HTTP_INTERNAL_ERROR, message);
        }
    }

    /**
     * License验证异常
     */
    public static class LicenseValidationException extends LicenseException {
        public LicenseValidationException(String message) {
            super(HttpStatus.HTTP_UNAUTHORIZED, message);
        }
    }
}
