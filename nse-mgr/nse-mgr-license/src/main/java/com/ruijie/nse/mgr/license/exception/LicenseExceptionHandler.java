package com.ruijie.nse.mgr.license.exception;

import com.ruijie.nse.common.dto.R;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.http.meta.HttpStatus;
import org.springframework.core.annotation.Order;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * License异常处理器
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@RestControllerAdvice
@Order(1) // 优先级高于全局异常处理器
public class LicenseExceptionHandler {


    /**
     * 处理License验证失败异常
     * 
     * @param e License异常
     * @return 错误响应
     */
    @ExceptionHandler(LicenseException.class)
    @ResponseStatus(org.springframework.http.HttpStatus.FORBIDDEN)
    public R<Object> handleLicenseValidationException(LicenseException e) {
        if (HttpStatus.HTTP_FORBIDDEN == e.getCode()) {
            log.error("License已过期: {}", e.getMessage());
            return R.error(e.getCode(), "License已过期，请联系管理员更新License");
        }

        if (HttpStatus.HTTP_UNAUTHORIZED == e.getCode()) {
            log.error("License校验失败: {}", e.getMessage());
            return R.error(e.getCode(), "License校验失败，请检查License文件完整性");
        }

        if (HttpStatus.HTTP_BANDWIDTH_LIMIT_EXCEEDED == e.getCode()) {
            log.warn("用户数超限: {}", e.getMessage());
            return R.error(e.getCode(), "用户数已达到License限制，无法继续使用");
        }
        
        // 默认处理
        log.error("License验证异常: {} - {}", e.getCode(), e.getMessage());
        return R.error(e.getCode(), e.getMessage());
    }

}
