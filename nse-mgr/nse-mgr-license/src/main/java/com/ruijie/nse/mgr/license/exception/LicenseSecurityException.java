package com.ruijie.nse.mgr.license.exception;

import lombok.Getter;
import org.dromara.hutool.http.meta.HttpStatus;

/**
 * License安全异常类
 * 用于处理License相关的安全异常情况
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Getter
public class LicenseSecurityException extends LicenseException {

    /**
     * 安全异常类型枚举
     */
    @Getter
    public enum SecurityExceptionType {
        ENCRYPTION_FAILED("加密失败"),
        DECRYPTION_FAILED("解密失败"),
        SIGNATURE_VERIFICATION_FAILED("数字签名验证失败"),
        INTEGRITY_CHECK_FAILED("完整性校验失败"),
        HARDWARE_BINDING_FAILED("硬件绑定验证失败"),
        TAMPER_DETECTED("检测到篡改"),
        DEBUG_ENVIRONMENT_DETECTED("检测到调试环境"),
        UNAUTHORIZED_ACCESS("未授权访问"),
        KEY_DERIVATION_FAILED("密钥派生失败"),
        SECURITY_POLICY_VIOLATION("安全策略违规");

        private final String description;

        SecurityExceptionType(String description) {
            this.description = description;
        }

    }

    /**
     * -- GETTER --
     *  获取安全异常类型
     *
     * @return 安全异常类型
     */
    private final SecurityExceptionType securityType;
    /**
     * -- GETTER --
     *  获取安全详情
     *
     * @return 安全详情
     */
    private final String securityDetails;

    /**
     * 构造函数
     * 
     * @param message 异常消息
     */
    public LicenseSecurityException(String message) {
        super(HttpStatus.HTTP_INTERNAL_ERROR, message);
        this.securityType = null;
        this.securityDetails = null;
    }

    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param cause 原因异常
     */
    public LicenseSecurityException(String message, Throwable cause) {
        super(HttpStatus.HTTP_INTERNAL_ERROR, message, cause);
        this.securityType = null;
        this.securityDetails = null;
    }

    /**
     * 构造函数
     * 
     * @param securityType 安全异常类型
     * @param message 异常消息
     */
    public LicenseSecurityException(SecurityExceptionType securityType, String message) {
        super(HttpStatus.HTTP_INTERNAL_ERROR, message);
        this.securityType = securityType;
        this.securityDetails = null;
    }

    /**
     * 构造函数
     * 
     * @param securityType 安全异常类型
     * @param message 异常消息
     * @param cause 原因异常
     */
    public LicenseSecurityException(SecurityExceptionType securityType, String message, Throwable cause) {
        super(HttpStatus.HTTP_INTERNAL_ERROR, message, cause);
        this.securityType = securityType;
        this.securityDetails = null;
    }

    /**
     * 构造函数
     * 
     * @param securityType 安全异常类型
     * @param message 异常消息
     * @param securityDetails 安全详情
     */
    public LicenseSecurityException(SecurityExceptionType securityType, String message, String securityDetails) {
        super(HttpStatus.HTTP_INTERNAL_ERROR, message);
        this.securityType = securityType;
        this.securityDetails = securityDetails;
    }

    /**
     * 构造函数
     * 
     * @param securityType 安全异常类型
     * @param message 异常消息
     * @param securityDetails 安全详情
     * @param cause 原因异常
     */
    public LicenseSecurityException(SecurityExceptionType securityType, String message, String securityDetails, Throwable cause) {
        super(HttpStatus.HTTP_INTERNAL_ERROR, message, cause);
        this.securityType = securityType;
        this.securityDetails = securityDetails;
    }

    /**
     * 获取安全异常类型描述
     * 
     * @return 安全异常类型描述
     */
    public String getSecurityTypeDescription() {
        return securityType != null ? securityType.getDescription() : "未知安全异常";
    }

    /**
     * 获取完整的异常信息
     * 
     * @return 完整的异常信息
     */
    public String getFullMessage() {
        StringBuilder sb = new StringBuilder();
        
        if (securityType != null) {
            sb.append("[").append(securityType.getDescription()).append("] ");
        }
        
        sb.append(getMessage());
        
        if (securityDetails != null && !securityDetails.isEmpty()) {
            sb.append(" - 详情: ").append(securityDetails);
        }
        
        return sb.toString();
    }

    /**
     * 判断是否为高风险安全异常
     * 
     * @return 是否为高风险
     */
    public boolean isHighRisk() {
        if (securityType == null) {
            return false;
        }

        return switch (securityType) {
            case TAMPER_DETECTED, DEBUG_ENVIRONMENT_DETECTED, UNAUTHORIZED_ACCESS, SECURITY_POLICY_VIOLATION -> true;
            default -> false;
        };
    }

    /**
     * 判断是否需要立即响应
     * 
     * @return 是否需要立即响应
     */
    public boolean requiresImmediateResponse() {
        return isHighRisk();
    }

    @Override
    public String toString() {
        return "LicenseSecurityException{" +
                "securityType=" + securityType +
                ", securityDetails='" + securityDetails + '\'' +
                ", message='" + getMessage() + '\'' +
                '}';
    }

    // 静态工厂方法，用于创建常见的安全异常

    /**
     * 创建加密失败异常
     * 
     * @param details 详细信息
     * @return 安全异常
     */
    public static LicenseSecurityException encryptionFailed(String details) {
        return new LicenseSecurityException(
                SecurityExceptionType.ENCRYPTION_FAILED,
                "License内容加密失败",
                details
        );
    }

    /**
     * 创建解密失败异常
     * 
     * @param details 详细信息
     * @return 安全异常
     */
    public static LicenseSecurityException decryptionFailed(String details) {
        return new LicenseSecurityException(
                SecurityExceptionType.DECRYPTION_FAILED,
                "License内容解密失败",
                details
        );
    }

    /**
     * 创建签名验证失败异常
     * 
     * @param details 详细信息
     * @return 安全异常
     */
    public static LicenseSecurityException signatureVerificationFailed(String details) {
        return new LicenseSecurityException(
                SecurityExceptionType.SIGNATURE_VERIFICATION_FAILED,
                "License数字签名验证失败",
                details
        );
    }

    /**
     * 创建完整性校验失败异常
     * 
     * @param details 详细信息
     * @return 安全异常
     */
    public static LicenseSecurityException integrityCheckFailed(String details) {
        return new LicenseSecurityException(
                SecurityExceptionType.INTEGRITY_CHECK_FAILED,
                "License完整性校验失败",
                details
        );
    }

    /**
     * 创建硬件绑定失败异常
     * 
     * @param details 详细信息
     * @return 安全异常
     */
    public static LicenseSecurityException hardwareBindingFailed(String details) {
        return new LicenseSecurityException(
                SecurityExceptionType.HARDWARE_BINDING_FAILED,
                "License硬件绑定验证失败",
                details
        );
    }

    /**
     * 创建篡改检测异常
     * 
     * @param details 详细信息
     * @return 安全异常
     */
    public static LicenseSecurityException tamperDetected(String details) {
        return new LicenseSecurityException(
                SecurityExceptionType.TAMPER_DETECTED,
                "检测到License被篡改",
                details
        );
    }

    /**
     * 创建调试环境检测异常
     * 
     * @param details 详细信息
     * @return 安全异常
     */
    public static LicenseSecurityException debugEnvironmentDetected(String details) {
        return new LicenseSecurityException(
                SecurityExceptionType.DEBUG_ENVIRONMENT_DETECTED,
                "检测到调试环境",
                details
        );
    }

    /**
     * 创建未授权访问异常
     * 
     * @param details 详细信息
     * @return 安全异常
     */
    public static LicenseSecurityException unauthorizedAccess(String details) {
        return new LicenseSecurityException(
                SecurityExceptionType.UNAUTHORIZED_ACCESS,
                "未授权的License访问",
                details
        );
    }

    /**
     * 创建密钥派生失败异常
     * 
     * @param details 详细信息
     * @return 安全异常
     */
    public static LicenseSecurityException keyDerivationFailed(String details) {
        return new LicenseSecurityException(
                SecurityExceptionType.KEY_DERIVATION_FAILED,
                "License密钥派生失败",
                details
        );
    }

    /**
     * 创建安全策略违规异常
     * 
     * @param details 详细信息
     * @return 安全异常
     */
    public static LicenseSecurityException securityPolicyViolation(String details) {
        return new LicenseSecurityException(
                SecurityExceptionType.SECURITY_POLICY_VIOLATION,
                "License安全策略违规",
                details
        );
    }
}
