package com.ruijie.nse.mgr.license.parser;

import com.ruijie.nse.mgr.license.exception.LicenseException;
import com.ruijie.nse.mgr.license.exception.LicenseSecurityException;
import com.ruijie.nse.mgr.license.parser.handler.LicenseParser;
import com.ruijie.nse.mgr.license.service.RuntimeDetectedService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.extra.spring.SpringUtil;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * License解析器工厂
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LicenseParserFactory {

    private final List<LicenseParser> parsers;
    private final RuntimeDetectedService runtimeDetectedService;

    /**
     * 根据格式获取解析器
     * 
     * @param format License格式
     * @return License解析器
     */
    public LicenseParser getParserByFormat(String format) {
        // 安全运行时检查
        performSecurityCheck();

        for (LicenseParser parser : parsers) {
            if (parser.supports(format)) {
                log.debug("根据格式{}找到解析器: {}", format, parser.getClass().getSimpleName());
                return parser;
            }
        }
        
        log.error("不支持的License格式: {}", format);
        throw new LicenseException.LicenseFormatException("不支持的License格式: " + format);
    }

    /**
     * 获取所有支持的格式
     * 
     * @return 支持的格式列表
     */
    public String[] getAllSupportedFormats() {
        return parsers.stream()
                .flatMap(parser -> Arrays.stream(parser.getSupportedFormats()))
                .distinct()
                .toArray(String[]::new);
    }

    /**
     * 检查是否支持指定格式
     * 
     * @param format License格式
     * @return 是否支持
     */
    public boolean isFormatSupported(String format) {
        return parsers.stream()
                .anyMatch(parser -> parser.supports(format));
    }

    /**
     * 执行安全检查
     */
    private void performSecurityCheck() {
        String profile = SpringUtil.getActiveProfile();
        if(!profile.equalsIgnoreCase("prod")) {
            return ;
        }
        RuntimeDetectedService.SecurityStatus securityStatus = runtimeDetectedService.detectThreats();

        if (securityStatus.hasThreats() &&
                securityStatus.getSecurityLevel() == RuntimeDetectedService.SecurityLevel.HIGH_RISK) {

            throw LicenseSecurityException.tamperDetected(
                    "检测到高风险安全威胁，拒绝License操作"
            );
        }
    }
}
