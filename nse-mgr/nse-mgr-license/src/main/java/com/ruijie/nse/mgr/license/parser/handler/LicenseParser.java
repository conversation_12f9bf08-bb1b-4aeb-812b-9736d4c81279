package com.ruijie.nse.mgr.license.parser.handler;


import com.ruijie.nse.mgr.common.dto.License;

import java.io.File;
import java.io.InputStream;

/**
 * License解析器接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface LicenseParser {

    /**
     * 解析License字符串
     *
     * @param licenseContent License内容字符串
     * @return License对象
     */
    License parse(String licenseContent);

    /**
     * 解析License文件
     *
     * @param licenseFile License文件
     * @return License对象
     */
    License parse(File licenseFile);

    /**
     * 解析License输入流
     *
     * @param inputStream License输入流
     * @return License对象
     */
    License parse(InputStream inputStream);

    /**
     * 检查是否支持指定格式
     *
     * @param format License格式
     * @return 是否支持
     */
    boolean supports(String format);

    /**
     * 获取支持的格式列表
     *
     * @return 支持的格式列表
     */
    String[] getSupportedFormats();
}