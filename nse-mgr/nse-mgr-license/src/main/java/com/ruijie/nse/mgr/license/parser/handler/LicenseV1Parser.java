package com.ruijie.nse.mgr.license.parser.handler;

import com.ruijie.nse.common.utils.oshi.HardwareUtil;
import com.ruijie.nse.mgr.common.utils.SM2Utils;
import com.ruijie.nse.mgr.license.constants.LicenseConstants;
import com.ruijie.nse.mgr.license.exception.LicenseException;
import com.ruijie.nse.mgr.common.dto.License;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.dromara.hutool.core.codec.binary.Hex;
import org.dromara.hutool.core.date.DateUtil;
import org.dromara.hutool.core.io.IoUtil;
import org.dromara.hutool.core.text.StrUtil;
import org.dromara.hutool.json.JSONUtil;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.Base64;
import java.util.HexFormat;
import java.util.function.Supplier;

/**
 * V1版本 - License解析器
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LicenseV1Parser implements LicenseParser {

    private static final String[] SUPPORTED_FORMATS = {
        LicenseConstants.Format.VERSION_1_0
    };

    // AES-256-GCM加密算法
    private static final String AES_ALGORITHM = "AES";
    private static final String AES_TRANSFORMATION = "AES/GCM/NoPadding";
    private static final int GCM_IV_LENGTH = 12;
    private static final int GCM_TAG_LENGTH = 16;

    // HMAC-SHA256完整性校验
    private static final String HASH_ALGORITHM = "SHA-256";


    public License parse(String licenseContent) {
        try {
            log.debug("开始解析License内容: {}", licenseContent.substring(0, Math.min(100, licenseContent.length())));

            if(StrUtil.isBlankIfStr(licenseContent)) {
                throw new LicenseException.LicenseValidationException("License内容为空");
            }

            // 解析标准License内容
            return parseLicenseContent(licenseContent);

        } catch (Exception e) {
            throw new LicenseException.LicenseParseException("License解析失败，请确认", e);
        }
    }


    /**
     * 解析License文件
     * @param licenseFile 上传的License文件
     * @return 解析后的License对象
     */
    public License parse(File licenseFile) {
        try (FileInputStream fis = new FileInputStream(licenseFile);
             BufferedInputStream bis = new BufferedInputStream(fis)) {
            return parse(bis);
        } catch (IOException e) {
            log.error("读取License文件失败: {}", licenseFile.getAbsolutePath(), e);
            throw new LicenseException.LicenseParseException("文件读取失败", e);
        }
    }


    public License parse(InputStream inputStream) {
        String content = IoUtil.readUtf8(inputStream);
        return parse(content);
    }


    public boolean supports(String format) {
        return Arrays.asList(SUPPORTED_FORMATS).contains(format);
    }


    public String[] getSupportedFormats() {
        return SUPPORTED_FORMATS.clone();
    }


    public void isValidFormat(License license) {
        boolean flag = license.isValidFormat();
        if(!flag) {
            throw new LicenseException.LicenseFormatException("License内容格式有误，请确认");
        }
    }

    /**
     * 解析License内容
     */
    private License parseLicenseContent(String licenseContent) {
        try {
            // rsa解密
            String rsaContent = SM2Utils.decrypt(licenseContent, SM2Utils.getPrivateKeyBase64(getKeyInputStream));
            // 内容解析
            String decryptedContent = decryptLicenseContent(rsaContent);
            // 解析header
            License license = JSONUtil.toBean(decryptedContent, License.class);
            // 校验license内容完整性
            isValidFormat(license);
            // 校验签名
            checkContentSignature(license);
            // 校验机器码
            checkHardwareFingerprint(license);
            // 校验license有效期
            checkLicenseTime(license);

            return license;

        } catch (Exception e) {
            log.error("License内容解析失败", e);
            throw new LicenseException.LicenseParseException("内容解析失败", e);
        }
    }


    /**
     * 解密License内容
     *
     * @param encryptedContent 加密结果对象
     * @return 解密后的原始内容
     */
    private String decryptLicenseContent(String encryptedContent) {
        try {
            // 解码Base64字符串
            byte[] encryptedData = Base64.getDecoder().decode(encryptedContent);

            // 从加密数据中提取各部分
            int keyLength = 32; // AES-256密钥长度是32字节

            // 检查数据长度是否足够
            if (encryptedData.length < keyLength + GCM_IV_LENGTH) {
                throw new IllegalArgumentException("无效的加密数据");
            }

            // 提取密钥
            byte[] keyBytes = new byte[keyLength];
            System.arraycopy(encryptedData, 0, keyBytes, 0, keyLength);
            SecretKey secretKey = new SecretKeySpec(keyBytes, AES_ALGORITHM);

            // 提取IV
            byte[] iv = new byte[GCM_IV_LENGTH];
            System.arraycopy(encryptedData, keyLength, iv, 0, GCM_IV_LENGTH);

            // 提取实际的加密数据
            int encryptedContentLength = encryptedData.length - keyLength - GCM_IV_LENGTH;
            byte[] contentBytes = new byte[encryptedContentLength];
            System.arraycopy(encryptedData, keyLength + GCM_IV_LENGTH, contentBytes, 0, encryptedContentLength);

            // 执行AES-GCM解密
            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);
            GCMParameterSpec gcmSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, gcmSpec);

            byte[] decryptedData = cipher.doFinal(contentBytes);

            return new String(decryptedData, StandardCharsets.UTF_8);

        } catch (Exception e) {
            log.error("License内容解密失败", e);
            throw new RuntimeException("License内容解密失败: " + e.getMessage(), e);
        }
    }



    /**
     * 生成内容签名
     *
     * @param license License载荷
     * @return 内容签名
     */
    private void checkContentSignature(License license) {
        try {
            String payloadJson = JSONUtil.toJsonStr(license.getCdata().getPayload());
            String headerJson = JSONUtil.toJsonStr(license.getCdata().getHeader());
            MessageDigest digest = MessageDigest.getInstance(HASH_ALGORITHM);

            //
            String contentToSign = headerJson + "|" + license.getCdata().getFinger() + "|" + payloadJson + "|" + license.getCdata().getFinger();
            log.debug("[license file 校验] - 待签名内容: {}", contentToSign);
            byte[] hash = digest.digest(contentToSign.getBytes(StandardCharsets.UTF_8));
            String formatHex = org.apache.commons.codec.binary.Hex.encodeHexString(hash);
            if(formatHex.equals(license.getCdata().getSignature())) {
                return ;
            }

            throw new LicenseException.LicenseSignatureInvalidException("License内容校验失败，请确认");

        } catch (Exception e) {
            throw new RuntimeException("[license file 校验] - 生成内容签名失败", e);
        }
    }


    /**
     *
     * 校验机器码
     * @param license
     */
    private void checkHardwareFingerprint(License license) {
        String hardwareHash = license.getCdata().getPayload().getRestrictions().getHardwareHash();
        // 获取这条服务器的机器码
        String currentMachineCode = HardwareUtil.generateMachineCode();

        if(!hardwareHash.equalsIgnoreCase(currentMachineCode)) {
            throw new LicenseException.LicenseValidationException("License硬件校验失败，请确认");
        }
    }

    private void checkLicenseTime(License license) {
        String end = license.getCdata().getPayload().getValidity().getEnd();
        // 判断时间是否小于当前时间
        if(DateUtil.parse(end).isBefore(DateUtil.now())) {
            throw new LicenseException.LicenseValidationException("License已过期，请确认");
        }
    }



    /**
     * 获取resource下的private_key.pem的内容
     * 私钥加密
     * @return
     */
    public Supplier<InputStream> getKeyInputStream = () -> this.getClass().getResourceAsStream("/certs/private_key.pem");


}
