package com.ruijie.nse.mgr.license.pojo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpStatus;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * License验证结果
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LicenseValidationResult {

    /**
     * 验证是否通过
     */
    private Boolean valid;

    /**
     * License状态
     */
    private HttpStatus status;

    /**
     * 验证时间
     */
    private Date validationTime;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 验证详情列表
     */
    @Builder.Default
    private List<ValidationDetail> details = new ArrayList<>();

    /**
     * 扩展信息
     */
    private Object extensions;

    /**
     * 创建成功的验证结果
     */
    public static LicenseValidationResult success() {
        return LicenseValidationResult.builder()
                .valid(true)
                .status(HttpStatus.OK)
                .validationTime(new Date())
                .build();
    }

    /**
     * 创建失败的验证结果
     */
    public static LicenseValidationResult failure(HttpStatus status, String errorMessage) {
        return LicenseValidationResult.builder()
                .valid(false)
                .status(status)
                .errorMessage(errorMessage)
                .validationTime(new Date())
                .build();
    }

    /**
     * 添加验证详情
     */
    public LicenseValidationResult addDetail(String type, boolean passed, String message) {
        if (details == null) {
            details = new ArrayList<>();
        }
        details.add(ValidationDetail.builder()
                .type(type)
                .passed(passed)
                .message(message)
                .build());
        return this;
    }

    /**
     * 验证详情
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationDetail {

        /**
         * 验证类型
         */
        private String type;

        /**
         * 是否通过
         */
        private Boolean passed;

        /**
         * 详细信息
         */
        private String message;

        /**
         * 验证时间
         */
        @Builder.Default
        private Date timestamp = new Date();
    }

    /**
     * 验证类型常量
     */
    public static class ValidationType {
        public static final String SIGNATURE = "SIGNATURE";
        public static final String TIME_VALIDITY = "TIME_VALIDITY";
        public static final String HARDWARE_FINGERPRINT = "HARDWARE_FINGERPRINT";
        public static final String USER_LIMIT = "USER_LIMIT";
        public static final String MODULE_AUTHORIZATION = "MODULE_AUTHORIZATION";
        public static final String IP_RESTRICTION = "IP_RESTRICTION";
        public static final String MAC_RESTRICTION = "MAC_RESTRICTION";
    }
}
