package com.ruijie.nse.mgr.license.pojo.output;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 授权信息
 */
@Data
public class LicenseActivationInfoOutput {

    /**
     * 授权产品信息
     */
    private String productInfo;
    /**
     * 授权码
     */
    private String activationCode;
    /**
     * 注销码
     */
    private String cancelCode;
    /**
     * 终端许可数量（管理员）
     */
    private Integer permitMgrCnt;
    /**
     * 终端许可数量（普通用户）
     */
    private Integer permitUserCnt;
    /**
     * 有效期类型，永久、1年、2年、3年
     */
    private String validType;
    /**
     * 激活时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime activationTime;
    /**
     * 注销时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cancelTime;
    /**
     * 授权状态，已授权，已过期，已注销
     */
    private String status;

}
