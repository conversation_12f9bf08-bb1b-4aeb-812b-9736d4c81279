package com.ruijie.nse.mgr.license.pojo.output;

import lombok.Data;

@Data
public class LicenseImportOutput {

    private Integer permitMgrCnt;
    private Integer permitUserCnt;


    private LicenseImportOutput() {

    }

    private LicenseImportOutput(Integer permitMgrCnt, Integer permitUserCnt) {
        this.permitMgrCnt = permitMgrCnt;
        this.permitUserCnt = permitUserCnt;
    }

    public static LicenseImportOutput of(Integer permitMgrCnt, Integer permitUserCnt) {
        return new LicenseImportOutput(permitMgrCnt, permitUserCnt);
    }

}
