package com.ruijie.nse.mgr.license.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruijie.nse.mgr.common.context.LicenseContext;
import com.ruijie.nse.mgr.common.dto.EventMessage;
import com.ruijie.nse.mgr.license.constants.LicenseConstants;
import com.ruijie.nse.mgr.repository.entity.EventVerifyRecords;
import com.ruijie.nse.mgr.repository.entity.LicenseActivationInfo;
import com.ruijie.nse.mgr.repository.entity.enums.EventTypeEnum;
import com.ruijie.nse.mgr.repository.mapper.EventVerifyRecordsDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.collection.CollUtil;
import org.dromara.hutool.core.util.EnumUtil;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 事件验证记录服务
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EventVerifyRecordsService extends ServiceImpl<EventVerifyRecordsDao, EventVerifyRecords> {


    private final LicenseActivationInfoService licenseActivationInfoService;

    /**
     * 保存事件消息到数据库
     *
     * @param eventMessage 事件消息
     */
    public void saveEventMessage(EventMessage eventMessage) {
        EventVerifyRecords record = new EventVerifyRecords();
        record.setEvt(eventMessage.getEventType());
        record.setEvtDetails(eventMessage.getEventDetails());
        record.setEvtLevel(eventMessage.getEventLevel());
        record.setEvtMessage(eventMessage.getEventMessage());
        record.setRemark(eventMessage.getRemark());
        record.setCreatedDate(LocalDateTime.now());
        record.setModifiedDate(LocalDateTime.now());

        // 如果同一个事件，连续推送超过5次，就不重复存储了。
        List<EventVerifyRecords> verifyRecords = this.lambdaQuery()
                .orderByDesc(EventVerifyRecords::getCreatedDate)
                .last("LIMIT 5").list();
        if(CollUtil.isNotEmpty(verifyRecords) && verifyRecords.stream().allMatch(
                r -> (
                        r.getEvt().name().equalsIgnoreCase(eventMessage.getEventType().name())
                        && r.getEvtMessage().equalsIgnoreCase(eventMessage.getEventMessage())
           ))) {
            log.warn("事件 - {} 已经推送超过5次，不再重复存储", eventMessage.getEventType());
        } else {
            if (save(record)) {
                log.info("事件消息已保存到数据库: {}", record);
            } else {
                log.error("事件消息保存失败: {}", eventMessage);
            }
        }

        /**
         * 如果是license校验错误，刷新license状态
         */

        LambdaUpdateWrapper<LicenseActivationInfo> updateWrapper = Wrappers.lambdaUpdate(LicenseActivationInfo.class)
                .set(LicenseActivationInfo::getRemark, eventMessage.getEventMessage())
                .set(LicenseActivationInfo::getModifiedDate, LocalDateTime.now())
                .eq(LicenseActivationInfo::getStatus, LicenseConstants.Status.ACTIVE);

        if(EnumUtil.equals(eventMessage.getEventType(), EventTypeEnum.EVT_LIC_VERIFY_FAILED.name())
            || EnumUtil.equals(eventMessage.getEventType(), EventTypeEnum.EVT_LIC_FILE_ERROR.name())
        ) {

            LicenseContext.setValid(false);
            licenseActivationInfoService.update(
                    updateWrapper.set(LicenseActivationInfo::getStatus, LicenseConstants.Status.VERIFY_FAILED)
            );
            return ;

        }

        // lic过期
        if(EnumUtil.equals(eventMessage.getEventType(), EventTypeEnum.EVT_LIC_EXPIRED.name())) {

            LicenseContext.setValid(false);

            licenseActivationInfoService.update(
                    updateWrapper.set(LicenseActivationInfo::getStatus, LicenseConstants.Status.EXPIRED)
            );
            return ;
        }

        // lic注销
        if(EnumUtil.equals(eventMessage.getEventType(), EventTypeEnum.EVT_LIC_CANCEL.name())) {

            LicenseContext.setValid(false);

            licenseActivationInfoService.update(
                    updateWrapper.set(LicenseActivationInfo::getStatus, LicenseConstants.Status.CANCELED)
            );
        }

    }
}