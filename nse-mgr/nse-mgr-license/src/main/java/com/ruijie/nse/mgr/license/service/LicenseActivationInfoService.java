package com.ruijie.nse.mgr.license.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruijie.nse.common.dto.PageInput;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.utils.bean.BeanCopierUtils;
import com.ruijie.nse.common.utils.oshi.HardwareUtil;
import com.ruijie.nse.mgr.license.constants.LicenseConstants;
import com.ruijie.nse.mgr.license.pojo.output.LicenseActivationInfoOutput;
import com.ruijie.nse.mgr.repository.entity.LicenseActivationInfo;
import com.ruijie.nse.mgr.repository.mapper.LicenseActivationInfoDao;
import lombok.RequiredArgsConstructor;
import org.dromara.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class LicenseActivationInfoService extends ServiceImpl<LicenseActivationInfoDao, LicenseActivationInfo> {

    /**
     * 该系统只允许存在一条激活信息
     * @param pageInput
     * @return
     */
    public PageOutput<LicenseActivationInfoOutput> findPage(PageInput pageInput) {
        Page<LicenseActivationInfo> pageParam = new Page<>(pageInput.getPageNumber(), pageInput.getPageSize());
        Page<LicenseActivationInfo> page = this.lambdaQuery().orderByDesc(LicenseActivationInfo::getCreatedDate)
                .page(pageParam);

        if(page == null || page.getRecords().isEmpty()) {
            return new PageOutput<>();
        }

        List<LicenseActivationInfoOutput> infoOutputs = BeanCopierUtils.copyListProperties(page.getRecords(), LicenseActivationInfoOutput::new);
        return new PageOutput<>(page.getTotal(), infoOutputs);
    }


    /**
     * 获取当前License状态
     * @return
     */
    public String getCurrentStatus() {
        // 获取最新一条激活信息
        LicenseActivationInfo currentLicense = getCurrentLicense();
        if(currentLicense == null) {
            return LicenseConstants.Status.INACTIVE;
        }

        // 这里确保生效的license只会有一条
        return currentLicense.getStatus();
    }


    public LicenseActivationInfo getCurrentLicense() {
        // 获取最新一条激活信息
        String machineCode = HardwareUtil.getMachineCode();
        List<LicenseActivationInfo> listed = this.lambdaQuery()
                .eq(LicenseActivationInfo::getMachineCode, machineCode)
                .gt(LicenseActivationInfo::getValidTo, LocalDateTime.now())
                .orderByDesc(LicenseActivationInfo::getActivationTime)
                .list();
        if(CollUtil.isEmpty(listed)) {
            return null;
        }

        // 这里确保生效的license只会有一条
        return listed.getFirst();
    }

}
