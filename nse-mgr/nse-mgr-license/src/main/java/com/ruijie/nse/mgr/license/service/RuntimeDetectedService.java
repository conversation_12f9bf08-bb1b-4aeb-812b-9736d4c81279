package com.ruijie.nse.mgr.license.service;

import com.ruijie.nse.mgr.common.service.EventPublishService;
import com.ruijie.nse.mgr.license.constants.LicenseConstants;
import com.ruijie.nse.mgr.repository.entity.enums.EventTypeEnum;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.lang.management.ManagementFactory;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 运行时安全服务
 * 提供反调试检测、内存保护、完整性监控等安全功能
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RuntimeDetectedService {

    // 安全状态缓存
    private final Map<String, SecurityStatus> securityStatusCache = new ConcurrentHashMap<>();
    
    // 安全事件计数器
    private final AtomicInteger securityEventCounter = new AtomicInteger(0);
    
    // 文件完整性基线
    private final Map<String, String> fileIntegrityBaseline = new ConcurrentHashMap<>();
    
    // 安全检查间隔（毫秒）
    private static final long SECURITY_CHECK_INTERVAL = 60000; // 1分钟

    private final EventPublishService eventPublishService;

    /**
     * 检测安全威胁
     * 
     * @return 安全状态
     */
    public SecurityStatus detectThreats() {
        SecurityStatus status = new SecurityStatus();
        
        try {
            // 检测调试环境
            status.setDebuggingDetected(detectDebugging());
            
            // 检测JVM参数异常
            status.setJvmTampered(detectJvmTampering());
            
            // 检测文件完整性
            status.setFileIntegrityViolated(detectFileIntegrityViolation());
            
            // 检测内存异常
            status.setMemoryAnomalyDetected(detectMemoryAnomaly());
            
            // 检测网络异常
            status.setNetworkAnomalyDetected(detectNetworkAnomaly());
            
            // 计算总体安全等级
            status.setSecurityLevel(calculateSecurityLevel(status));
            
            // 更新检测时间
            status.setLastCheckTime(System.currentTimeMillis());
            
            // 缓存安全状态
            securityStatusCache.put("current", status);
            
            // 记录安全事件
            if (status.hasThreats()) {
                recordSecurityEvent(SecurityEventType.THREAT_DETECTED, status.toString());
            }
            
            return status;
            
        } catch (Exception e) {
            log.error("安全威胁检测失败", e);
            status.setSecurityLevel(SecurityLevel.HIGH_RISK);
            return status;
        }
    }

    /**
     * 检测调试环境
     * 
     * @return 是否检测到调试
     */
    public boolean detectDebugging() {
        try {
            // 检测JVM调试参数
            List<String> jvmArgs = ManagementFactory.getRuntimeMXBean().getInputArguments();
            for (String arg : jvmArgs) {
                if (arg.contains("jdwp") || arg.contains("Xdebug") || arg.contains("agentlib")) {
                    log.warn("检测到JVM调试参数: {}", arg);
                    return true;
                }
            }
            
            // 检测调试端口
            if (isDebugPortOpen()) {
                log.warn("检测到调试端口开放");
                return true;
            }
            
            // 检测IDE环境
            if (isRunningInIDE()) {
                log.warn("检测到IDE运行环境");
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("调试检测失败", e);
            return false;
        }
    }

    /**
     * 检测JVM参数篡改
     * 
     * @return 是否检测到篡改
     */
    public boolean detectJvmTampering() {
        try {
            List<String> jvmArgs = ManagementFactory.getRuntimeMXBean().getInputArguments();
            
            // 检测危险的JVM参数
            String[] dangerousArgs = {
                "-XX:+DisableExplicitGC",
                "-XX:+PrintGCDetails",
                "-Xverify:none",
                "-XX:-UseSplitVerifier"
            };
            
            for (String arg : jvmArgs) {
                for (String dangerous : dangerousArgs) {
                    if (arg.contains(dangerous)) {
                        log.warn("检测到危险JVM参数: {}", arg);
                        return true;
                    }
                }
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("JVM参数检测失败", e);
            return false;
        }
    }

    /**
     * 检测文件完整性违规
     * 
     * @return 是否检测到违规
     */
    public boolean detectFileIntegrityViolation() {
        try {
            // 检查关键文件的完整性
            String[] criticalFiles = {
                "application.yml",
                "application.properties",
                LicenseConstants.LicenseExt.LICF_PATH.toString()
            };
            
            for (String fileName : criticalFiles) {
                if (isFileModified(fileName)) {
                    log.warn("检测到关键文件被修改: {}", fileName);
                    return true;
                }
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("文件完整性检测失败", e);
            return false;
        }
    }

    /**
     * 检测内存异常
     * 
     * @return 是否检测到异常
     */
    public boolean detectMemoryAnomaly() {
        try {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            // 检测内存使用异常
            double memoryUsageRatio = (double) usedMemory / totalMemory;
            if (memoryUsageRatio > 0.95) {
                log.warn("检测到内存使用异常: {}%", memoryUsageRatio * 100);
                return true;
            }
            
            // 检测GC异常
            if (isGcAnomalyDetected()) {
                log.warn("检测到GC异常");
                return true;
            }
            
            return false;
            
        } catch (Exception e) {
            log.error("内存异常检测失败", e);
            return false;
        }
    }

    /**
     * 检测网络异常
     * 
     * @return 是否检测到异常
     */
    public boolean detectNetworkAnomaly() {
        try {
            // 检测异常的网络连接
            // 这里可以添加具体的网络监控逻辑
            
            return false;
            
        } catch (Exception e) {
            log.error("网络异常检测失败", e);
            return false;
        }
    }

    /**
     * 开始完整性监控
     */
    public void startIntegrityMonitoring() {
        try {
            // 建立文件完整性基线
            establishFileIntegrityBaseline();
            log.info("完整性监控已启动");
        } catch (Exception e) {
            log.error("完整性监控启动失败", e);
        }
    }

    /**
     * 定时安全检查
     */
//    @Scheduled(fixedRate = SECURITY_CHECK_INTERVAL)
    public void scheduledSecurityCheck() {
        SecurityStatus status = detectThreats();

        if (status.hasThreats()) {
            log.warn("定时安全检查发现威胁: {}", status);

            // 触发安全响应
            handleSecurityThreat(status);
        }
    }

    /**
     * 记录安全事件
     * 
     * @param eventType 事件类型
     * @param details 事件详情
     */
    public void recordSecurityEvent(SecurityEventType eventType, String details) {
        int eventId = securityEventCounter.incrementAndGet();
        // 记录到日志
        log.warn("安全事件 #{}: {} - {}", eventId, eventType, details);
        eventPublishService.publishEvent(EventTypeEnum.EVT_THREAT_DETECTED,  "安全事件检查", details);
    }

    /**
     * 处理安全威胁
     * 
     * @param status 安全状态
     */
    private void handleSecurityThreat(SecurityStatus status) {
        if (status.getSecurityLevel() == SecurityLevel.HIGH_RISK) {
            // 高风险处理
            log.error("检测到高风险安全威胁，建议立即处理");
            
            // 可以添加自动响应机制，如禁用某些功能等
        } else if (status.getSecurityLevel() == SecurityLevel.MEDIUM_RISK) {
            // 中等风险处理
            log.warn("检测到中等风险安全威胁，建议关注");
        }
    }

    /**
     * 检测调试端口是否开放
     */
    private boolean isDebugPortOpen() {
        // 检测常见的调试端口
        int[] debugPorts = {5005, 8000, 8080, 9999};
        
        for (int port : debugPorts) {
            try (java.net.Socket socket = new java.net.Socket()) {
                socket.connect(new java.net.InetSocketAddress("localhost", port), 1000);
                return true;
            } catch (Exception e) {
                // 端口未开放，继续检查下一个
            }
        }
        
        return false;
    }

    /**
     * 检测是否在IDE中运行
     */
    private boolean isRunningInIDE() {
        String classPath = System.getProperty("java.class.path");
        return classPath.contains("idea") || 
               classPath.contains("eclipse") || 
               classPath.contains("netbeans") ||
               classPath.contains("vscode");
    }

    /**
     * 检测文件是否被修改
     */
    private boolean isFileModified(String fileName) {
        try {
            Path filePath = Paths.get(fileName);
            if (!Files.exists(filePath)) {
                return false;
            }
            
            // 计算文件哈希
            byte[] fileBytes = Files.readAllBytes(filePath);
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(fileBytes);
            String currentHash = Base64.getEncoder().encodeToString(hashBytes);
            
            // 与基线比较
            String baselineHash = fileIntegrityBaseline.get(fileName);
            if (baselineHash == null) {
                // 首次检查，建立基线
                fileIntegrityBaseline.put(fileName, currentHash);
                return false;
            }
            
            return !baselineHash.equals(currentHash);
            
        } catch (Exception e) {
            log.error("文件完整性检查失败: {}", fileName, e);
            return false;
        }
    }

    /**
     * 检测GC异常
     */
    private boolean isGcAnomalyDetected() {
        // 可以通过JMX监控GC情况
        return false;
    }

    /**
     * 建立文件完整性基线
     */
    private void establishFileIntegrityBaseline() {
        String[] criticalFiles = {
            "application.yml",
            "application.properties", 
            "license.properties"
        };
        
        for (String fileName : criticalFiles) {
            isFileModified(fileName); // 这会建立基线
        }
    }

    /**
     * 计算安全等级
     */
    private SecurityLevel calculateSecurityLevel(SecurityStatus status) {
        int riskScore = 0;
        
        if (status.isDebuggingDetected()) riskScore += 3;
        if (status.isJvmTampered()) riskScore += 2;
        if (status.isFileIntegrityViolated()) riskScore += 3;
        if (status.isMemoryAnomalyDetected()) riskScore += 1;
        if (status.isNetworkAnomalyDetected()) riskScore += 1;
        
        if (riskScore >= 5) return SecurityLevel.HIGH_RISK;
        if (riskScore >= 2) return SecurityLevel.MEDIUM_RISK;
        return SecurityLevel.LOW_RISK;
    }

    /**
     * 确定事件严重程度
     */
    private SecuritySeverity determineSeverity(SecurityEventType eventType) {
        return switch (eventType) {
            case THREAT_DETECTED, TAMPER_DETECTED -> SecuritySeverity.HIGH;
            case DEBUG_DETECTED, INTEGRITY_VIOLATION -> SecuritySeverity.MEDIUM;
            default -> SecuritySeverity.LOW;
        };
    }

    // 枚举和数据类定义
    public enum SecurityLevel {
        LOW_RISK, MEDIUM_RISK, HIGH_RISK
    }

    public enum SecurityEventType {
        THREAT_DETECTED, TAMPER_DETECTED, DEBUG_DETECTED, 
        INTEGRITY_VIOLATION, UNAUTHORIZED_ACCESS
    }

    public enum SecuritySeverity {
        LOW, MEDIUM, HIGH
    }

    @Data
    public static class SecurityStatus {
        private boolean debuggingDetected;
        private boolean jvmTampered;
        private boolean fileIntegrityViolated;
        private boolean memoryAnomalyDetected;
        private boolean networkAnomalyDetected;
        private SecurityLevel securityLevel;
        private long lastCheckTime;
        
        public boolean hasThreats() {
            return debuggingDetected || jvmTampered || fileIntegrityViolated || 
                   memoryAnomalyDetected || networkAnomalyDetected;
        }
    }

    @Data
    @lombok.Builder
    public static class SecurityEvent {
        private int id;
        private SecurityEventType type;
        private String details;
        private long timestamp;
        private SecuritySeverity severity;
    }
}
