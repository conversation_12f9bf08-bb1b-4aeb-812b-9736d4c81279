package com.ruijie.nse.mgr.license.validator;

import com.ruijie.nse.common.utils.system.OSUtil;
import com.ruijie.nse.mgr.common.context.LicenseContext;
import com.ruijie.nse.mgr.common.dto.License;
import com.ruijie.nse.mgr.common.service.EventPublishService;
import com.ruijie.nse.mgr.common.utils.SM2Utils;
import com.ruijie.nse.mgr.license.constants.LicenseConstants;
import com.ruijie.nse.mgr.license.exception.LicenseException;
import com.ruijie.nse.mgr.license.pojo.LicenseValidationResult;
import com.ruijie.nse.mgr.license.validator.impl.HardwareFingerprintValidator;
import com.ruijie.nse.mgr.license.validator.impl.SignatureValidator;
import com.ruijie.nse.mgr.license.validator.impl.TimeValidityValidator;
import com.ruijie.nse.mgr.repository.entity.enums.EventTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.exception.ExceptionUtil;
import org.dromara.hutool.core.io.file.FileUtil;
import org.dromara.hutool.extra.management.oshi.OshiUtil;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.Date;
import java.util.function.Supplier;

/**
 * 组合License验证器 - 主要验证器实现
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CompositeLicenseValidator implements LicenseValidator {

    private final SignatureValidator signatureValidator;
    private final TimeValidityValidator timeValidityValidator;
    private final HardwareFingerprintValidator hardwareFingerprintValidator;
    private final EventPublishService eventPublishService;

    @Override
    public LicenseValidationResult validate(License license) {
        log.debug("开始验证License: {}", license.getActivationCode());

        LicenseValidationResult.LicenseValidationResultBuilder result = LicenseValidationResult.builder()
                .validationTime(new Date());

        try {
            // 先重新定位当前系统时间
            refreshSystemBootTime();

            // 1. 基础检查
            if (license.getCdata() == null) {
                eventPublishService.publishLicenseVerifyErrorEvent("License对象或内容为空", null);

                return result.valid(false).status(HttpStatus.UNAUTHORIZED)
                        .errorMessage("License对象或内容为空").build();
            }

            // 2. 签名验证
            boolean signatureValid = validateSignature(license);
            result.build().addDetail(LicenseValidationResult.ValidationType.SIGNATURE,
                    signatureValid,
                    signatureValid ? "签名验证通过" : "签名验证失败");

            if (!signatureValid) {
                eventPublishService.publishLicenseVerifyErrorEvent("License签名验证失败", null);
                return result.valid(false).status(HttpStatus.UNAUTHORIZED)
                        .errorMessage("License签名验证失败").build();
            }

            // 3. 时间有效性验证
            boolean timeValid = validateTimeValidity(license);
            result.build().addDetail(LicenseValidationResult.ValidationType.TIME_VALIDITY,
                           timeValid, 
                           timeValid ? "时间验证通过" : "License已过期");
            
            if (!timeValid) {
                eventPublishService.publishEvent(EventTypeEnum.EVT_LIC_EXPIRED, "lic授权已过期", null);
                return result.valid(false).status(HttpStatus.FORBIDDEN)
                        .errorMessage("License已过期")
                        .build();
            }

            // 4. 硬件指纹验证
            boolean hardwareValid = validateHardwareFingerprint(license);
            result.build().addDetail(LicenseValidationResult.ValidationType.HARDWARE_FINGERPRINT,
                    hardwareValid,
                    hardwareValid ? "硬件指纹验证通过" : "硬件指纹不匹配");

            if (!hardwareValid) {
                eventPublishService.publishLicenseVerifyErrorEvent("硬件指纹不匹配", null);
                return result.valid(false).status(HttpStatus.UNAUTHORIZED)
                        .errorMessage("硬件指纹不匹配")
                        .build();
            }

            // 验证通过
            log.debug("License验证通过: {}", license.getActivationCode());
            // 防止在内存中
            LicenseContext.setLicense(license);
            return result.valid(true).status(HttpStatus.OK)
                    .build();

        } catch (Exception e) {
            log.error("License验证过程中发生异常: {}", license.getActivationCode(), e);
            eventPublishService.publishLicenseVerifyErrorEvent("License验证异常", ExceptionUtil.getRootCauseMessage(e));
            return result.valid(false).status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .errorMessage("验证过程中发生异常: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public boolean isValid(License license) {
        return validate(license).getValid();
    }

    @Override
    public boolean validateSignature(License license) {
        return signatureValidator.valid(license);
    }

    @Override
    public boolean validateTimeValidity(License license) {
        return timeValidityValidator.valid(license);
    }

    @Override
    public boolean validateHardwareFingerprint(License license) {
        return hardwareFingerprintValidator.valid(license);
    }


    @Override
    public boolean validateIpRestriction(License license, String currentIp) {
        return true;
    }


    @Override
    public String getValidatorName() {
        return "CompositeLicenseValidator";
    }

    @Override
    public int getPriority() {
        return 1;
    }


    /**
     * 刷新当前时间
     * @throws IOException
     * @throws NumberFormatException
     */
    private void refreshSystemBootTime() throws Exception {
        // 如果是windows系统，直接返回
        if(OSUtil.isWindows()) {
            log.warn("Windows系统，暂不支持获取系统开机时间，忽略！");
            return ;
        }
        long uptimeSeconds = getUptimeSeconds();

        Path licdPath = Paths.get(LicenseConstants.LicenseExt.LICD_PATH.toString());
        if (!Files.exists(licdPath)) {
            throw new LicenseException.LicenseValidationException("license关键文件不存在，请确认");
        }
        String decryptRsa = FileUtil.readUtf8String(licdPath.toFile());
        String licdContent = SM2Utils.decrypt(decryptRsa);

        // 计算新的启动时间（licd时间 + 运行时间）
        long newBootTimeMillis = Long.parseLong(licdContent) + (uptimeSeconds * 1000);
        // 建议这里不用判断当前文件是否为空，如果判断为空后 又将最新时间写进去，那么很容易出错。
        // 所以交给程序部署的时候去处理吧，整个程序期间都不会去删除，如果真的该文件丢失了，那么大概率为人为事
        try (BufferedWriter writer = Files.newBufferedWriter(licdPath,
                StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING)) {
            writer.write(SM2Utils.encrypt(String.valueOf(newBootTimeMillis)));
        }
    }

    private static long getUptimeSeconds() throws IOException {
        try (BufferedReader uptimeReader = new BufferedReader(new FileReader(LicenseConstants.LicenseExt.SYS_UPTIME_PATH.toString()))) {
            String line = uptimeReader.readLine();
            if (line == null || line.trim().isEmpty()) {
                throw new LicenseException.LicenseValidationException(LicenseConstants.Message.LICENSE_EXPIRED_OR_NOT_FOUNT);
            }

            String[] parts = line.split("\\s+");
            if (parts.length == 0 || parts[0] == null || parts[0].trim().isEmpty()) {
                throw new LicenseException.LicenseValidationException(LicenseConstants.Message.LICENSE_EXPIRED_OR_NOT_FOUNT);
            }

            try {
                double uptimeSecondsDouble = Double.parseDouble(parts[0]);
                if (uptimeSecondsDouble < 0) {
                    throw new LicenseException.LicenseValidationException("系统运行时间异常（负数）");
                }
                return (long) uptimeSecondsDouble; // 截断小数部分
            } catch (NumberFormatException e) {
                throw new LicenseException.LicenseValidationException("无法解析系统运行时间: " + parts[0]);
            }
        }
    }


    /**
     * 获取resource下的public_key.pem的内容
     * 私钥加密
     * @return
     */
    public Supplier<InputStream> getKeyInputStream = () -> this.getClass().getResourceAsStream("/certs/private_key.pem");

}
