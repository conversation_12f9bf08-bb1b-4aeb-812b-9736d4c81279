package com.ruijie.nse.mgr.license.web.filter;

import com.ruijie.nse.mgr.license.constants.LicenseConstants;
import com.ruijie.nse.mgr.common.context.LicenseContext;
import com.ruijie.nse.mgr.license.service.LicenseActivationInfoService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.hutool.core.text.StrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

@Component
public class LicenseThreadLocalInterceptor implements HandlerInterceptor {

    @Autowired
    private LicenseActivationInfoService licenseActivationInfoService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String currentStatus = licenseActivationInfoService.getCurrentStatus();
        LicenseContext.setValid(StrUtil.isNotBlank(currentStatus) && currentStatus.equalsIgnoreCase(LicenseConstants.Status.ACTIVE));
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response,
                                Object handler, Exception ex) {
        LicenseContext.clear();
    }
}