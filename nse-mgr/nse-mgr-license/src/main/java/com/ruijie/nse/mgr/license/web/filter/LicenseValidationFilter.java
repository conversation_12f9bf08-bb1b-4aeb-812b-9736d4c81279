package com.ruijie.nse.mgr.license.web.filter;

import com.ruijie.nse.common.dto.R;
import com.ruijie.nse.mgr.license.annotation.VerifyLicense;
import com.ruijie.nse.mgr.license.pojo.LicenseValidationResult;
import com.ruijie.nse.mgr.license.service.LicenseV1Service;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.json.JSONUtil;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerExecutionChain;
import org.springframework.web.servlet.HandlerMapping;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * License验证过滤器
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Component
public class LicenseValidationFilter extends OncePerRequestFilter {

    private final LicenseV1Service licenseService;
    private final List<HandlerMapping> handlerMappings;

    public LicenseValidationFilter(LicenseV1Service licenseService,
                         ObjectProvider<List<HandlerMapping>> handlerMappingsProvider) {
        this.licenseService = licenseService;
        this.handlerMappings = handlerMappingsProvider.getIfAvailable(Collections::emptyList);
    }

    /**
     * 不需要License验证的路径
     */
    private static final List<String> EXCLUDED_PATHS = Arrays.asList(
        "/api",
        "/logout",
        "/error",
        "/static",
        "/css",
        "/js",
        "/img",
        "/fonts",
        "/favicon.ico"
    );

    @Override
    protected void doFilterInternal(HttpServletRequest request,
                                  HttpServletResponse response,
                                  FilterChain filterChain) throws ServletException, IOException {

        String requestPath = request.getRequestURI();
        String method = request.getMethod();

        log.debug("License验证过滤器处理请求: {} {}", method, requestPath);

        // 如果没有添加VerifyLicense注解，则不需要验证license
        if (!isVerifyLicense(request)) {
            log.debug("路径在排除列表中，跳过License验证: {}", requestPath);
            filterChain.doFilter(request, response);
            return;
        }

        try {
            // 执行License验证
//             LicenseValidationResult validationResult = licenseService.validateCurrentLicense();
//
//             if (validationResult == null || !validationResult.getValid()) {
//                 handleLicenseValidationFailure(request, response, validationResult);
//                 return;
//             }

            // 验证通过，继续处理请求
            log.debug("License验证通过，继续处理请求: {}", requestPath);
            filterChain.doFilter(request, response);

        } catch (Exception e) {
            log.error("License验证过程中发生异常: {}", requestPath, e);
            handleLicenseValidationError(request, response, e);
        }
    }

    /**
     * 检查路径是否在排除列表中
     *
     * @param requestPath 请求路径
     * @return 是否排除
     */
    private boolean isExcludedPath(String requestPath) {
        return EXCLUDED_PATHS.stream()
                .anyMatch(requestPath::startsWith);
    }

    /**
     * 处理License验证失败
     *
     * @param request HTTP请求
     * @param response HTTP响应
     * @param validationResult 验证结果
     */
    private void handleLicenseValidationFailure(HttpServletRequest request,
                                              HttpServletResponse response,
                                              LicenseValidationResult validationResult) throws IOException {

        String errorMessage = "License验证失败";
        HttpStatus errorCode = HttpStatus.INTERNAL_SERVER_ERROR;

        if (validationResult != null) {
            errorMessage = validationResult.getErrorMessage() != null ?
                          validationResult.getErrorMessage() : errorMessage;

            if (validationResult.getStatus() != null) {
                switch (validationResult.getStatus()) {
                    case FORBIDDEN:
                        errorCode = HttpStatus.FORBIDDEN;
                        errorMessage = "License已过期，请联系管理员更新License";
                        break;
                    case UNAUTHORIZED:
                        errorCode = HttpStatus.UNAUTHORIZED;
                        errorMessage = "License签名验证失败，请检查License文件完整性";
                        break;
                    case BANDWIDTH_LIMIT_EXCEEDED:
                        errorCode = HttpStatus.BANDWIDTH_LIMIT_EXCEEDED;
                        errorMessage = "用户数已达到License限制，无法继续使用";
                        break;
                    default:
                        break;
                }
            }
        }

        log.warn("License验证失败: {} - {} - {}", request.getRequestURI(), errorCode, errorMessage);

        // 构建错误响应
        R<Object> errorResponse = R.error(errorCode.value(), errorMessage);

        response.setStatus(HttpStatus.FORBIDDEN.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.getWriter().write(JSONUtil.toJsonStr(errorResponse));
    }

    /**
     * 处理License验证异常
     *
     * @param request HTTP请求
     * @param response HTTP响应
     * @param exception 异常
     */
    private void handleLicenseValidationError(HttpServletRequest request,
                                            HttpServletResponse response,
                                            Exception exception) throws IOException {

        log.error("License验证异常: {} - {}", request.getRequestURI(), exception.getMessage());

        R<Object> errorResponse = R.error(
            HttpStatus.INTERNAL_SERVER_ERROR.value(),
            "License验证服务异常，请联系管理员"
        );

        response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.getWriter().write(JSONUtil.toJsonStr(errorResponse));
    }

    private boolean isVerifyLicense(HttpServletRequest request) {
        try {
            for (HandlerMapping mapping : handlerMappings) {
                HandlerExecutionChain chain = mapping.getHandler(request);
                if (chain != null) {
                    Object handler = chain.getHandler();
                    if (handler instanceof HandlerMethod hm) {
                        return hm.hasMethodAnnotation(VerifyLicense.class) || hm.getBeanType().isAnnotationPresent(VerifyLicense.class);
                    }
                    return false;
                }
            }
        } catch (Exception ignored) {
            // ignored
        }
        return false;
    }
}
