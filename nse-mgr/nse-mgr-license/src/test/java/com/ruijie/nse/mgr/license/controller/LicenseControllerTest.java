//package com.ruijie.nse.mgr.license.controller;
//
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.ruijie.nse.mgr.license.dto.LicenseDto;
//import com.ruijie.nse.mgr.license.pojo.LicenseValidationResult;
//import com.ruijie.nse.mgr.license.service.LicenseV1Service;
//import com.ruijie.nse.mgr.license.vo.LicenseVo;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
//import org.springframework.boot.test.mock.mockito.MockBean;
//import org.springframework.http.MediaType;
//import org.springframework.test.web.servlet.MockMvc;
//
//import java.util.Date;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.anyString;
//import static org.mockito.Mockito.when;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
//
///**
// * License控制器测试类
// *
// * <AUTHOR>
// * @since 2025-07-25
// */
//@WebMvcTest(LicenseV1Controller.class)
//class LicenseControllerTest {
//
//    @Autowired
//    private MockMvc mockMvc;
//
//    @MockBean
//    private LicenseV1Service licenseService;
//
//    @Autowired
//    private ObjectMapper objectMapper;
//
//    private LicenseVo.StatusStatistics mockStatusStatistics;
//    private LicenseVo.DetailInfo mockDetailInfo;
//    private LicenseValidationResult mockValidationResult;
//
//    @BeforeEach
//    void setUp() {
//        // 创建模拟状态统计
//        mockStatusStatistics = LicenseVo.StatusStatistics.builder()
//                .hasLicense(true)
//                .isValid(true)
//                .status("VALID")
//                .remainingDays(365L)
//                .totalUsers(50)
//                .totalManagers(5)
//                .maxUsers(100)
//                .maxManagers(10)
//                .lastValidationTime(new Date())
//                .build();
//
//        // 创建模拟详细信息
//        mockDetailInfo = LicenseVo.DetailInfo.builder()
//                .licenseId("TEST-LICENSE-001")
//                .productName("NSE Management System")
//                .version("1.0.0")
//                .issuer("Test Issuer")
//                .issuedAt(new Date())
//                .build();
//
//        // 创建模拟验证结果
//        mockValidationResult = LicenseValidationResult.builder()
//                .valid(true)
//                .status("VALID")
//                .validationTime(new Date())
//                .build();
//    }
//
//    @Test
//    void testGetLicenseStatus_Success() throws Exception {
//        // Given
//        when(licenseService.getLicenseStatistics()).thenReturn(mockStatusStatistics);
//
//        // When & Then
//        mockMvc.perform(get("/api/license/status"))
//                .andExpect(status().isOk())
//                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.data.hasLicense").value(true))
//                .andExpect(jsonPath("$.data.isValid").value(true))
//                .andExpect(jsonPath("$.data.status").value("VALID"))
//                .andExpect(jsonPath("$.data.remainingDays").value(365))
//                .andExpect(jsonPath("$.data.totalUsers").value(50))
//                .andExpect(jsonPath("$.data.totalManagers").value(5));
//    }
//
//    @Test
//    void testGetLicenseInfo_Success() throws Exception {
//        // Given
//        when(licenseService.getCurrentLicenseInfo()).thenReturn(mockDetailInfo);
//
//        // When & Then
//        mockMvc.perform(get("/api/license/info"))
//                .andExpect(status().isOk())
//                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.data.licenseId").value("TEST-LICENSE-001"))
//                .andExpect(jsonPath("$.data.productName").value("NSE Management System"))
//                .andExpect(jsonPath("$.data.version").value("1.0.0"))
//                .andExpect(jsonPath("$.data.issuer").value("Test Issuer"));
//    }
//
//    @Test
//    void testGetLicenseInfo_NoLicense() throws Exception {
//        // Given
//        when(licenseService.getCurrentLicenseInfo()).thenReturn(null);
//
//        // When & Then
//        mockMvc.perform(get("/api/license/info"))
//                .andExpect(status().isOk())
//                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.code").value(500))
//                .andExpect(jsonPath("$.msg").value("未找到有效的License"));
//    }
//
//    @Test
//    void testValidateLicense_Success() throws Exception {
//        // Given
//        when(licenseService.validateCurrentLicense()).thenReturn(mockValidationResult);
//
//        // When & Then
//        mockMvc.perform(post("/api/license/validate"))
//                .andExpect(status().isOk())
//                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.data.valid").value(true))
//                .andExpect(jsonPath("$.data.status").value("VALID"));
//    }
//
//    @Test
//    void testValidateLicenseContent_Success() throws Exception {
//        // Given
//        LicenseDto.ValidateRequest request = new LicenseDto.ValidateRequest();
//        request.setLicenseContent("test-license-content");
//
//        when(licenseService.validateLicenseContent(anyString())).thenReturn(mockValidationResult);
//
//        // When & Then
//        mockMvc.perform(post("/api/license/validate-content")
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .content(objectMapper.writeValueAsString(request)))
//                .andExpect(status().isOk())
//                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.data.valid").value(true))
//                .andExpect(jsonPath("$.data.status").value("VALID"));
//    }
//
//    @Test
//    void testImportLicense_Success() throws Exception {
//        // Given
//        LicenseDto.ImportRequest request = new LicenseDto.ImportRequest();
//        request.setLicenseContent("test-license-content");
//        request.setFormat("json");
//        request.setImportBy("admin");
//
//        when(licenseService.importLicense(any(LicenseDto.ImportRequest.class))).thenReturn(true);
//
//        // When & Then
//        mockMvc.perform(post("/api/license/import")
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .content(objectMapper.writeValueAsString(request)))
//                .andExpect(status().isOk())
//                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.data").value(true))
//                .andExpect(jsonPath("$.msg").value("License导入成功"));
//    }
//
//    @Test
//    void testImportLicense_Failure() throws Exception {
//        // Given
//        LicenseDto.ImportRequest request = new LicenseDto.ImportRequest();
//        request.setLicenseContent("invalid-license-content");
//        request.setFormat("json");
//        request.setImportBy("admin");
//
//        when(licenseService.importLicense(any(LicenseDto.ImportRequest.class))).thenReturn(false);
//
//        // When & Then
//        mockMvc.perform(post("/api/license/import")
//                        .contentType(MediaType.APPLICATION_JSON)
//                        .content(objectMapper.writeValueAsString(request)))
//                .andExpect(status().isOk())
//                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.code").value(500))
//                .andExpect(jsonPath("$.msg").value("License导入失败"));
//    }
//
//    @Test
//    void testGetLicenseConfig_Success() throws Exception {
//        // Given
//        LicenseVo.ConfigInfo configInfo = LicenseVo.ConfigInfo.builder()
//                .enabled(true)
//                .enableHardwareValidation(true)
//                .enableIpRestriction(false)
//                .enableMacRestriction(false)
//                .cacheEnabled(true)
//                .cacheTtl(1800)
//                .validationInterval(300)
//                .warningDays(30)
//                .build();
//
//        when(licenseService.getLicenseConfig()).thenReturn(configInfo);
//
//        // When & Then
//        mockMvc.perform(get("/api/license/config"))
//                .andExpect(status().isOk())
//                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.data.enabled").value(true))
//                .andExpect(jsonPath("$.data.enableHardwareValidation").value(true))
//                .andExpect(jsonPath("$.data.cacheEnabled").value(true))
//                .andExpect(jsonPath("$.data.cacheTtl").value(1800));
//    }
//
//    @Test
//    void testRefreshLicenseCache_Success() throws Exception {
//        // When & Then
//        mockMvc.perform(post("/api/license/refresh-cache"))
//                .andExpect(status().isOk())
//                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.data").value("License缓存刷新成功"));
//    }
//
//    @Test
//    void testGetHardwareFingerprint_Success() throws Exception {
//        // Given
//        when(licenseService.getHardwareFingerprint()).thenReturn("test-hardware-fingerprint");
//
//        // When & Then
//        mockMvc.perform(get("/api/license/hardware-fingerprint"))
//                .andExpect(status().isOk())
//                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.data").value("test-hardware-fingerprint"));
//    }
//
//    @Test
//    void testGenerateLicenseApplication_Success() throws Exception {
//        // Given
//        LicenseVo.ApplicationInfo applicationInfo = LicenseVo.ApplicationInfo.builder()
//                .hardwareFingerprint("test-hardware-fingerprint")
//                .applicationTime("2025-07-25 10:00:00")
//                .systemInfo("OS: Windows 10, Arch: x64, Java: 21.0.6")
//                .build();
//
//        when(licenseService.generateLicenseApplication()).thenReturn(applicationInfo);
//
//        // When & Then
//        mockMvc.perform(get("/api/license/application-info"))
//                .andExpect(status().isOk())
//                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.data.hardwareFingerprint").value("test-hardware-fingerprint"))
//                .andExpect(jsonPath("$.data.applicationTime").value("2025-07-25 10:00:00"));
//    }
//
//    @Test
//    void testGetUserStatistics_Success() throws Exception {
//        // Given
//        LicenseVo.UserStatistics userStats = LicenseVo.UserStatistics.builder()
//                .totalUsers(100)
//                .totalManagers(10)
//                .activeUsers(90)
//                .onlineUsers(50)
//                .build();
//
//        when(licenseService.getUserStatistics()).thenReturn(userStats);
//
//        // When & Then
//        mockMvc.perform(get("/api/license/user-statistics"))
//                .andExpect(status().isOk())
//                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.data.totalUsers").value(100))
//                .andExpect(jsonPath("$.data.totalManagers").value(10))
//                .andExpect(jsonPath("$.data.activeUsers").value(90))
//                .andExpect(jsonPath("$.data.onlineUsers").value(50));
//    }
//
//    @Test
//    void testIsLicenseExpiringSoon_Success() throws Exception {
//        // Given
//        when(licenseService.isLicenseExpiringSoon(30)).thenReturn(false);
//
//        // When & Then
//        mockMvc.perform(get("/api/license/expiring-soon")
//                        .param("warningDays", "30"))
//                .andExpect(status().isOk())
//                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.data").value(false));
//    }
//
//    @Test
//    void testGetLicenseRemainingDays_Success() throws Exception {
//        // Given
//        when(licenseService.getLicenseRemainingDays()).thenReturn(365L);
//
//        // When & Then
//        mockMvc.perform(get("/api/license/remaining-days"))
//                .andExpect(status().isOk())
//                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.code").value(200))
//                .andExpect(jsonPath("$.data").value(365));
//    }
//}
