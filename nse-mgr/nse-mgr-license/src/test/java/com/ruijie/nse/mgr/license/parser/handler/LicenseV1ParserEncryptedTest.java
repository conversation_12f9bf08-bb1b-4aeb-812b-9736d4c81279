package com.ruijie.nse.mgr.license.parser.handler;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * LicenseV1Parser加密License解析测试
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@ExtendWith(MockitoExtension.class)
class LicenseV1ParserEncryptedTest {

    @InjectMocks
    private LicenseV1Parser licenseV1Parser;

    @Test
    void testParseEmptyContent() {
        // 测试解析空内容
        String emptyJson = "BO1YzwTy12m+Pm6o7oK1zpTWPk/6qCF1IeVM5w9kqhJdyuG79KSg60vVQCRVdcMv5DGWVihwwLzz2dWMck2vD5NHx3D6Hcn8hUUuLNKol3QVihb93DTVK4bX/nCFSYMEPtr8Ax4qzAgKxILcKtfoFYpIl0w5irH50SMPNosdsLBWyJxy1dVuwSRIinyfQw0t8l4fGmXbU3ID7z6SPSeGGUEYZptK6mlDQmHmRozevTJpu63hD/kGdR6lwjWShkuXWoz5kSN0qUcRkwFS+zfH5veDwWz59jf3zkwSTscmr8GEueqHVAr/OUU3ZhlnYUkeR6TRyjKO3Fx+fIfPuslWF5+ukx2hA9BQKkSfw1hY9yDXLWgekIANsErx9G6td5ei7MuEQj0Vpqi9hFYcJ0fClvrsIqyuc+pKkT4W4F81Cmj9zdDLzrNQr9IpZxoF954G/7GeHTVtl7EVJ923Uqp0E2kiavxGGX9QhIF3wFS2UhMYSmOWaYj3SUn935YZZQEGsy+0OPt8Mli8VcX9ZlrmpTZwEEW9O+fTco1NLZ2tvvvp9h3su25/+CJXyhLVHo93wuoAYbs3ffg6WIXJjsYPkSpHsdDQY/T5Zjtv7PIw/XS9DyDegAq46g8f3jMhbfkbVx7sE5CiuJCcUymKASmp8ZymPjw2xn20iXBjdJSMcqA6dxPwrMapkE92pl4lbS+IyNNwD4qu5gdy6KdQqG5bpYMNnGq4Af/0oRIa+lG8+NAiMUbNXY1k9mp7cpmNMo9PEGEK9cqpc0W6hTe1igWxir41ojjUG5ozkQola/xamKyM7NTWc2o9yOGwUUcClUF7gGKoYFWlpPNFpBIo42E8Sc3RJrSltb26PrFn3wfDmZjhzdlprMOq5YWkM6Xee1sCwdapEBd5e7Mp+iuY+dMpAzurzc/IHqRTL3GGBkPcXgzgBkEFGtTTt113tv6eqMALLVUhy8XgGjcPjj9IOuF9GV4/SuSxvRjvBo5qdJODUMM949L+e9nk5W3mOhgWemq3fg3cvMKUdFJX3OhtRBAjvj6CJad0T22rEf0YA3Ms7XumSgAS0mpbry43jS5jPNiTJbbdF7Pwb6S7vPvVC6rSUX4GspHFjt5VoTP0DYzq7j3mM31i9jp0KBg/jI8dBxO9SLXyjMDdBTuzO/jlrgnLkSjes0JIWQiV53s2S103Az/oIi8qKrDJYjnYQ62Ulb943n+J6EmEglCCM3zG8f0gn8dyHSHZNwa6GHSbRFhnPQKDNpw5TvEKTAHI/4vea4VEnN5+E9EhloDMknqIu8vPG2vLL32aID+XuSEiWwT+kjVPUDnj+EwXXpWoouHrcF/UjnSpjPc8xxH4DfDObb+4GJuXYyEODze5512UBQ6qT1SesQZAc9X/xyyvdXhKo9pqutQHUKdLOTI3mkXKCmjBQ0zDIdH3qeSw5/M5jvjRUPfokTVGnRVNNF1DF6vFPLZFgw==";

        assertThrows(Exception.class, () -> {
            licenseV1Parser.parse(emptyJson);
        });
    }
}
