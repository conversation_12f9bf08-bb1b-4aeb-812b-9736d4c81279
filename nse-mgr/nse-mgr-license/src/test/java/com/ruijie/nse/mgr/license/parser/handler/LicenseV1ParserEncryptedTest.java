package com.ruijie.nse.mgr.license.parser.handler;

import com.ruijie.nse.mgr.common.dto.License;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * LicenseV1Parser加密License解析测试
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@ExtendWith(MockitoExtension.class)
class LicenseV1ParserEncryptedTest {

    @InjectMocks
    private LicenseV1Parser licenseV1Parser;

    @Test
    void testParseEmptyContent() {
        // 测试解析空内容
        String emptyJson = "BAE2jxKPlAZ70b2otUzg0IShNJc3Nr/ZCiPa7YT6u6NBbp8bcWxNKrE7nhjx8W2un1YUzSUQauJeMt923j3GGUez5mIyMlQDUmpvxUpi5ygDpx0m0xjE2TCP3SldTvVXWHb8Sy1Kk+SQusD+5jOOP8o+rIH1Lhymo/MqNVnPtjse5lw0fU30KF+hxmQJy6dbRfbk+RTj1J2vvYkEtotrfypkWeTvuEHA9N6XWFz+/bWJ+USHpsKcgOGbZFlIw33PJtfvTF3DFXPOMssihAmvCTtnTsigeZFzThI8gy4zmBwFUdfD8AFT38f3OgXRZBo5zERJpdm4dMqsUhtrF0FRZ4P+AGIHDdhE6IX2O0FYVC3lxxKsKIwJVTaLfAkILzAuVEk7E5b4Laia7phvWetnR7xEEW6yy7+XrhW45wRRwjY0O2bP3IdBpn0qTs1KLrBP5Ey4GYWJMlLSw5dBlnzCiF0NTER97LzkrSv/XqbZ31bFtu4IaOc+wjIl4thltitzVLs6c+vPHTVnjy6Vh8B3SEJXxFGbYh9TP/LYE2U5rIpmrNE3i0LiB6squGAqYDEHQAFoqHsMofsrWr4YMcoNv+DqcbHR+YZZthN4LgRqZ24N7/gpSyMZIx4dr8fo9kzNu5D2v2JBmdiO1POxH0xbmsH7i3gLnu0Uj6LV7H1n41fBP+I2vqQjrv9UNAH2ZSpKqVSP3sS3nF+6XsVMKzfQpDfvB31eD8yWmR35mGgqTNpOWjqwiuIHXUrW1C5ZsEMai1Afy1cH2ujxS0griGGi2cLLUo437gKN/poFUCUKDRmhK2OtYbqY3g+yP4xDdAjdW60HgBPZGmiavjw+WDlmGbX4bnUP59pjg8L18kMBpFBz4jBE6ucCSBkYFYf6matQobTrbiHysLbOK9TYHn4k5btFU1wwASdqV8VOEhyXrj7OdXyRQd5BWadb/JEIdwlmJAJxtimTR+9nwyBZMFE0wzD+gE42yqFJmX/W+15e8P4YFdV/TM9tqhZcQnRmAjc2SDSm6ANeu7M/4cC0PV2cHc1DN0R8TDZMJ5gNf6FABzbH10qOq7rSrCwZ6J9Qyg9JPGfhDZhD3Fk1ROhshvrzfeN3A0GqX4D/9OMKSQYDIYKIRJjwgy0N9Hn+Q18mGs/q4ZheZ0vKTxlzEOSD/r+KQl9EUvXC4Cpp326luHlNUJKZxDjTaCQQ5wu8QmBwCAWz9EsLRm3morNZ0d+4Yk+tO6Kh3y6VfODzOQzEVheavA+2h4YpXi2mZaBtbtOS9N/dKpVg9KrqOehkBZjrVj+bIpJwXTX4uySDtTiBvPdaT16h31H3Ltwo55cK2W87VgGCJu08yIXtlnpOSgalFkg7azRuDNvfU11LCmLmuUUm5vFmcgNIiFDe8RnxeH3rSoNQsk3HJZWYFmkaENo9XwmjE6pVbZtcmhqaRPCwnXAr+Vmtmoob3iCsAh8jJD43/QgHx20XHVRwfzg+KxXvuz1oQWRIegxHhtSzoWPSXPGT4BH0";

        assertThrows(Exception.class, () -> {
            License license = licenseV1Parser.parse(emptyJson);
            System.out.println(license);
        });
    }
}
