//package com.ruijie.nse.mgr.license.security;
//
//import com.ruijie.nse.mgr.license.entity.SecureLicense;
//import com.ruijie.nse.mgr.license.exception.LicenseSecurityException;
//import com.ruijie.nse.mgr.license.validator.SecureLicenseValidator;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.*;
//
///**
// * License安全集成测试
// * 验证安全组件的集成和协作
// *
// * <AUTHOR>
// * @since 2025-07-25
// */
//@ExtendWith(MockitoExtension.class)
//@SpringJUnitConfig
//@SpringBootTest
//class LicenseSecurityIntegrationTest {
//
//    @Mock
//    private LicenseEncryptionService encryptionService;
//
//    @Mock
//    private SecureHardwareFingerprintService hardwareFingerprintService;
//
//    @Mock
//    private RuntimeDetectedService runtimeDetectedService;
//
//    @Mock
//    private LicenseProperties licenseProperties;
//
//    private SecureLicenseValidator secureLicenseValidator;
//
//    private SecureLicense testSecureLicense;
//
//    @BeforeEach
//    void setUp() {
//        // 初始化测试数据
//        setupTestLicense();
//        setupMockServices();
//
//        // 创建被测试对象
//        secureLicenseValidator = new SecureLicenseValidator(
//                encryptionService,
//                hardwareFingerprintService,
//                runtimeDetectedService,
//                licenseProperties
//        );
//    }
//
//    /**
//     * 测试正常的License验证流程
//     */
//    @Test
//    void testValidLicenseValidation() {
//        // Given
//        setupValidLicenseScenario();
//
//        // When
//        boolean result = secureLicenseValidator.validateSecureLicense(testSecureLicense);
//
//        // Then
//        assertTrue(result, "有效License应该验证通过");
//
//        // 验证各个安全组件都被调用
//        verify(runtimeDetectedService).detectThreats();
//        verify(encryptionService).verifySignature(any(), any(), any());
//        verify(encryptionService).generateIntegrityHash(any(), any());
//        verify(hardwareFingerprintService).verifyFingerprint(any(), any(), anyInt());
//    }
//
//    /**
//     * 测试运行时安全威胁检测
//     */
//    @Test
//    void testRuntimeSecurityThreatDetection() {
//        // Given
//        RuntimeDetectedService.SecurityStatus threatStatus = createHighRiskSecurityStatus();
//        when(runtimeDetectedService.detectThreats()).thenReturn(threatStatus);
//
//        // When & Then
//        LicenseSecurityException exception = assertThrows(
//                LicenseSecurityException.class,
//                () -> secureLicenseValidator.validateSecureLicense(testSecureLicense)
//        );
//
//        assertEquals("THREAT_DETECTED", exception.getSecurityEventType().name());
//        assertTrue(exception.getMessage().contains("高风险安全威胁"));
//    }
//
//    /**
//     * 测试数字签名验证失败
//     */
//    @Test
//    void testDigitalSignatureVerificationFailure() {
//        // Given
//        setupValidLicenseScenario();
//        when(encryptionService.verifySignature(any(), any(), any())).thenReturn(false);
//
//        // When & Then
//        LicenseSecurityException exception = assertThrows(
//                LicenseSecurityException.class,
//                () -> secureLicenseValidator.validateSecureLicense(testSecureLicense)
//        );
//
//        assertEquals("SIGNATURE_INVALID", exception.getSecurityEventType().name());
//        assertTrue(exception.getMessage().contains("数字签名验证失败"));
//    }
//
//    /**
//     * 测试完整性校验失败
//     */
//    @Test
//    void testIntegrityVerificationFailure() {
//        // Given
//        setupValidLicenseScenario();
//        when(encryptionService.generateIntegrityHash(any(), any()))
//                .thenReturn("different-hash");
//
//        // When & Then
//        LicenseSecurityException exception = assertThrows(
//                LicenseSecurityException.class,
//                () -> secureLicenseValidator.validateSecureLicense(testSecureLicense)
//        );
//
//        assertEquals("INTEGRITY_VIOLATION", exception.getSecurityEventType().name());
//        assertTrue(exception.getMessage().contains("完整性校验失败"));
//    }
//
//    /**
//     * 测试硬件指纹验证失败
//     */
//    @Test
//    void testHardwareFingerprintVerificationFailure() {
//        // Given
//        setupValidLicenseScenario();
//        when(hardwareFingerprintService.verifyFingerprint(any(), any(), anyInt()))
//                .thenReturn(false);
//
//        // When & Then
//        LicenseSecurityException exception = assertThrows(
//                LicenseSecurityException.class,
//                () -> secureLicenseValidator.validateSecureLicense(testSecureLicense)
//        );
//
//        assertEquals("HARDWARE_MISMATCH", exception.getSecurityEventType().name());
//        assertTrue(exception.getMessage().contains("硬件指纹不匹配"));
//    }
//
//    /**
//     * 测试License内容解密失败
//     */
//    @Test
//    void testLicenseContentDecryptionFailure() {
//        // Given
//        setupValidLicenseScenario();
//        when(encryptionService.decryptLicenseContent(any(), any()))
//                .thenThrow(new RuntimeException("解密失败"));
//
//        // When & Then
//        LicenseSecurityException exception = assertThrows(
//                LicenseSecurityException.class,
//                () -> secureLicenseValidator.validateSecureLicense(testSecureLicense)
//        );
//
//        assertEquals("DECRYPTION_FAILED", exception.getSecurityEventType().name());
//        assertTrue(exception.getMessage().contains("License内容解密失败"));
//    }
//
//    /**
//     * 测试多重安全威胁同时发生
//     */
//    @Test
//    void testMultipleSecurityThreats() {
//        // Given
//        RuntimeDetectedService.SecurityStatus threatStatus = createHighRiskSecurityStatus();
//        when(runtimeDetectedService.detectThreats()).thenReturn(threatStatus);
//        when(encryptionService.verifySignature(any(), any(), any())).thenReturn(false);
//        when(hardwareFingerprintService.verifyFingerprint(any(), any(), anyInt()))
//                .thenReturn(false);
//
//        // When & Then
//        LicenseSecurityException exception = assertThrows(
//                LicenseSecurityException.class,
//                () -> secureLicenseValidator.validateSecureLicense(testSecureLicense)
//        );
//
//        // 应该首先检测到运行时威胁
//        assertEquals("THREAT_DETECTED", exception.getSecurityEventType().name());
//    }
//
//    /**
//     * 测试License加密和解密的完整流程
//     */
//    @Test
//    void testLicenseEncryptionDecryptionFlow() {
//        // Given
//        String originalContent = "test-license-content";
//        String password = "test-password";
//
//        LicenseEncryptionService realEncryptionService = new LicenseEncryptionService();
//
//        // When
//        LicenseEncryptionService.EncryptionResult encryptionResult =
//                realEncryptionService.encryptLicenseContent(originalContent, password);
//
//        String decryptedContent = realEncryptionService.decryptLicenseContent(
//                encryptionResult, password);
//
//        // Then
//        assertNotNull(encryptionResult);
//        assertNotNull(encryptionResult.getEncryptedData());
//        assertNotNull(encryptionResult.getSalt());
//        assertNotNull(encryptionResult.getIv());
//        assertEquals(originalContent, decryptedContent);
//    }
//
//    /**
//     * 测试硬件指纹生成和验证
//     */
//    @Test
//    void testHardwareFingerprintGenerationAndVerification() {
//        // Given
//        SecureHardwareFingerprintService realHardwareService =
//                new SecureHardwareFingerprintService();
//
//        // When
//        SecureHardwareFingerprintService.HardwareProfile profile =
//                realHardwareService.collectHardwareProfile();
//
//        String fingerprint1 = realHardwareService.generateSecureFingerprint(profile);
//        String fingerprint2 = realHardwareService.generateSecureFingerprint(profile);
//
//        boolean isMatch = realHardwareService.verifyFingerprint(
//                fingerprint1, fingerprint2, 0);
//
//        // Then
//        assertNotNull(profile);
//        assertNotNull(fingerprint1);
//        assertNotNull(fingerprint2);
//        assertEquals(fingerprint1, fingerprint2);
//        assertTrue(isMatch);
//    }
//
//    /**
//     * 测试运行时安全检测
//     */
//    @Test
//    void testRuntimeSecurityDetection() {
//        // Given
//        RuntimeDetectedService realRuntimeService =
//                new RuntimeDetectedService(createTestLicenseProperties());
//
//        // When
//        RuntimeDetectedService.SecurityStatus status =
//                realRuntimeService.detectThreats();
//
//        boolean isDebugging = realRuntimeService.detectDebugging();
//
//        // Then
//        assertNotNull(status);
//        assertNotNull(status.getSecurityLevel());
//        assertNotNull(status.getThreats());
//        // 在测试环境下，调试检测结果可能为true
//    }
//
//    // ========== 辅助方法 ==========
//
//    private void setupTestLicense() {
//        testSecureLicense = new SecureLicense();
//        testSecureLicense.setId("test-license-001");
//        testSecureLicense.setEncryptedData("encrypted-test-data");
//        testSecureLicense.setSignature("test-signature");
//        testSecureLicense.setIntegrityHash("test-integrity-hash");
//        testSecureLicense.setHardwareHash("test-hardware-hash");
//        testSecureLicense.setSalt("test-salt");
//        testSecureLicense.setIv("test-iv");
//        testSecureLicense.setAlgorithm("AES-256-GCM");
//        testSecureLicense.setStatus(SecureLicense.STATUS_VALID);
//        testSecureLicense.setActive(true);
//    }
//
//    private void setupMockServices() {
//        // 设置License属性Mock
//        LicenseProperties.Security security = new LicenseProperties.Security();
//        LicenseProperties.Security.Hardware hardware = new LicenseProperties.Security.Hardware();
//        hardware.setBindingRequired(true);
//        hardware.setTolerance(1);
//        security.setHardware(hardware);
//
//        when(licenseProperties.getSecurity()).thenReturn(security);
//    }
//
//    private void setupValidLicenseScenario() {
//        // 运行时安全检查通过
//        RuntimeDetectedService.SecurityStatus safeStatus = createSafeSecurityStatus();
//        when(runtimeDetectedService.detectThreats()).thenReturn(safeStatus);
//
//        // 数字签名验证通过
//        when(encryptionService.verifySignature(any(), any(), any())).thenReturn(true);
//
//        // 完整性校验通过
//        when(encryptionService.generateIntegrityHash(any(), any()))
//                .thenReturn("test-integrity-hash");
//
//        // 硬件指纹验证通过
//        when(hardwareFingerprintService.verifyFingerprint(any(), any(), anyInt()))
//                .thenReturn(true);
//
//        // License内容解密成功
//        when(encryptionService.decryptLicenseContent(any(), any()))
//                .thenReturn("decrypted-license-content");
//    }
//
//    private RuntimeDetectedService.SecurityStatus createSafeSecurityStatus() {
//        return new RuntimeDetectedService.SecurityStatus(
//                RuntimeDetectedService.SecurityLevel.LOW_RISK,
//                java.util.Collections.emptyList()
//        );
//    }
//
//    private RuntimeDetectedService.SecurityStatus createHighRiskSecurityStatus() {
//        java.util.List<String> threats = java.util.Arrays.asList(
//                "检测到调试器连接",
//                "发现JVM参数篡改"
//        );
//
//        return new RuntimeDetectedService.SecurityStatus(
//                RuntimeDetectedService.SecurityLevel.HIGH_RISK,
//                threats
//        );
//    }
//
//    private LicenseProperties createTestLicenseProperties() {
//        LicenseProperties properties = new LicenseProperties();
//        LicenseProperties.Security security = new LicenseProperties.Security();
//
//        LicenseProperties.Security.Runtime runtime = new LicenseProperties.Security.Runtime();
//        runtime.setAntiDebug(true);
//        runtime.setMemoryProtection(true);
//        runtime.setIntegrityCheck(true);
//        runtime.setCheckInterval(30000L);
//
//        security.setRuntime(runtime);
//        properties.setSecurity(security);
//
//        return properties;
//    }
//}
