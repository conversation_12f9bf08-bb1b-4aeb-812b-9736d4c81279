//package com.ruijie.nse.mgr.license.service;
//
//import com.ruijie.nse.mgr.license.dto.LicenseDto;
//import com.ruijie.nse.mgr.license.entity.License;
//import com.ruijie.nse.mgr.license.mapper.LicenseMapper;
//import com.ruijie.nse.mgr.license.pojo.LicenseValidationResult;
//import com.ruijie.nse.mgr.license.parser.LicenseParserFactory;
//import com.ruijie.nse.mgr.license.parser.handler.LicenseV1Parser;
//import com.ruijie.nse.mgr.license.validator.LicenseValidator;
//import com.ruijie.nse.mgr.license.vo.LicenseVo;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.util.Arrays;
//import java.util.Date;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.*;
//
///**
// * License服务测试类
// *
// * <AUTHOR>
// * @since 2025-07-25
// */
//@ExtendWith(MockitoExtension.class)
//class LicenseServiceTest {
//
//    @Mock
//    private LicenseMapper licenseMapper;
//
//    @Mock
//    private LicenseValidator licenseValidator;
//
//    @Mock
//    private LicenseParserFactory licenseParserFactory;
//
//    @Mock
//    private LicenseProperties licenseProperties;
//
//    @Mock
//    private UserCountService userCountService;
//
//    @InjectMocks
//    private LicenseV1Service licenseService;
//
//    private License mockLicense;
//    private LicenseValidationResult mockValidationResult;
//
//    @BeforeEach
//    void setUp() {
//        // 创建模拟License对象
//        mockLicense = new License();
//        mockLicense.setId(1L);
//        mockLicense.setLicenseId("TEST-LICENSE-001");
//        mockLicense.setActive(true);
//        mockLicense.setImportTime(new Date());
//
//        // 创建模拟验证结果
//        mockValidationResult = LicenseValidationResult.builder()
//                .valid(true)
//                .status("VALID")
//                .validationTime(new Date())
//                .build();
//    }
//
//    @Test
//    void testValidateCurrentLicense_Success() {
//        // Given
//        when(licenseMapper.selectActiveLicense()).thenReturn(mockLicense);
//        when(licenseValidator.validate(mockLicense)).thenReturn(mockValidationResult);
//
//        // When
//        LicenseValidationResult result = licenseService.validateCurrentLicense();
//
//        // Then
//        assertNotNull(result);
//        assertTrue(result.getValid());
//        assertEquals("VALID", result.getStatus());
//        verify(licenseMapper).selectActiveLicense();
//        verify(licenseValidator).validate(mockLicense);
//    }
//
//    @Test
//    void testValidateCurrentLicense_NoLicense() {
//        // Given
//        when(licenseMapper.selectActiveLicense()).thenReturn(null);
//
//        // When
//        LicenseValidationResult result = licenseService.validateCurrentLicense();
//
//        // Then
//        assertNotNull(result);
//        assertFalse(result.getValid());
//        assertEquals("未找到有效的License", result.getErrorMessage());
//        verify(licenseMapper).selectActiveLicense();
//        verify(licenseValidator, never()).validate(any());
//    }
//
//    @Test
//    void testValidateModuleAuthorization_Success() {
//        // Given
//        when(licenseMapper.selectActiveLicense()).thenReturn(mockLicense);
//        when(licenseValidator.validateModuleAuthorization(eq(mockLicense), anyList())).thenReturn(true);
//
//        // When
//        boolean result = licenseService.validateModuleAuthorization(Arrays.asList("admin", "teacher"));
//
//        // Then
//        assertTrue(result);
//        verify(licenseMapper).selectActiveLicense();
//        verify(licenseValidator).validateModuleAuthorization(eq(mockLicense), anyList());
//    }
//
//    @Test
//    void testValidateModuleAuthorization_NoLicense() {
//        // Given
//        when(licenseMapper.selectActiveLicense()).thenReturn(null);
//
//        // When
//        boolean result = licenseService.validateModuleAuthorization(Arrays.asList("admin", "teacher"));
//
//        // Then
//        assertFalse(result);
//        verify(licenseMapper).selectActiveLicense();
//        verify(licenseValidator, never()).validateModuleAuthorization(any(), anyList());
//    }
//
//    @Test
//    void testValidateUserLimit_Success() {
//        // Given
//        LicenseVo.UserStatistics userStats = LicenseVo.UserStatistics.builder()
//                .totalUsers(50)
//                .totalManagers(5)
//                .build();
//
//        when(licenseMapper.selectActiveLicense()).thenReturn(mockLicense);
//        when(userCountService.getUserStatistics()).thenReturn(userStats);
//        when(licenseValidator.validateUserLimit(mockLicense, 50, 5)).thenReturn(true);
//
//        // When
//        boolean result = licenseService.validateUserLimit();
//
//        // Then
//        assertTrue(result);
//        verify(licenseMapper).selectActiveLicense();
//        verify(userCountService).getUserStatistics();
//        verify(licenseValidator).validateUserLimit(mockLicense, 50, 5);
//    }
//
//    @Test
//    void testGetCurrentLicenseInfo_Success() {
//        // Given
//        License.LicenseContent content = new License.LicenseContent();
//        License.LicenseContent.Header header = new License.LicenseContent.Header();
//        header.setIssuer("Test Issuer");
//        header.setIssuedAt(new Date());
//
//        License.LicenseContent.Payload payload = new License.LicenseContent.Payload();
//        License.LicenseContent.Payload.Product product = new License.LicenseContent.Payload.Product();
//        product.setName("NSE Management System");
//        product.setVersion("1.0.0");
//        payload.setProduct(product);
//
//        content.setHeader(header);
//        content.setPayload(payload);
//        mockLicense.setContent(content);
//
//        when(licenseMapper.selectActiveLicense()).thenReturn(mockLicense);
//
//        // When
//        LicenseVo.DetailInfo result = licenseService.getCurrentLicenseInfo();
//
//        // Then
//        assertNotNull(result);
//        assertEquals("TEST-LICENSE-001", result.getLicenseId());
//        assertEquals("NSE Management System", result.getProductName());
//        assertEquals("1.0.0", result.getVersion());
//        assertEquals("Test Issuer", result.getIssuer());
//        verify(licenseMapper).selectActiveLicense();
//    }
//
//    @Test
//    void testGetCurrentLicenseInfo_NoLicense() {
//        // Given
//        when(licenseMapper.selectActiveLicense()).thenReturn(null);
//
//        // When
//        LicenseVo.DetailInfo result = licenseService.getCurrentLicenseInfo();
//
//        // Then
//        assertNull(result);
//        verify(licenseMapper).selectActiveLicense();
//    }
//
//    @Test
//    void testGetLicenseStatistics_Success() {
//        // Given
//        LicenseVo.UserStatistics userStats = LicenseVo.UserStatistics.builder()
//                .totalUsers(50)
//                .totalManagers(5)
//                .activeUsers(45)
//                .build();
//
//        when(licenseMapper.selectActiveLicense()).thenReturn(mockLicense);
//        when(licenseValidator.validate(mockLicense)).thenReturn(mockValidationResult);
//        when(userCountService.getUserStatistics()).thenReturn(userStats);
//
//        // When
//        LicenseVo.StatusStatistics result = licenseService.getLicenseStatistics();
//
//        // Then
//        assertNotNull(result);
//        assertTrue(result.getHasLicense());
//        assertTrue(result.getIsValid());
//        assertEquals("VALID", result.getStatus());
//        assertEquals(50, result.getTotalUsers());
//        assertEquals(5, result.getTotalManagers());
//    }
//
//    @Test
//    void testImportLicense_Success() {
//        // Given
//        LicenseDto.ImportRequest request = new LicenseDto.ImportRequest();
//        request.setLicenseContent("test-license-content");
//        request.setFormat("json");
//        request.setImportBy("admin");
//
//        LicenseProperties.File fileProperties = new LicenseProperties.File();
//        fileProperties.setSaveToFile(false);
//        when(licenseProperties.getFile()).thenReturn(fileProperties);
//
//        // Mock parser and validator
//        when(licenseParserFactory.getParser("json")).thenReturn(mock(LicenseV1Parser.class));
//        when(licenseParserFactory.getParser("json").parse(anyString())).thenReturn(mockLicense);
//        when(licenseValidator.validate(mockLicense)).thenReturn(mockValidationResult);
//        when(licenseMapper.deactivateAllLicenses()).thenReturn(1);
//        when(licenseMapper.insert(mockLicense)).thenReturn(1);
//
//        // When
//        boolean result = licenseService.importLicense(request);
//
//        // Then
//        assertTrue(result);
//        verify(licenseParserFactory).getParser("json");
//        verify(licenseValidator).validate(mockLicense);
//        verify(licenseMapper).deactivateAllLicenses();
//        verify(licenseMapper).insert(mockLicense);
//    }
//
//    @Test
//    void testRefreshLicenseCache() {
//        // When
//        licenseService.refreshLicenseCache();
//
//        // Then - 验证方法执行无异常
//        // 由于这是一个简单的缓存清理方法，主要验证不抛异常即可
//    }
//
//    @Test
//    void testGetUserStatistics_Success() {
//        // Given
//        LicenseVo.UserStatistics expectedStats = LicenseVo.UserStatistics.builder()
//                .totalUsers(100)
//                .totalManagers(10)
//                .activeUsers(90)
//                .onlineUsers(50)
//                .build();
//
//        when(userCountService.getUserStatistics()).thenReturn(expectedStats);
//
//        // When
//        LicenseVo.UserStatistics result = licenseService.getUserStatistics();
//
//        // Then
//        assertNotNull(result);
//        assertEquals(100, result.getTotalUsers());
//        assertEquals(10, result.getTotalManagers());
//        assertEquals(90, result.getActiveUsers());
//        assertEquals(50, result.getOnlineUsers());
//        verify(userCountService).getUserStatistics();
//    }
//
//    @Test
//    void testGetHardwareFingerprint() {
//        // When
//        String result = licenseService.getHardwareFingerprint();
//
//        // Then
//        assertNotNull(result);
//        // 硬件指纹应该是非空字符串
//        assertFalse(result.isEmpty());
//    }
//
//    @Test
//    void testGenerateLicenseApplication() {
//        // When
//        LicenseVo.ApplicationInfo result = licenseService.generateLicenseApplication();
//
//        // Then
//        assertNotNull(result);
//        assertNotNull(result.getHardwareFingerprint());
//        assertNotNull(result.getApplicationTime());
//        assertNotNull(result.getSystemInfo());
//    }
//}
