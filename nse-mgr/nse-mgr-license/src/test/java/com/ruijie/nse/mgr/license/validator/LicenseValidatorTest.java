//package com.ruijie.nse.mgr.license.validator;
//
//import com.ruijie.nse.mgr.license.entity.License;
//import com.ruijie.nse.mgr.license.pojo.LicenseValidationResult;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.time.LocalDateTime;
//import java.time.ZoneId;
//import java.util.Arrays;
//import java.util.Date;
//
//import static org.junit.jupiter.api.Assertions.*;
//
///**
// * License验证器测试类
// *
// * <AUTHOR>
// * @since 2025-07-25
// */
//@ExtendWith(MockitoExtension.class)
//class LicenseValidatorTest {
//
//    private LicenseValidator licenseValidator;
//    private License validLicense;
//
//    @BeforeEach
//    void setUp() {
//        licenseValidator = new CompositeLicenseValidator();
//        validLicense = createValidLicense();
//    }
//
//    @Test
//    void testValidate_ValidLicense() {
//        // When
//        LicenseValidationResult result = licenseValidator.validate(validLicense);
//
//        // Then
//        assertNotNull(result);
//        assertTrue(result.getValid());
//        assertEquals("VALID", result.getStatus());
//        assertNull(result.getErrorMessage());
//    }
//
//    @Test
//    void testValidate_NullLicense() {
//        // When
//        LicenseValidationResult result = licenseValidator.validate(null);
//
//        // Then
//        assertNotNull(result);
//        assertFalse(result.getValid());
//        assertNotNull(result.getErrorMessage());
//    }
//
//    @Test
//    void testValidate_ExpiredLicense() {
//        // Given
//        License expiredLicense = createValidLicense();
//        // 设置过期时间为昨天
//        Date yesterday = Date.from(LocalDateTime.now().minusDays(1).atZone(ZoneId.systemDefault()).toInstant());
//        expiredLicense.getContent().getPayload().getValidity().setNotAfter(yesterday);
//
//        // When
//        LicenseValidationResult result = licenseValidator.validate(expiredLicense);
//
//        // Then
//        assertNotNull(result);
//        assertFalse(result.getValid());
//        assertTrue(result.getErrorMessage().contains("过期") || result.getErrorMessage().contains("expired"));
//    }
//
//    @Test
//    void testValidate_NotYetValidLicense() {
//        // Given
//        License futureValidLicense = createValidLicense();
//        // 设置生效时间为明天
//        Date tomorrow = Date.from(LocalDateTime.now().plusDays(1).atZone(ZoneId.systemDefault()).toInstant());
//        futureValidLicense.getContent().getPayload().getValidity().setNotBefore(tomorrow);
//
//        // When
//        LicenseValidationResult result = licenseValidator.validate(futureValidLicense);
//
//        // Then
//        assertNotNull(result);
//        assertFalse(result.getValid());
//        assertTrue(result.getErrorMessage().contains("尚未生效") || result.getErrorMessage().contains("not yet valid"));
//    }
//
//    @Test
//    void testValidateModuleAuthorization_AuthorizedModules() {
//        // Given
//        License license = createValidLicense();
//        license.getContent().getPayload().getRestrictions().setAuthorizedModules(
//                Arrays.asList("admin", "teacher", "student")
//        );
//
//        // When
//        boolean result = licenseValidator.validateModuleAuthorization(license, Arrays.asList("admin", "teacher"));
//
//        // Then
//        assertTrue(result);
//    }
//
//    @Test
//    void testValidateModuleAuthorization_UnauthorizedModules() {
//        // Given
//        License license = createValidLicense();
//        license.getContent().getPayload().getRestrictions().setAuthorizedModules(
//                Arrays.asList("student")
//        );
//
//        // When
//        boolean result = licenseValidator.validateModuleAuthorization(license, Arrays.asList("admin", "teacher"));
//
//        // Then
//        assertFalse(result);
//    }
//
//    @Test
//    void testValidateUserLimit_WithinLimit() {
//        // Given
//        License license = createValidLicense();
//        license.getContent().getPayload().getRestrictions().setMaxUsers(100);
//        license.getContent().getPayload().getRestrictions().setMaxManagers(10);
//
//        // When
//        boolean result = licenseValidator.validateUserLimit(license, 50, 5);
//
//        // Then
//        assertTrue(result);
//    }
//
//    @Test
//    void testValidateUserLimit_ExceedsUserLimit() {
//        // Given
//        License license = createValidLicense();
//        license.getContent().getPayload().getRestrictions().setMaxUsers(100);
//        license.getContent().getPayload().getRestrictions().setMaxManagers(10);
//
//        // When
//        boolean result = licenseValidator.validateUserLimit(license, 150, 5);
//
//        // Then
//        assertFalse(result);
//    }
//
//    @Test
//    void testValidateUserLimit_ExceedsManagerLimit() {
//        // Given
//        License license = createValidLicense();
//        license.getContent().getPayload().getRestrictions().setMaxUsers(100);
//        license.getContent().getPayload().getRestrictions().setMaxManagers(10);
//
//        // When
//        boolean result = licenseValidator.validateUserLimit(license, 50, 15);
//
//        // Then
//        assertFalse(result);
//    }
//
//    @Test
//    void testValidateUserLimit_NoLimit() {
//        // Given
//        License license = createValidLicense();
//        license.getContent().getPayload().getRestrictions().setMaxUsers(null);
//        license.getContent().getPayload().getRestrictions().setMaxManagers(null);
//
//        // When
//        boolean result = licenseValidator.validateUserLimit(license, 1000, 100);
//
//        // Then
//        assertTrue(result); // 无限制时应该返回true
//    }
//
//    /**
//     * 创建有效的License对象用于测试
//     *
//     * @return 有效的License对象
//     */
//    private License createValidLicense() {
//        License license = new License();
//        license.setId(1L);
//        license.setLicenseId("TEST-LICENSE-001");
//        license.setActive(true);
//
//        // 创建License内容
//        License.LicenseContent content = new License.LicenseContent();
//
//        // 创建Header
//        License.LicenseContent.Header header = new License.LicenseContent.Header();
//        header.setIssuer("Test Issuer");
//        header.setIssuedAt(new Date());
//        content.setHeader(header);
//
//        // 创建Payload
//        License.LicenseContent.Payload payload = new License.LicenseContent.Payload();
//
//        // 创建Product
//        License.LicenseContent.Payload.Product product = new License.LicenseContent.Payload.Product();
//        product.setName("NSE Management System");
//        product.setVersion("1.0.0");
//        payload.setProduct(product);
//
//        // 创建Validity
//        License.LicenseContent.Payload.Validity validity = new License.LicenseContent.Payload.Validity();
//        // 设置生效时间为昨天
//        validity.setNotBefore(Date.from(LocalDateTime.now().minusDays(1).atZone(ZoneId.systemDefault()).toInstant()));
//        // 设置过期时间为一年后
//        validity.setNotAfter(Date.from(LocalDateTime.now().plusYears(1).atZone(ZoneId.systemDefault()).toInstant()));
//        payload.setValidity(validity);
//
//        // 创建Restrictions
//        License.LicenseContent.Payload.Restrictions restrictions = new License.LicenseContent.Payload.Restrictions();
//        restrictions.setMaxUsers(1000);
//        restrictions.setMaxManagers(100);
//        restrictions.setAuthorizedModules(Arrays.asList("admin", "teacher", "student"));
//        restrictions.setHardwareFingerprint("test-hardware-fingerprint");
//        payload.setRestrictions(restrictions);
//
//        content.setPayload(payload);
//        license.setContent(content);
//
//        // 设置签名
//        license.setSignature("test-signature");
//
//        return license;
//    }
//}
