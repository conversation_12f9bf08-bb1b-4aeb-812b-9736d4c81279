package com.ruijie.nse.mgr.py3server.annotation;

import java.lang.annotation.*;

/**
 * 校验当前用户对应的SerHost是否可达或联通的注解
 * 用于方法级别的服务连通性验证
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ValidateReachable {
    
    /**
     * 是否必须有运行中的服务
     * @return true表示必须有运行中的服务，false表示允许没有服务
     */
    boolean required() default true;
    
    /**
     * 当服务不可达时的错误消息
     * @return 错误消息
     */
    String message() default "当前NSE Server服务不可达或未启动";
    
    /**
     * 是否自动启动服务（当服务不存在时）
     * @return true表示自动启动，false表示不自动启动
     */
    boolean autoStart() default false;
}