package com.ruijie.nse.mgr.py3server.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


/**
 * PY3服务配置信息
 */
@Data
@Component
@ConfigurationProperties(prefix = "nse.py3")
public class Py3ServerProperties {

    /**
     * 项目数据存储地址
     */
    private String storagePath;

    /**
     * python3服务地址
     */
    private String py3Path;


    private Ssl ssl;

    @Data
    public static class Ssl {
        private boolean enable;
    }


}
