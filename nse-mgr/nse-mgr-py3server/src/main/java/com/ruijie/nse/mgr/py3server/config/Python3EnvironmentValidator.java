package com.ruijie.nse.mgr.py3server.config;

import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Arrays;


/**
 * 用于检测当前是否有python环境。优先检测python3
 * <AUTHOR>
 * @date 2025-07-17
 */
@Slf4j
@Component
public class Python3EnvironmentValidator implements CommandLineRunner {


    private static final String[] PYTHON_COMMANDS = {"python3", "python", "/usr/bin/python3", "/usr/bin/python"};

    @Override
    public void run(String... args) throws Exception {
        PythonEnvInfo envInfo = checkPythonEnvironment();
        if(envInfo == null || !envInfo.isPython3Available()) {
            log.warn("！！！当前环境没有python环境，可能会导致nse Server启动失败！！！");
            System.exit(0);
            return ;
        }

        String python3Version = envInfo.getPython3Version();
        log.info("|===========================================");
        log.info("|--- 当前python版本为: {}", python3Version);
        log.info("|===========================================");
        python3Version = python3Version.replaceAll("\\s+","");
        if(!python3Version.startsWith("Python3")) {
            log.warn("！！！当前python版本为: {}, 建议使用python3版本！！！否则可能导致NseServer功能错误！！！", python3Version);
            System.exit(0);
        }
    }


    public PythonEnvInfo checkPythonEnvironment() {
        PythonEnvInfo info = new PythonEnvInfo();

        // 检查python命令
        info.setPython3Available(checkCommand());

        // 获取版本信息
        if (info.isPython3Available()) {
            info.setPython3Version(getCommandOutput());
        }

        return info;
    }


    private boolean checkCommand() {
        for (String cmd : PYTHON_COMMANDS) {
            try {
                String pythonCmd = Arrays.toString(new String[]{cmd, "--version"});
                log.info("检查python命令: {}", pythonCmd);
                Process process = Runtime.getRuntime().exec(new String[]{cmd, "--version"});
                int exitCode = process.waitFor();
                if (exitCode == 0) {
                    return true;
                }
            } catch (IOException | InterruptedException e) {
                // ignored
            }
        }
        return false;
    }

    private String getCommandOutput() {
        for (String cmd : PYTHON_COMMANDS) {
            try {
                Process process = Runtime.getRuntime().exec(new String[]{cmd, "--version"});
                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
                String line = reader.readLine();
                if (line != null) {
                    process.waitFor();
                    return line.trim();
                }
            } catch (IOException | InterruptedException e) {
                // ignored
            }
        }
        return "N/A";
    }

    @Getter
    @Setter
    public static class PythonEnvInfo {
        private boolean python3Available;
        private String python3Version;
    }

}
