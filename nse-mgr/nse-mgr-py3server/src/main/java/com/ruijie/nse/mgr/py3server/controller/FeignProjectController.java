package com.ruijie.nse.mgr.py3server.controller;


import com.ruijie.nse.common.dto.R;
import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.py3server.annotation.ValidateReachable;
import com.ruijie.nse.mgr.py3server.feign.adaptor.handler.FeignForwardAdaptorHandler;
import lombok.RequiredArgsConstructor;
import org.dromara.hutool.core.data.id.IdUtil;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

@RestController
@RequestMapping("/api/pyserver/project")
@RequiredArgsConstructor
public class FeignProjectController {

    private final FeignForwardAdaptorHandler feignForwardAdaptorHandler;

    @PostMapping("/create")
    @ValidateReachable(autoStart = true, message = "创建项目前需要确保Python3服务可达")
    public R<Boolean> createProject() throws IOException {
        String projectName = SecurityUtils.getUserName() + " - 测试项目 - " + IdUtil.getSeataSnowflakeNextId();
        feignForwardAdaptorHandler.createProject(IdUtil.fastUUID(), projectName);
        return R.success(Boolean.TRUE);
    }

}
