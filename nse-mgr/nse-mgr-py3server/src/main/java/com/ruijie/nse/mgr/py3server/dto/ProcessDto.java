package com.ruijie.nse.mgr.py3server.dto;


import com.ruijie.nse.mgr.py3server.launcher.context.EnvironmentContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * process进程资源情况
 * <AUTHOR>
 * @date 2025-07-23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessDto {

    /**
     * cpu使用率，0-1之间
     */
    private Double cpuUsage = 1D;

    @Builder.Default
    private long memoryTotal = (int)(EnvironmentContext.getMaxMemory() * 1024 * 1024 * 1024);
    /**
     * 内存使用，单位byte
     */
    private long memoryUsage = 0;

    @Builder.Default
    private long diskTotal = (int)(EnvironmentContext.getMaxDisk() * 1024 * 1024 * 1024);
    /**
     * 磁盘使用，单位byte
     */
    private long diskUsage = 0;

}
