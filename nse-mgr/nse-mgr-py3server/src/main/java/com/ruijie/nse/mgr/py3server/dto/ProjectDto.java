package com.ruijie.nse.mgr.py3server.dto;

import lombok.*;
import org.dromara.hutool.core.annotation.Alias;


/**
 * 项目相关信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class ProjectDto {

    @Alias("project_id")
    private String projectId;

    /**
     * 实验名称，不带后缀.nse
     */
    @Alias("name")
    private String name;

    /**
     * 实验文件名称，带.nse后缀
     */
    @Alias("filename")
    private String fileName;

    @Alias("path")
    private String projectPath;

}
