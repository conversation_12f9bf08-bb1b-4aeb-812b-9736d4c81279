package com.ruijie.nse.mgr.py3server.feign.adaptor.handler;

import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.py3server.config.Py3Constants;
import com.ruijie.nse.mgr.py3server.dto.ProjectDto;
import com.ruijie.nse.mgr.py3server.feign.adaptor.Python3FeignForwardAdaptor;
import com.ruijie.nse.mgr.py3server.launcher.context.Python3ServerContext;
import com.ruijie.nse.mgr.py3server.utils.NseServerRestUtil;
import com.ruijie.nse.mgr.repository.entity.SerHosts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.data.id.IdUtil;
import org.dromara.hutool.core.io.file.FileUtil;
import org.dromara.hutool.core.io.file.PathUtil;
import org.dromara.hutool.core.text.StrUtil;
import org.dromara.hutool.core.util.RandomUtil;
import org.dromara.hutool.http.client.body.MultipartBody;
import org.dromara.hutool.json.JSONUtil;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.Map;


/**
 * 用于gns3server的服务器接口桥接
 * <AUTHOR>
 * @date 2025-07-21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FeignForwardAdaptorHandler implements Python3FeignForwardAdaptor {

    private final NseServerRestUtil nseServerRestUtil;
    private final Python3ServerContext python3ServerContext;


    /**
     * 桥接gns3server创建项目，id由我们自己创建。如果projectId为空，默认生成一个uuid
     * @param projectId
     * @param projectName
     * @return
     */
    @Override
    public ProjectDto createProject(String projectId, String projectName) {
        if(StrUtil.isBlankIfStr(projectName)) {
            throw new IllegalArgumentException("项目名称不能为空");
        }

        try {
            if (StrUtil.isBlankIfStr(projectId)) {
                projectId = IdUtil.fastUUID();
            }

            /**
             * 这里的项目名称，直接使用项目id。 因为GNS3会对项目名称进行重复性判断
             */
            Map<String, Object> body = Map.of(
                    "project_id", projectId,
                    "name", projectId
            );
            String userId = SecurityUtils.getUserId();
            SerHosts host = python3ServerContext.getHost(userId);
            nseServerRestUtil.post(host, OpenApi.PROJECT_URI, body);

            Path projectPath = Path.of("projects", userId, projectId, projectName + Py3Constants.Project.PROJECT_EXTENSION);
            return ProjectDto.builder()
                    .projectPath(projectPath.toString())
                    .projectId(projectId)
                    .name(projectName)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("创建项目失败", e);
        }
    }

    @Override
    public ProjectDto getProject(String projectId) {
        if(StrUtil.isBlankIfStr(projectId)) {
            throw new IllegalArgumentException("项目ID不能为空");
        }

        try {
            String userId = SecurityUtils.getUserId();
            SerHosts host = python3ServerContext.getHost(userId);
            String result = nseServerRestUtil.get(host, String.format(OpenApi.PROJECT_URI_WITH_PLACE, projectId));
            return JSONUtil.toBean(result, ProjectDto.class);
        } catch (Exception e) {
            throw new RuntimeException("创建项目失败", e);
        }
    }

    @Override
    public boolean closeProject(String projectId) {
        if(StrUtil.isBlankIfStr(projectId)) {
            throw new IllegalArgumentException("项目ID不能为空");
        }

        try {
            String userId = SecurityUtils.getUserId();
            SerHosts host = python3ServerContext.getHost(userId);
            return nseServerRestUtil.delete(host, String.format(OpenApi.CLOSE_PROJECT_URI_WITH_PLACE, projectId), null);
        } catch (Exception e) {
            throw new RuntimeException("项目关闭失败", e);
        }
    }

    @Override
    public ProjectDto importProject(String projectId, String projectName, Path nseProjectPath) {
        if(StrUtil.isBlankIfStr(projectName)) {
            throw new IllegalArgumentException("项目名称不能为空");
        }

        if(nseProjectPath == null) {
            throw new IllegalArgumentException("导入项目路径不能为空");
        }

        if(!PathUtil.exists(nseProjectPath, false)) {
            throw new IllegalArgumentException("导入项目文件不存在");
        }

        try {
            if (StrUtil.isBlankIfStr(projectId)) {
                projectId = IdUtil.fastUUID();
            }

            String userId = SecurityUtils.getUserId();
            SerHosts host = python3ServerContext.getHost(userId);
            if (host == null) {
                throw new RuntimeException("未找到对应的 NSE Server Host信息");
            }

            // 复制工程项目 - 将课程包数据复制到自己的目录下
            Path courseRepoTempDir = Path.of(host.getStoragePath(), "projects", userId, projectId);
            FileUtil.mkdir(courseRepoTempDir.toString());
            Path targetPath = courseRepoTempDir.resolve(projectName + Py3Constants.Project.PROJECT_EXTENSION);
            FileUtil.copy(nseProjectPath.toFile(), targetPath.toFile(), true);

            /**
             * 构建参数
             */
            Map<String, Object> body = Map.of(
                    "path", nseProjectPath.toString().replaceAll(".nseproject$", ""),
                    "name", projectName,
                    "auto_close", true,
                    "file", nseProjectPath.toFile()
            );

            // 构建多部分表单数据
            MultipartBody multipartBody = MultipartBody.of(body, StandardCharsets.UTF_8, "----NSEWebKitFormBoundary_" + RandomUtil.randomStringLower(16));
            nseServerRestUtil.multipartData(host, String.format(OpenApi.IMPORT_PROJECT_URI_WITH_PLACE, projectId), multipartBody);

            Path projectPath = Path.of("projects", userId, projectId, projectName);
            return ProjectDto.builder()
                    .projectPath(projectPath.toString())
                    .projectId(projectId)
                    .name(projectName)
                    .build();
        } catch (Exception e) {
            throw new RuntimeException("项目导入失败", e);
        }
    }


    @Override
    public boolean deleteProject(String projectId) {
        if(StrUtil.isBlankIfStr(projectId)) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        try {
            SerHosts host = python3ServerContext.getHost(SecurityUtils.getUserId());
            return nseServerRestUtil.delete(host, String.format(OpenApi.PROJECT_URI_WITH_PLACE, projectId), null);
        } catch (Exception e) {
            throw new RuntimeException("删除项目失败", e);
        }
    }

    @Override
    public boolean openProject(String projectId) {
        if(StrUtil.isBlankIfStr(projectId)) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        try {
            SerHosts host = python3ServerContext.getHost(SecurityUtils.getUserId());
            nseServerRestUtil.post(host, String.format(OpenApi.OPEN_PROJECT_URI_WITH_PLACE, projectId), null);
            return true;
        } catch (Exception e) {
            throw new RuntimeException("项目启动失败", e );
        }
    }

    @Override
    public ProjectDto duplicateProject(String projectId, String projectName) {
        if(StrUtil.isBlankIfStr(projectId)) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        if(StrUtil.isBlankIfStr(projectName)) {
            throw new IllegalArgumentException("项目名称不能为空");
        }
        try {
            SerHosts host = python3ServerContext.getHost(SecurityUtils.getUserId());
            String bodyStr = nseServerRestUtil.post(host, String.format(OpenApi.DUPLICATE_PROJECT_URI_WITH_PLACE, projectId), Map.of("name", projectName));
            return JSONUtil.toBean(bodyStr, ProjectDto.class);
        } catch (Exception e) {
            throw new RuntimeException("项目复制失败:", e);
        }
    }

    @Override
    public ProjectDto exportProject(String projectId) {
        if(StrUtil.isBlankIfStr(projectId)) {
            throw new IllegalArgumentException("项目ID不能为空");
        }
        SerHosts host = python3ServerContext.getHost(SecurityUtils.getUserId());
        try {
            String bodyStr = nseServerRestUtil.get(host, String.format(OpenApi.EXPORT_PROJECT_URI_WITH_PLACE, projectId));
            return JSONUtil.toBean(bodyStr, ProjectDto.class);
        } catch (Exception e) {
            throw new RuntimeException("项目导出失败:" + e.getMessage());
        }
    }

}
