package com.ruijie.nse.mgr.py3server.launcher.context;


import org.dromara.hutool.core.text.StrUtil;

import java.util.Map;

/**
 * 当前服务环境全局配置
 * <AUTHOR>
 * @date 2025-07-22
 */
public class EnvironmentContext {

    private final static String CPU_CORES_KEY = "NSE_INIT_CPU_CORES";
    private final static String MAX_MEMORY_KEY = "NSE_INIT_MAX_MEMORY";
    private final static String MAX_DISK_KEY = "NSE_INIT_MAX_DISK";

    /**
     * 最大可用内存，默认10G
     */
    private static Double MAX_MEMORY = 1D;

    /**
     * 可用的cpu核心数，默认2个
     */
    private static Long CPU_CORES = 2L;

    /**
     * 最大可用磁盘空间，默认10G
     */
    private static Double MAX_DISK = 10D;

    static {
        Map<String, String> env = System.getenv();
        CPU_CORES = Long.valueOf(getByKeyOrDef(env, CPU_CORES_KEY, "2"));
        MAX_MEMORY = Double.valueOf(getByKeyOrDef(env, MAX_MEMORY_KEY, "0.5"));
        MAX_DISK = Double.valueOf(getByKeyOrDef(env, MAX_DISK_KEY, "10"));
    }

    private static String getByKeyOrDef(Map<String, String> env, String key, String def) {
        // jvm参数有的话，优先获取jvm
        String property = System.getProperty(key);
        if(StrUtil.isNotBlank(property)) {
            return property;
        }
        return env.getOrDefault(key, def);
    }


    public static Double getMaxMemory() {
        return MAX_MEMORY;
    }
    public static Double getMaxDisk() {
        return MAX_DISK;
    }
    public static Long getCpuCores() {
        return CPU_CORES;
    }

}
