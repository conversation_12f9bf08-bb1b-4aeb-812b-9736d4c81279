package com.ruijie.nse.mgr.py3server.launcher.context;


import com.ruijie.nse.common.constant.CacheConstants;
import com.ruijie.nse.common.service.cache.EhcacheService;
import com.ruijie.nse.mgr.py3server.service.SerHostsService;
import com.ruijie.nse.mgr.repository.entity.SerHosts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * 用户启动python3server上下文
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class Python3ServerContext {

    private final SerHostsService serHostsService;
    private final EhcacheService ehcacheService;
    private final static String CACHE_HOST_KEY_SUFFIX = "-sersHost";

    public void setHost(String userId, SerHosts host) {
        ehcacheService.put(CacheConstants.PYTHON_SERVER_INFO_CACHE, userId + CACHE_HOST_KEY_SUFFIX, host);
    }

    public SerHosts getHost(String userId) {
        return ehcacheService.getOrCompute(CacheConstants.PYTHON_SERVER_INFO_CACHE, userId + CACHE_HOST_KEY_SUFFIX, SerHosts.class, () -> {
            // 如果不存在，则直接从ser_hosts中获取一次
            return serHostsService.getHostByUserId(userId);
        });
    }

    public void releaseHost(String userId){
        ehcacheService.evict(CacheConstants.PYTHON_SERVER_INFO_CACHE, userId);
        // 从ser_hosts中移除
        serHostsService.removeByUserId(userId);
    }

    /**
     * 释放所有服务主机资源
     * 用于全局清理，会终止所有正在运行的Python3服务进程
     */
    public void releaseAllHosts() {
        try {
            List<SerHosts> allHosts = serHostsService.getAllHosts();
            log.info("释放所有服务主机资源，数量：{}", allHosts.size());
            serHostsService.releaseAll();

            // 清空整个缓存
            ehcacheService.clear(CacheConstants.PYTHON_SERVER_INFO_CACHE);
            
            for (SerHosts host : allHosts) {
                ProcessHandle processHandle = null;
                try {
                    // 终止进程
                    Optional<ProcessHandle> processHandleOpt = ProcessHandle.of(host.getPid());
                    if(processHandleOpt.isEmpty()) {
                        log.warn("NSE Server进程不存在: PID={}", host.getPid());
                        continue;
                    }
                    // 先获取该进程的父进程。 如果有，先删除父进程
                    ProcessHandle current = ProcessHandle.current();
                    processHandleOpt.flatMap(ProcessHandle::parent)
                            // 移除自身
                            .filter(parent -> !parent.equals(current))
                            .ifPresent(ProcessHandle::destroy);

                    processHandle = processHandleOpt.get();
                    // 终止进程树
                    processHandle.descendants().forEach(ProcessHandle::destroy);
                    processHandle.destroy();
                    log.info("终止 NSE Server进程: PID={}", host.getPid());
                } catch (Exception e) {
                    log.error("终止 NSE Server进程失败: PID={}", host.getPid(), e);
                } finally {
                    if (processHandle != null && processHandle.isAlive()) {
                        // 如果还是没终止，那么强制终止
                        processHandle.destroyForcibly();
                    }
                }
            }
            log.info("NSE Server进程缓存清理成功");

        } catch (Exception e) {
            log.error("NSE Server进程缓存清理失败", e);
        }
    }

}
