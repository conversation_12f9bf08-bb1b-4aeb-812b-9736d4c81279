package com.ruijie.nse.mgr.py3server.launcher.lifecycle;

import com.ruijie.nse.mgr.repository.entity.SerHosts;

/**
 * Python3服务生命周期管理接口
 * 提供统一的服务启动、管理和释放功能
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
public interface Python3ServerLifecycle {

    /**
     * 启动前的准备工作
     * 包括用户验证、资源检查等
     * 
     * @return 是否可以继续启动
     */
    boolean beforeLauncher();

    /**
     * 完整的启动流程
     * 按照生命周期顺序执行：beforeLauncher -> launcher0 -> afterLauncher
     * 
     * @return 启动的服务主机信息
     * @throws Exception 启动过程中的异常
     */
    default SerHosts launcher() throws Exception {
        if (!this.beforeLauncher()) {
            throw new IllegalStateException("启动前检查失败，无法启动服务");
        }
        
        SerHosts host = this.launcher0();
        
        this.afterLauncher(host);
        
        return host;
    }

    /**
     * 核心启动逻辑
     * 实际的Python3服务启动过程
     *
     * @return 启动的服务主机信息
     * @throws Exception 启动过程中的异常
     */
    SerHosts launcher0() throws Exception;

    /**
     * 启动后的处理工作
     * 包括缓存存储、日志记录等
     * 
     * @param host 启动的服务主机信息
     */
    void afterLauncher(SerHosts host);

    /**
     * 释放指定用户的Python3服务
     * 包括进程终止、端口释放、缓存清理等
     * 
     * @param userId 用户ID
     * @return 是否成功释放
     */
    boolean release(String userId);

    /**
     * 获取指定用户的服务信息
     * 
     * @param userId 用户ID
     * @return 服务主机信息，如果不存在则返回null
     */
    SerHosts getServerInfo(String userId);

    /**
     * 检查指定用户是否已有运行中的服务
     * 
     * @param userId 用户ID
     * @return 是否有运行中的服务
     */
    boolean hasRunningServer(String userId);

}
