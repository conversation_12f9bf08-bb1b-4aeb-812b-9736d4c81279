package com.ruijie.nse.mgr.py3server.launcher.manager;

import com.ruijie.nse.mgr.py3server.launcher.context.Python3ServerContext;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 全局关闭钩子管理器
 * 负责在JVM关闭时清理所有Python3服务相关资源
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GlobalShutdownHookManager {

    private final Python3ServerContext python3ServerContext;

    private final AtomicBoolean shutdownHookRegistered = new AtomicBoolean(false);
    private Thread shutdownHook;

    @PostConstruct
    public void init() {
        registerShutdownHook();
    }

    /**
     * 注册JVM关闭钩子。
     * 这个钩子是整个JVM进程退出时，需要执行的。
     * 这里需要清理掉所有的Python3服务相关资源，包括Python3服务，端口信息等。
     */
    private void registerShutdownHook() {
        if (shutdownHookRegistered.compareAndSet(false, true)) {
            // 清理所有的python3资源
            shutdownHook = new Thread(python3ServerContext::releaseAllHosts, "Python3Server-Shutdown-Hook");
            Runtime.getRuntime().addShutdownHook(shutdownHook);
            log.info("[Python3Server] - hook 注册成功！");
        }
    }


    /**
     * spring注销时候，也释放
     */
    @PreDestroy
    public void destroy() {
        python3ServerContext.releaseAllHosts();
        
        // 移除关闭钩子（如果还存在）
        if (shutdownHook != null && shutdownHookRegistered.get()) {
            try {
                Runtime.getRuntime().removeShutdownHook(shutdownHook);
            } catch (IllegalStateException ignored) {
                // ignored
            }
        }
    }
}