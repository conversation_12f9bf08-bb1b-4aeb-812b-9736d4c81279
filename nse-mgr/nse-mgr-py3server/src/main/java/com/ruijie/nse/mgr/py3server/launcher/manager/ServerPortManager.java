package com.ruijie.nse.mgr.py3server.launcher.manager;

import java.io.IOException;
import java.net.ServerSocket;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;


/**
 * 启动server的端口管理器。主要用于随机端口复用
 * <AUTHOR>
 * @date 2025-07-22
 */
public class ServerPortManager {

    private final int minPort;
    private final int maxPort;
    private int nextPort;
    private final Set<Integer> allPorts = ConcurrentHashMap.newKeySet();
    private final Queue<Integer> releasedPorts = new ConcurrentLinkedQueue<>();

    public ServerPortManager(int minPort, int maxPort) {
        this.minPort = minPort;
        this.maxPort = maxPort;
        this.nextPort = minPort;
    }

    public synchronized int acquireAvailablePort() throws IllegalAccessException {
        // 优先使用被释放的端口
        while (!releasedPorts.isEmpty()) {
            Integer port = releasedPorts.poll();
            if (port != null && isPortAvailable(port) && !allPorts.contains(port)) {
                allPorts.add(port);
                return port;
            }
        }

        // 获取新端口
        while (nextPort <= maxPort) {
            int port = nextPort++;
            if (!allPorts.contains(port)) {
                if (isPortAvailable(port)) {
                    allPorts.add(port);
                    return port;
                }
            }
        }

        throw new IllegalAccessException("当前暂无可分配的服务端口，请排队或重试");
    }

    /**
     * 释放端口
     * @param port
     */
    public synchronized void releasePort(int port) {
        if (port >= minPort && port <= maxPort) {
            allPorts.remove(port);
            releasedPorts.offer(port);
        }
    }

    /**
     * 检测端口是否被使用
     * @param port
     * @return
     */
    private boolean isPortAvailable(int port) {
        try (ServerSocket serverSocket = new ServerSocket(port)) {
            serverSocket.setReuseAddress(true);
            return true;
        } catch (IOException e) {
            return false;
        }
    }
}
