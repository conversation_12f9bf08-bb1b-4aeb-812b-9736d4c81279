package com.ruijie.nse.mgr.py3server.listener;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruijie.nse.mgr.common.dto.EventMessage;
import com.ruijie.nse.mgr.common.dto.event.payload.ResourceReleaseEventPayload;
import com.ruijie.nse.mgr.license.service.EventVerifyRecordsService;
import com.ruijie.nse.mgr.py3server.launcher.Python3ServerLauncher;
import com.ruijie.nse.mgr.repository.entity.enums.EventTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;

/**
 * Py3Server事件监听器
 * 专门处理Python3服务器和学生Py3Server相关的事件
 *
 * <AUTHOR> Team
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class Py3ServerEventListener {

    private final EventVerifyRecordsService eventVerifyRecordsService;
    private final Python3ServerLauncher python3ServerLauncher;
    private final ObjectMapper objectMapper;

    /**
     * 监听Py3Server相关事件
     * 通过Spring Integration的ServiceActivator注解监听特定通道的消息
     */
    @ServiceActivator(inputChannel = "py3ServerChannel")
    public void handlePy3ServerEvent(@Payload EventMessage eventMessage,
                                         @Header("eventType") String eventType) {
        try {
            log.info("接收到Py3Server事件: type={}, message={}", eventType, eventMessage.getEventMessage());
            
            EventTypeEnum eventTypeEnum = EventTypeEnum.valueOf(eventType);

            if (eventTypeEnum == EventTypeEnum.EVT_PY3_SERVER_RELEASE) {
                handlePy3ServerRelease(eventMessage);
            } else {
                log.debug("未处理的事件类型: {}", eventType);
            }
            
        } catch (Exception e) {
            log.error("处理Py3Server事件失败: eventType={}, error={}", eventType, e.getMessage(), e);
        }
    }


    /**
     * 处理Python3服务器释放事件
     */
    private void handlePy3ServerRelease(EventMessage eventMessage) {
        try {
            // 保存事件
            eventVerifyRecordsService.saveEventMessage(eventMessage);

            ResourceReleaseEventPayload eventData = parseEventData(eventMessage);
            if (eventData == null || !StringUtils.hasText(eventData.getUserId())) {
                log.warn("Python3服务器释放事件数据无效: {}", eventMessage.getEventDetails());
                return;
            }

            String userId = eventData.getUserId();
            log.info("开始处理Python3服务器释放: userId={}", userId);

            // 直接释放Python3服务器资源
            releasePython3ServerResources(userId, eventData);

        } catch (Exception e) {
            log.error("处理Python3服务器释放事件失败: {}", e.getMessage(), e);
        }
    }


    /**
     * 释放Python3服务器资源
     */
    private void releasePython3ServerResources(String userId, ResourceReleaseEventPayload eventData) {
        try {
            boolean released = python3ServerLauncher.release(userId);
            if (released) {
                log.info("Python3服务器Py3Server成功: userId={}, reason={}", 
                        userId, eventData.getReleaseReason());
            } else {
                log.warn("Python3服务器Py3Server失败或无需释放: userId={}", userId);
            }
        } catch (Exception e) {
            log.error("释放Python3服务器资源异常: userId={}, error={}", userId, e.getMessage(), e);
        }
    }

    /**
     * 解析事件数据
     */
    private ResourceReleaseEventPayload parseEventData(EventMessage eventMessage) {
        try {
            if (StringUtils.hasText(eventMessage.getPayload())) {
                return objectMapper.readValue(eventMessage.getPayload(), ResourceReleaseEventPayload.class);
            } else if (StringUtils.hasText(eventMessage.getEventDetails())) {
                return objectMapper.readValue(eventMessage.getEventDetails(), ResourceReleaseEventPayload.class);
            }
        } catch (JsonProcessingException e) {
            log.warn("解析事件数据失败: {}", e.getMessage());
        }
        
        return ResourceReleaseEventPayload.builder()
                .releaseTime(LocalDateTime.now())
                .build();
    }
}
