package com.ruijie.nse.mgr.py3server.service;


import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.py3server.config.Py3ServerProperties;
import com.ruijie.nse.mgr.py3server.dto.ProcessDto;
import lombok.RequiredArgsConstructor;
import org.dromara.hutool.core.io.file.PathUtil;
import org.springframework.stereotype.Service;
import oshi.SystemInfo;
import oshi.software.os.OSProcess;
import oshi.software.os.OperatingSystem;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.stream.Stream;

/**
 * python3每个process的资源情况
 */
@Service
@RequiredArgsConstructor
public class ProcessService {

    private final Py3ServerProperties py3ServerProperties;

    public boolean isValidPid(long pid) {
        OperatingSystem os = new SystemInfo().getOperatingSystem();
        OSProcess process = os.getProcess((int) pid);
        return process != null;
    }

    /**
     * 获取python3进程的资源情况
     *
     * @param pid  process进程id
     * @return
     */
    public ProcessDto getProcessInfo(long pid) throws IOException {
        OperatingSystem os = new SystemInfo().getOperatingSystem();
        OSProcess process = os.getProcess((int) pid);
        if(process == null) {
            throw new IllegalCallerException("该进程[" + pid + "]不存在");
        }

        // 获取CPU使用率(0-100)
        double cpuUsage = process.getProcessCpuLoadCumulative();

        // 获取内存信息
        long memoryUsage = process.getResidentSetSize();

        long diskUsage = getDiskUsage(SecurityUtils.getUserId());

        return ProcessDto.builder()
                .cpuUsage(cpuUsage)
                .memoryUsage(memoryUsage)
                .diskUsage(diskUsage)
                .build();
    }


    /**
     * 获取用户的磁盘使用量，对于用户来说。目前最大的使用量就是projects
     * @param userId
     * @return
     * @throws IOException
     */
    private long getDiskUsage(String userId) throws IOException {
        Path userDir = Paths.get(py3ServerProperties.getStoragePath(), "projects", userId);
        if(!PathUtil.exists(userDir, false)) {
            PathUtil.mkdir(userDir);
        }
        try (Stream<Path> fileWalk = Files.walk(userDir)) {
            return fileWalk.filter(p -> p.toFile().isFile())
                    .mapToLong(p -> p.toFile().length())
                    .sum();
        }
    }


}
