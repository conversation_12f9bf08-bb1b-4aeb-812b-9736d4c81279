package com.ruijie.nse.mgr.py3server.service;


import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.py3server.config.Py3ServerProperties;
import com.ruijie.nse.mgr.py3server.dto.ProcessDto;
import lombok.RequiredArgsConstructor;
import org.dromara.hutool.core.io.file.PathUtil;
import org.springframework.stereotype.Service;
import oshi.SystemInfo;
import oshi.software.os.OSProcess;
import oshi.software.os.OperatingSystem;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.stream.Stream;

/**
 * python3每个process的资源情况
 */
@Service
@RequiredArgsConstructor
public class ProcessService {

    private final Py3ServerProperties py3ServerProperties;

    public boolean isValidPid(long pid) {
        try {
            OperatingSystem os = new SystemInfo().getOperatingSystem();
            OSProcess process = os.getProcess((int) pid);
            // 检查进程是否存在且仍在运行
            return process != null && process.getProcessID() > 0;
        } catch (Exception e) {
            // 如果获取进程信息时发生异常，认为进程不存在
            return false;
        }
    }

    /**
     * 获取python3进程的资源情况
     *
     * @param pid  process进程id
     * @return ProcessDto 进程资源信息
     * @throws IOException 磁盘使用量获取失败时抛出
     * @throws IllegalArgumentException 进程不存在时抛出
     */
    public ProcessDto getProcessInfo(long pid) throws IOException {
        try {
            OperatingSystem os = new SystemInfo().getOperatingSystem();
            OSProcess process = os.getProcess((int) pid);

            if (process == null || process.getProcessID() <= 0) {
                throw new IllegalArgumentException("该进程[" + pid + "]不存在");
            }

            // 获取CPU使用率(0-100)
            double cpuUsage = process.getProcessCpuLoadCumulative();

            // 获取内存信息
            long memoryUsage = process.getResidentSetSize();

            long diskUsage = getDiskUsage(SecurityUtils.getUserId());

            return ProcessDto.builder()
                    .cpuUsage(cpuUsage)
                    .memoryUsage(memoryUsage)
                    .diskUsage(diskUsage)
                    .build();
        } catch (IllegalArgumentException e) {
            // 重新抛出参数异常
            throw e;
        } catch (Exception e) {
            // 其他异常转换为参数异常
            throw new IllegalArgumentException("获取进程[" + pid + "]信息失败: " + e.getMessage(), e);
        }
    }


    /**
     * 获取用户的磁盘使用量，对于用户来说。目前最大的使用量就是projects
     * @param userId
     * @return
     * @throws IOException
     */
    private long getDiskUsage(String userId) throws IOException {
        Path userDir = Paths.get(py3ServerProperties.getStoragePath(), "projects", userId);
        if(!PathUtil.exists(userDir, false)) {
            PathUtil.mkdir(userDir);
        }
        try (Stream<Path> fileWalk = Files.walk(userDir)) {
            return fileWalk.filter(p -> p.toFile().isFile())
                    .mapToLong(p -> p.toFile().length())
                    .sum();
        }
    }


}
