package com.ruijie.nse.mgr.py3server.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruijie.nse.mgr.repository.entity.SerClients;
import com.ruijie.nse.mgr.repository.entity.SerHosts;
import com.ruijie.nse.mgr.repository.mapper.SerClientsDao;
import com.ruijie.nse.mgr.repository.mapper.SerHostsDao;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


@Service
@RequiredArgsConstructor
public class SerHostsService extends ServiceImpl<SerHostsDao, SerHosts> {

    private final SerClientsDao serClientsDao;

    public SerHosts getHostByUserId(String userId) {
        List<SerHosts> serHosts = this.baseMapper.selectList(Wrappers.lambdaQuery(SerHosts.class).eq(SerHosts::getUserId, userId).orderByDesc(SerHosts::getCreatedDate));
        return serHosts.isEmpty() ? null : serHosts.getFirst();
    }

    @Transactional
    public void removeByUserId(String userId) {
        this.baseMapper.deletePhysically(Wrappers.lambdaQuery(SerHosts.class).eq(SerHosts::getUserId, userId));
        serClientsDao.deletePhysically(Wrappers.lambdaQuery(SerClients.class).eq(SerClients::getUserId, userId));
    }

    public List<SerHosts> getAllHosts() {
        return this.baseMapper.selectList(Wrappers.lambdaQuery(SerHosts.class));
    }

    @Transactional
    public void removeByHostId(String hostId) {
        this.baseMapper.deletePhysically(Wrappers.lambdaQuery(SerHosts.class).eq(SerHosts::getId, hostId));
        serClientsDao.deletePhysically(Wrappers.lambdaQuery(SerClients.class).eq(SerClients::getHostId, hostId));
    }

    @Transactional
    public void releaseAll() {
        this.baseMapper.deletePhysically(Wrappers.lambdaQuery(SerHosts.class));
        serClientsDao.deletePhysically(Wrappers.lambdaQuery(SerClients.class));
    }
}
