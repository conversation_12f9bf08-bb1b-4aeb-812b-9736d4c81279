package com.ruijie.nse.mgr.py3server.util;

import com.ruijie.nse.common.utils.system.OSUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Python命令工具类
 * 统一管理Python命令查找和匹配逻辑
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
public class PythonCommandUtil {

    /**
     * Linux/Mac系统的Python命令列表，按优先级排序
     */
    private static final String[] UNIX_PYTHON_COMMANDS = {"/usr/bin/python3", "python3", "python"};

    /**
     * Windows系统的Python命令列表，按优先级排序
     */
    private static final String[] WINDOWS_PYTHON_COMMANDS = {
        "python.exe", "py.exe", "python3.exe",
        "C:\\Python3\\python.exe", "C:\\Python39\\python.exe",
        "C:\\Python310\\python.exe", "C:\\Python311\\python.exe",
        "C:\\Python312\\python.exe", "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python3\\python.exe"
    };
    
    /**
     * NSE服务的Python模块名
     */
    private static final String NSE_PYTHON_MODULE = "nseserver.main";
    
    /**
     * 缓存的可用Python命令
     */
    private static volatile String cachedPythonCommand = null;

    /**
     * 查找可用的Python命令
     * 根据操作系统类型选择合适的Python命令列表
     *
     * @return 可用的Python命令路径
     */
    public static String findPythonCommand() {
        if (cachedPythonCommand != null) {
            return cachedPythonCommand;
        }

        synchronized (PythonCommandUtil.class) {
            if (cachedPythonCommand != null) {
                return cachedPythonCommand;
            }

            // 根据操作系统选择Python命令列表
            String[] pythonCommands = getPythonCommandsByOS();

            // 尝试每个Python命令
            for (String command : pythonCommands) {
                try {
                    // 处理Windows环境变量
                    String resolvedCommand = resolveCommand(command);

                    ProcessBuilder processBuilder = new ProcessBuilder(resolvedCommand, "--version");
                    Process process = processBuilder.start();
                    int exitCode = process.waitFor();

                    if (exitCode == 0) {
                        log.info("使用Python命令: {}", resolvedCommand);
                        cachedPythonCommand = resolvedCommand;
                        return resolvedCommand;
                    }
                } catch (Exception e) {
                    // 命令不可用，继续尝试下一个
                    log.debug("Python命令 {} 不可用: {}", command, e.getMessage());
                }
            }

            // 如果都不可用，根据操作系统返回默认值
            String defaultCommand = getDefaultPythonCommand();
            log.warn("未找到可用的Python3命令，使用默认值: {}", defaultCommand);
            cachedPythonCommand = defaultCommand;
            return cachedPythonCommand;
        }
    }

    /**
     * 根据操作系统获取Python命令列表
     *
     * @return Python命令数组
     */
    private static String[] getPythonCommandsByOS() {
        return OSUtil.isWindows() ? WINDOWS_PYTHON_COMMANDS : UNIX_PYTHON_COMMANDS;
    }

    /**
     * 解析命令中的环境变量（主要针对Windows）
     *
     * @param command 原始命令
     * @return 解析后的命令
     */
    private static String resolveCommand(String command) {
        if (OSUtil.isWindows() && command.contains("%USERNAME%")) {
            String username = System.getProperty("user.name");
            return command.replace("%USERNAME%", username);
        }
        return command;
    }

    /**
     * 获取默认的Python命令
     *
     * @return 默认Python命令
     */
    private static String getDefaultPythonCommand() {
        return OSUtil.isWindows() ? "python.exe" : "/usr/bin/python3";
    }

    /**
     * 获取所有可能的Python命令列表
     *
     * @return Python命令列表
     */
    public static List<String> getAllPythonCommands() {
        List<String> allCommands = new ArrayList<>();
        allCommands.addAll(Arrays.asList(UNIX_PYTHON_COMMANDS));
        allCommands.addAll(Arrays.asList(WINDOWS_PYTHON_COMMANDS));
        return allCommands;
    }

    /**
     * 检查命令行是否匹配NSE Python服务进程
     * 支持Windows和Linux/Mac系统的不同路径格式
     *
     * @param commandLine 进程的命令行
     * @return 是否匹配NSE Python服务进程
     */
    public static boolean isNsePythonProcess(String commandLine) {
        if (commandLine == null || commandLine.trim().isEmpty()) {
            return false;
        }

        // 检查是否包含NSE模块
        if (!commandLine.contains("-m " + NSE_PYTHON_MODULE)) {
            return false;
        }

        // 获取所有可能的Python命令
        List<String> allPythonCommands = getAllPythonCommands();

        // 检查是否包含任何支持的Python命令
        for (String pythonCmd : allPythonCommands) {
            // 直接匹配完整命令
            if (commandLine.contains(pythonCmd + " -m " + NSE_PYTHON_MODULE)) {
                return true;
            }

            // 对于Windows，还要检查不带.exe后缀的情况
            if (OSUtil.isWindows() && pythonCmd.endsWith(".exe")) {
                String cmdWithoutExt = pythonCmd.substring(0, pythonCmd.length() - 4);
                if (commandLine.contains(cmdWithoutExt + " -m " + NSE_PYTHON_MODULE)) {
                    return true;
                }
            }

            // 检查只包含文件名的情况（不包含路径）
            String fileName = getFileName(pythonCmd);
            if (commandLine.contains(fileName + " -m " + NSE_PYTHON_MODULE)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 从完整路径中提取文件名
     *
     * @param path 完整路径
     * @return 文件名
     */
    private static String getFileName(String path) {
        if (path == null || path.isEmpty()) {
            return path;
        }

        // 处理Windows和Unix路径分隔符
        int lastSlash = Math.max(path.lastIndexOf('/'), path.lastIndexOf('\\'));
        return lastSlash >= 0 ? path.substring(lastSlash + 1) : path;
    }

    /**
     * 生成NSE Python进程的匹配模式列表
     * 根据操作系统生成相应的匹配模式
     *
     * @return 匹配模式列表
     */
    public static List<String> getNsePythonProcessPatterns() {
        List<String> patterns = new ArrayList<>();
        List<String> allCommands = getAllPythonCommands();

        for (String cmd : allCommands) {
            patterns.add(cmd + " -m " + NSE_PYTHON_MODULE);

            // 对于Windows，添加不带.exe后缀的模式
            if (OSUtil.isWindows() && cmd.endsWith(".exe")) {
                String cmdWithoutExt = cmd.substring(0, cmd.length() - 4);
                patterns.add(cmdWithoutExt + " -m " + NSE_PYTHON_MODULE);
            }

            // 添加只包含文件名的模式
            String fileName = getFileName(cmd);
            if (!fileName.equals(cmd)) {
                patterns.add(fileName + " -m " + NSE_PYTHON_MODULE);
            }
        }

        return patterns;
    }

    /**
     * 构建Python进程启动命令
     * 
     * @param port 端口号
     * @param host 主机地址
     * @return 完整的命令数组
     */
    public static String[] buildPythonCommand(int port, String host) {
        String pythonCmd = findPythonCommand();
        return new String[]{
                pythonCmd, "-m", NSE_PYTHON_MODULE, 
                "--port", String.valueOf(port),
                "--host", host
        };
    }

    /**
     * 获取NSE Python模块名
     * 
     * @return 模块名
     */
    public static String getNsePythonModule() {
        return NSE_PYTHON_MODULE;
    }

    /**
     * 清除缓存的Python命令
     * 主要用于测试或重新检测
     */
    public static void clearCache() {
        cachedPythonCommand = null;
    }

    /**
     * 检查指定的Python命令是否可用
     * 
     * @param command Python命令
     * @return 是否可用
     */
    public static boolean isPythonCommandAvailable(String command) {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(command, "--version");
            Process process = processBuilder.start();
            int exitCode = process.waitFor();
            return exitCode == 0;
        } catch (Exception e) {
            log.debug("检查Python命令 {} 可用性失败: {}", command, e.getMessage());
            return false;
        }
    }
}
