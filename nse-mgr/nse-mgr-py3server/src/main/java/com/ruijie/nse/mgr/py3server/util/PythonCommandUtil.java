package com.ruijie.nse.mgr.py3server.util;

import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * Python命令工具类
 * 统一管理Python命令查找和匹配逻辑
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
public class PythonCommandUtil {

    /**
     * 支持的Python命令列表，按优先级排序
     * 与Python3ServerLauncher保持一致
     */
    private static final String[] PYTHON_COMMANDS = {"/usr/bin/python3", "python3", "python"};
    
    /**
     * NSE服务的Python模块名
     */
    private static final String NSE_PYTHON_MODULE = "nseserver.main";
    
    /**
     * 缓存的可用Python命令
     */
    private static volatile String cachedPythonCommand = null;

    /**
     * 查找可用的Python命令
     * 与Python3ServerLauncher.findPythonCommand()逻辑保持一致
     * 
     * @return 可用的Python命令路径
     */
    public static String findPythonCommand() {
        if (cachedPythonCommand != null) {
            return cachedPythonCommand;
        }
        
        synchronized (PythonCommandUtil.class) {
            if (cachedPythonCommand != null) {
                return cachedPythonCommand;
            }
            
            // 首先尝试使用完整路径
            for (String command : PYTHON_COMMANDS) {
                try {
                    ProcessBuilder processBuilder = new ProcessBuilder(command, "--version");
                    Process process = processBuilder.start();
                    int exitCode = process.waitFor();
                    
                    if (exitCode == 0) {
                        log.info("使用Python命令: {}", command);
                        cachedPythonCommand = command;
                        return command;
                    }
                } catch (Exception e) {
                    // 命令不可用，继续尝试下一个
                    log.debug("Python命令 {} 不可用: {}", command, e.getMessage());
                }
            }
            
            // 如果都不可用，默认返回/usr/bin/python3
            log.warn("未找到可用的Python3命令，使用默认值: /usr/bin/python3");
            cachedPythonCommand = "/usr/bin/python3";
            return cachedPythonCommand;
        }
    }

    /**
     * 获取所有可能的Python命令列表
     * 
     * @return Python命令列表
     */
    public static List<String> getAllPythonCommands() {
        return Arrays.asList(PYTHON_COMMANDS);
    }

    /**
     * 检查命令行是否匹配NSE Python服务进程
     * 
     * @param commandLine 进程的命令行
     * @return 是否匹配NSE Python服务进程
     */
    public static boolean isNsePythonProcess(String commandLine) {
        if (commandLine == null || commandLine.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否包含NSE模块
        if (!commandLine.contains("-m " + NSE_PYTHON_MODULE)) {
            return false;
        }
        
        // 检查是否以任何支持的Python命令开头
        for (String pythonCmd : PYTHON_COMMANDS) {
            if (commandLine.contains(pythonCmd + " -m " + NSE_PYTHON_MODULE)) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 生成NSE Python进程的匹配模式列表
     * 用于在ps命令输出中查找NSE进程
     * 
     * @return 匹配模式列表
     */
    public static List<String> getNsePythonProcessPatterns() {
        return Arrays.stream(PYTHON_COMMANDS)
                .map(cmd -> cmd + " -m " + NSE_PYTHON_MODULE)
                .toList();
    }

    /**
     * 构建Python进程启动命令
     * 
     * @param port 端口号
     * @param host 主机地址
     * @return 完整的命令数组
     */
    public static String[] buildPythonCommand(int port, String host) {
        String pythonCmd = findPythonCommand();
        return new String[]{
                pythonCmd, "-m", NSE_PYTHON_MODULE, 
                "--port", String.valueOf(port),
                "--host", host
        };
    }

    /**
     * 获取NSE Python模块名
     * 
     * @return 模块名
     */
    public static String getNsePythonModule() {
        return NSE_PYTHON_MODULE;
    }

    /**
     * 清除缓存的Python命令
     * 主要用于测试或重新检测
     */
    public static void clearCache() {
        cachedPythonCommand = null;
    }

    /**
     * 检查指定的Python命令是否可用
     * 
     * @param command Python命令
     * @return 是否可用
     */
    public static boolean isPythonCommandAvailable(String command) {
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(command, "--version");
            Process process = processBuilder.start();
            int exitCode = process.waitFor();
            return exitCode == 0;
        } catch (Exception e) {
            log.debug("检查Python命令 {} 可用性失败: {}", command, e.getMessage());
            return false;
        }
    }
}
