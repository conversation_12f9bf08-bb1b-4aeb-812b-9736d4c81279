package com.ruijie.nse.mgr.py3server.utils;

import com.ruijie.nse.common.constant.CommonConstant;
import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.py3server.config.Py3Constants;
import com.ruijie.nse.mgr.repository.entity.SerHosts;
import com.ruijie.nse.mgr.repository.pojo.bo.BasicAuthBo;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.date.DateUtil;
import org.dromara.hutool.core.text.StrPool;
import org.dromara.hutool.http.HttpUtil;
import org.dromara.hutool.http.client.Request;
import org.dromara.hutool.http.client.Response;
import org.dromara.hutool.http.client.body.MultipartBody;
import org.dromara.hutool.http.meta.Method;
import org.dromara.hutool.json.JSONUtil;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class NseServerRestUtil {

    public void header() {

    }

    public String get(SerHosts host, String url) throws IOException {
        BasicAuthBo basicAuth = host.getBasicAuth();
        String baseUrl = getDomain(host);
        Request request = HttpUtil.createGet(baseUrl + url)
                .basicAuth(basicAuth.getKey(), basicAuth.getSecret())
                .header(Py3Constants.Server.PY3_HTTP_HEADER_TOKEN, buildBearerToken())
                .contentType(MediaType.APPLICATION_JSON_VALUE);
        try (Response response = request.send()) {
            if (!response.isOk()) {
                throw new RuntimeException("URL: [" + url + "] - [GET] 请求失败，状态码：" + response.getStatus() + "，响应：" + response.body());
            }
            return response.bodyStr();
        }
    }

    public String post(SerHosts host, String url, Map<String, Object> body) throws IOException {
        BasicAuthBo basicAuth = host.getBasicAuth();
        String baseUrl = getDomain(host);
        Request request = HttpUtil.createPost(baseUrl + url)
                .basicAuth(basicAuth.getKey(), basicAuth.getSecret())
                .header(Py3Constants.Server.PY3_HTTP_HEADER_TOKEN, buildBearerToken())
                .contentType(MediaType.APPLICATION_JSON_VALUE);
        if(body != null && !body.isEmpty()) {
            request.body(JSONUtil.toJsonStr(body));
        }
        try (Response response = request.send()) {
            if (!response.isOk()) {
                throw new RuntimeException("URL: [" + url + "] - [POST] 请求失败，状态码：" + response.getStatus() + "，响应：" + response.body());
            }
            return response.bodyStr();
        }
    }

    public boolean form(SerHosts host, String url, Map<String, Object> body) throws IOException {
        BasicAuthBo basicAuth = host.getBasicAuth();
        String baseUrl = getDomain(host);
        Request request = HttpUtil.createPost(baseUrl + url)
                .basicAuth(basicAuth.getKey(), basicAuth.getSecret())
                .header(Py3Constants.Server.PY3_HTTP_HEADER_TOKEN, buildBearerToken())
                .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .form(body);

        try (Response response = request.send()) {
            if (!response.isOk()) {
                throw new RuntimeException("URL: [" + url + "] - [FORM] 请求失败，状态码：" + response.getStatus() + "，响应：" + response.body());
            }
            return response.isOk();
        }
    }

    public boolean multipartData(SerHosts host, String url, MultipartBody multipartBody) throws IOException {
        BasicAuthBo basicAuth = host.getBasicAuth();
        String baseUrl = getDomain(host);
        Request request = HttpUtil.createPost(baseUrl + url)
                .basicAuth(basicAuth.getKey(), basicAuth.getSecret())
                .header(Py3Constants.Server.PY3_HTTP_HEADER_TOKEN, buildBearerToken())
                .contentType(MediaType.MULTIPART_FORM_DATA_VALUE)
                .body(multipartBody);

        try (Response response = request.send()) {
            if (!response.isOk()) {
                throw new RuntimeException("URL: [" + url + "] - [MULTIPART_FORM] 请求失败，状态码：" + response.getStatus() + "，响应：" + response.body());
            }
            return response.isOk();
        }
    }

    public boolean delete(SerHosts host, String url, Map<String, String> body) throws IOException {
        BasicAuthBo basicAuth = host.getBasicAuth();
        String baseUrl = getDomain(host);
        Request request = HttpUtil.createRequest(baseUrl + url, Method.DELETE)
                .basicAuth(basicAuth.getKey(), basicAuth.getSecret())
                .header(Py3Constants.Server.PY3_HTTP_HEADER_TOKEN, buildBearerToken())
                .contentType(MediaType.APPLICATION_JSON_VALUE);
        if(body != null && !body.isEmpty()) {
            request.body(JSONUtil.toJsonStr(body));
        }

        // GNS3 delete project响应码为204，说明响应body为null，这是合理的。但是hutool在处理delete请求时候，会缓存response.body，可能会导致npe
        // 目前hutool 不兼容delete且没有开放配置项，先修改gns3 delete project响应空body
        try (Response response = request.send()) {
            if (!response.isOk()) {
                throw new RuntimeException("URL: [" + url + "] - [DELETE] 请求失败，状态码：" + response.getStatus());
            }
            return response.isOk();
        }
    }


    private String buildBearerToken() {
        return "Bearer " + buildToken();
    }

    private String buildToken() {
        String userId = SecurityUtils.getUserId();
        return Jwts.builder()
                .subject(userId)
                .claim("user_id", userId)
                .claim("user_type", SecurityUtils.getUserType())
                .claim(CommonConstant.Jwt.CLAIM_USERNAME, SecurityUtils.getUserName())
                .issuedAt(DateUtil.now())
                .signWith(Keys.hmacShaKeyFor(CommonConstant.Jwt.SECRET.getBytes()))
                .compact();
    }

    private String getDomain(SerHosts host) {
        String domain = "http://" + host.getInternalServerIp() + StrPool.COLON + host.getServerPort();
        return System.getProperty("debug.nse.url", domain);
    }
}
