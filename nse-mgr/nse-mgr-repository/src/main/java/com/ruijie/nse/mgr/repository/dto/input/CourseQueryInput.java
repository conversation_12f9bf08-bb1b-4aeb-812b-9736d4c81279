package com.ruijie.nse.mgr.repository.dto.input;

import com.ruijie.nse.common.dto.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 课程查询条件输入实体类
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CourseQueryInput extends PageInput {

    /**
     * 课程ID
     */
    private String id;

    /**
     * 搜索关键字
     */
    private String searchKey;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 状态
     */
    private String status;

    /**
     * 最小人数
     */
    private Integer studentCntMin;

    /**
     * 最大人数
     */
    private Integer studentCntMax;

    /**
     * 最小实验数
     */
    private Integer expCntMin;

    /**
     * 最大实验数
     */
    private Integer expCntMax;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 用户ID
     */
    private String userId;
}