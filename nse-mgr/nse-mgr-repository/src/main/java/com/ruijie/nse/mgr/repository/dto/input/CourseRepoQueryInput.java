package com.ruijie.nse.mgr.repository.dto.input;

import com.ruijie.nse.common.dto.PageInput;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class CourseRepoQueryInput extends PageInput {

    private String coursePkg;

    private String expName;

    private String expType;

    private String createdBy;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
}
