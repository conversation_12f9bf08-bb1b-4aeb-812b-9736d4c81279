package com.ruijie.nse.mgr.repository.dto.input;

import com.ruijie.nse.common.dto.PageInput;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 课程查询条件输入实体类
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CourseStudentQueryInput extends PageInput {


    /**
     * 课程ID
     */
    private String courseId;

    /**
     * 账号
     */
    private String account;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 姓名
     */
    private String name;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}