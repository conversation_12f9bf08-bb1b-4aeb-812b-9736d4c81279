package com.ruijie.nse.mgr.repository.dto.input;

import com.ruijie.nse.common.dto.PageInput;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 实验数据 - 数据详情
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExperimentLessonDataInput extends PageInput {

    @NotBlank(message = "作业批次号不能为空")
    private String lessonBatchNo;
    private String account;
    private String studentName;
    private String officialName;
    private String submitStatus;

    // 分数这里要求整数
    private List<Integer> scores;
    private List<LocalDateTime> submitDates;

}
