package com.ruijie.nse.mgr.repository.dto.input;

import com.ruijie.nse.common.dto.PageInput;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class UserQueryInput extends PageInput {

    private String account;

    private String name;

    private String mobilePhone;

    private String userType;

    private String userIds;

    private String classes;

    private String createdBy;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date validStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date validEndDate;

    private String storageSpace;
}
