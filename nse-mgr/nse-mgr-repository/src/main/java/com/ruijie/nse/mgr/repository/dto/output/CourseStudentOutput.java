package com.ruijie.nse.mgr.repository.dto.output;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 课程学生输出
 */
@Data
public class CourseStudentOutput {

    private String id;
    private String studentId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 学号
     */
    private String account;

    /**
     * 班级
     */
    private String className;

    /**
     * 添加人
     */
    private String createdBy;

    /**
     * 添加时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

}