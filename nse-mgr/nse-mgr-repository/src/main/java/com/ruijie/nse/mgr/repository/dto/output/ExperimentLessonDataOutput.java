package com.ruijie.nse.mgr.repository.dto.output;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;


/**
 * 实验数据 - 数据详情
 */
@Data
public class ExperimentLessonDataOutput {

    private String lessonId;
    private String expId;
    private String account;
    private String studentName;
    private String officialName;
    private String submitStatus;

    // 分数这里要求整数
    private Integer score;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitDate;

    private String expResultFilename;
    private String expResultPath;

    private String lessonType;

}
