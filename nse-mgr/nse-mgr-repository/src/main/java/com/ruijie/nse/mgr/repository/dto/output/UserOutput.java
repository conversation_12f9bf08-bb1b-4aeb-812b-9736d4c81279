package com.ruijie.nse.mgr.repository.dto.output;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruijie.nse.mgr.repository.entity.enums.UserStatus;
import com.ruijie.nse.mgr.repository.entity.enums.UserType;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018/8/29.
 */
@Data
public class UserOutput {

    /**
     * 主键
     */
    private String id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 登录i账号
     */
    private String account;

    /**
     * 密码, 为hash过的
     */
    private String password;


    /**
     * 邮箱
     */
    private String email;


    /**
     * 手机
     */
    private String mobilePhone;


    /**
     * 用户状态
     */
    private UserStatus status;


    /**
     * 头像
     */
    private String avatar;


    /**
     * 用户类型
     */
    private UserType userType;


    /**
     * 用户类型
     */
    private String userTypeText;

    /**
     * 登录时间
     */
    private Date loginDate;

    /**
     * 有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date validDate;

    /**
     * 创建者
     */
    protected String createdBy;

    /**
     * 创建日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected Date createdDate;

    private String classes;

    private String storageSpace;

    public void setUserType(UserType userType) {
        if (userType != null) {
            this.userType = userType;
            this.userTypeText = userType.getDescription();
        }
    }
}
