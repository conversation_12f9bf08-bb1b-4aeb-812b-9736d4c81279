package com.ruijie.nse.mgr.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruijie.nse.common.entity.DataEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 课程表
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("crs_course")
public class Course extends DataEntity {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 班级ID
     */
    private String classId;

    /**
     * 状态
     */
    private String status;

    /**
     * 学生数量
     */
    private Integer studentCnt;

    /**
     * 实验数量
     */
    private Integer expCnt;
}