package com.ruijie.nse.mgr.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruijie.nse.common.entity.DataEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 课程库表
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("crs_course_repo")
public class CourseRepo extends DataEntity {

    /**
     * 课程包
     */
    private String coursePkg;

    /**
     * 实验名称
     */
    private String expName;

    /**
     * 实验手册文件名称
     */
    private String expManualName;

    /**
     * 实验工程文件名称
     */
    private String expProjectName;

    /**
     * 实验手册路径
     */
    private String expManualPath;

    /**
     * 实验工程文件路径
     */
    private String expProjectPath;

    /**
     * 实验类型
     */
    private String expType;

}
