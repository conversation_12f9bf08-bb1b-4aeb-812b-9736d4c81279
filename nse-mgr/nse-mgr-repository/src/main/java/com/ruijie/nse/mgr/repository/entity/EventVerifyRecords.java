package com.ruijie.nse.mgr.repository.entity;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruijie.nse.common.entity.BaseEntity;
import com.ruijie.nse.mgr.repository.entity.enums.EventTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("evt_verify_records")
public class EventVerifyRecords extends BaseEntity {

    @EnumValue
    private EventTypeEnum evt;

    private String evtDetails;
    private String remark;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdDate;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime modifiedDate;

    private String evtLevel;
    private String evtMessage;
}
