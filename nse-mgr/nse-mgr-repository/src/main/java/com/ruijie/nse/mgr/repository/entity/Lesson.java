package com.ruijie.nse.mgr.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.ruijie.nse.common.entity.DataEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 实验课表
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName(value = "crs_lesson", autoResultMap = true)
public class Lesson extends DataEntity {

    /**
     * 学生ID
     */
    private String studentId;

    /**
     * 教师ID
     */
    private String teacherId;

    /**
     * 课程类型
     */
    private String lessonType;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 提交状态
     */
    private String submitStatus;

    /**
     * 提交时间
     */
    private LocalDateTime submitDate;

    /**
     * 分数
     */
    private BigDecimal score;

    /**
     * 实验结果路径
     */
    private String expResultPath;

    /**
     * 实验结果文件名
     */
    private String expResultFilename;

    /**
     * 实验名称
     */
    private String expName;

    /**
     * 课程资源ID
     */
    private String courseRepoId;

    /**
     * 课程库数据快照
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private CourseRepo courseRepoSnapshot;
}