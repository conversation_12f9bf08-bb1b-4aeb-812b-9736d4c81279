package com.ruijie.nse.mgr.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruijie.nse.common.entity.DataEntity;
import lombok.*;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;


/**
 * license授权记录
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("lic_activation_info")
public class LicenseActivationInfo extends DataEntity {

    /**
     * 授权产品信息
     */
    private String productInfo;
    /**
     * 授权码
     */
    private String activationCode;
    /**
     * 注销码
     */
    private String cancelCode;
    /**
     * 终端许可数量（管理员）
     */
    private Integer permitMgrCnt;
    /**
     * 终端许可数量（普通用户）
     */
    private Integer permitUserCnt;
    /**
     * 有效期类型，永久、1年、2年、3年
     */
    private String validType;
    /**
     * 有效期开始日期
     */
    private LocalDateTime validFrom;
    /**
     * 有效期截止日期
     */
    private LocalDateTime validTo;
    /**
     * 激活时间
     */
    private LocalDateTime activationTime;
    /**
     * 注销时间
     */
    private LocalDateTime cancelTime;
    /**
     * 授权状态，已授权，已过期，已注销
     */
    private String status;

    /**
     * 数据来源：初次导入、定时校验
     * 如果数据一致则不插入
     */
    private String source;

    private String machineCode;
    private String signature;

}
