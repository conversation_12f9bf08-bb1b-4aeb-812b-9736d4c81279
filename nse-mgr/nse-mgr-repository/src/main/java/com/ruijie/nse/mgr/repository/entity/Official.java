package com.ruijie.nse.mgr.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruijie.nse.common.entity.DataEntity;
import com.ruijie.nse.mgr.repository.entity.enums.OfficialType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 组织机构信息表
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("sys_official")
public class Official extends DataEntity {

    /**
     * 名称
     */
    private String name;

    /**
     * 编码
     */
    private String code;

    /**
     * 类型，可选值：DEPARTMENT, GRADE, CLASS  (专业-DEPARTMENT, 年级-GRADE, 班级-CLASS)
     */
    private OfficialType type;

}
