package com.ruijie.nse.mgr.repository.entity;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruijie.nse.common.entity.BaseEntity;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * python服务连接客户端信息
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "ser_clients", autoResultMap = true)
public class SerClients extends BaseEntity {


    private String clientIp;
    private String hostId;
    private String userId;
    private LocalDateTime connectTime;


    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    protected String createdBy;

    /**
     * 创建日期
     */
    @TableField(fill = FieldFill.INSERT)
    protected Date createdDate;
}
