package com.ruijie.nse.mgr.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruijie.nse.common.entity.DataEntity;
import com.ruijie.nse.mgr.repository.entity.enums.UserStatus;
import com.ruijie.nse.mgr.repository.entity.enums.UserType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-08-15
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class User extends DataEntity {

    /**
     * 姓名
     */
    private String name;

    /**
     * 登录i账号
     */
    private String account;

    /**
     * 密码, 为hash过的
     */
    private String password;


    /**
     * 盐，用于给密码加盐后再进行hash
     */
    private String salt;


    /**
     * 邮箱
     */
    private String email;


    /**
     * 手机
     */
    private String mobilePhone;


    /**
     * 用户状态
     */
    private UserStatus status;


    /**
     * 头像
     */
    private String avatar;


    /**
     * 用户类型
     */
    private UserType userType;

    /**
     * 有效期
     */
    private Date validDate;

    /**
     * 是否默认密码
     */
    @TableField("is_default_pwd")
    private Boolean isDefaultPwd;

    /**
     * 登录IP
     */
    @TableField("login_ip")
    private String loginIp;

    /**
     * 登录时间
     */
    @TableField("login_date")
    private LocalDateTime loginDate;


    @TableField(exist = false)
    private List<Role> roleList;

}
