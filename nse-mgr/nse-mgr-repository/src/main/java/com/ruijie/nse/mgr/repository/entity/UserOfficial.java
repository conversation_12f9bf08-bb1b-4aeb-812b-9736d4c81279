package com.ruijie.nse.mgr.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 用户-班级信息关联表
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@TableName("sys_user_official")
public class UserOfficial {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 官方信息ID
     */
    private String officialId;
}
