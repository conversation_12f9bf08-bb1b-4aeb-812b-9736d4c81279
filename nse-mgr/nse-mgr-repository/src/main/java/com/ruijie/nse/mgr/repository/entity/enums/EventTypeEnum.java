package com.ruijie.nse.mgr.repository.entity.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EventTypeEnum implements IEnum<String> {

    // License相关事件
    EVT_LIC_FILE_ERROR("lic文件错误", "ERROR"),
    EVT_LIC_CONTEXT_ERROR("lic上下文内容错误", "ERROR"),
    EVT_LIC_VERIFY_FAILED("lic校验失败", "ERROR"),
    EVT_LIC_EXPIRED("lic已过期", "ERROR"),
    EVT_LIC_CANCEL("lic已注销", "ERROR"),

    // 系统事件
    EVT_THREAT_DETECTED("系统安全检查", "WARNING"),
    EVT_SYSTEM_UP("系统启动", "INFO"),
    EVT_SYSTEM_DOWN("系统关闭", "INFO"),

    // 用户会话事件
    EVT_USER_LOGIN("用户登录", "INFO"),
    EVT_USER_LOGOUT("用户登出", "INFO"),
    EVT_USER_SESSION_EXPIRED("用户会话过期", "WARNING"),
    EVT_USER_FORCE_LOGOUT("用户强制登出", "WARNING"),

    // Python3服务器资源事件
    EVT_PY3_SERVER_START("Python3服务器启动", "INFO"),
    EVT_PY3_SERVER_STOP("Python3服务器停止", "INFO"),
    EVT_PY3_SERVER_RELEASE("Python3服务器资源释放", "INFO"),
    EVT_PY3_SERVER_ERROR("Python3服务器错误", "ERROR"),
    ;

    private final String desc;
    private final String level;

    @Override
    public String getValue() {
        return this.name();
    }
}
