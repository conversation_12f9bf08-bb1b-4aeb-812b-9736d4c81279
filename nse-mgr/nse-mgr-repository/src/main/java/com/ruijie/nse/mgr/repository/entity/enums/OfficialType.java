package com.ruijie.nse.mgr.repository.entity.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

/**
 * 组织机构类型枚举
 */
@Getter
public enum OfficialType implements IEnum<String> {

    DEPARTMENT("DEPARTMENT", "专业"),
    GRADE("GRADE","年级"),
    CLASS("CLASS","班级");

    @EnumValue
    private final String value;
    private final String name;

    OfficialType(String value, String name) {
        this.value = value;
        this.name = name;
    }

}
