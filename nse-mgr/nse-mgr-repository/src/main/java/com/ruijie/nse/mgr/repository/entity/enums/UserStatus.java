package com.ruijie.nse.mgr.repository.entity.enums;


import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2018-08-30
 */
@Getter
public enum UserStatus implements IEnum<Integer> {
    // 账号可以正常使用
    OK(1, "正常"),

    // 账号未激活，需要通过邮件或短信等方式激活
    INACTIVE(2, "未激活"),

    //由于触发了某些限制，被系统自动给锁定了。比如密码试错过多，不断尝试访问非授权资源
    LOCKED(3, "锁定"),

    // 被管理员主动地停止使用
    DISABLED(4, "停用");

    private final Integer value;
    private final String description;

    UserStatus(final Integer value, final String description) {
        this.value = value;
        this.description = description;
    }

}
