package com.ruijie.nse.mgr.repository.entity.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.Getter;

/**
 * 用户类型枚举
 */
@Getter
public enum UserType implements IEnum<String> {

    UNKNOWN("UNKNOWN", "未知"),
    TEACHER("TEACHER", "老师"),
    STUDENT("STUDENT","学生");

    private final String value;
    private final String description;

    UserType(String value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public String getValue() {
        return value;
    }
}
