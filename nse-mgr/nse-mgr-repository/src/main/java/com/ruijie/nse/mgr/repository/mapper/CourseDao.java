package com.ruijie.nse.mgr.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruijie.nse.mgr.repository.dto.input.CourseQueryInput;
import com.ruijie.nse.mgr.repository.dto.input.CourseStudentQueryInput;
import com.ruijie.nse.mgr.repository.dto.output.CourseStudentOutput;
import com.ruijie.nse.mgr.repository.entity.Course;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 课程表 Mapper 接口
 */
public interface CourseDao extends BaseMapper<Course> {

    /**
     * 分页查询课程
     *
     * @param page       分页对象
     * @param courseQueryInput 查询条件
     * @return 分页结果
     */
    Page<Course> findPage(Page<Course> page, @Param("queryInput") CourseQueryInput courseQueryInput);

    /**
     * 分页查询课程学生
     *
     * @param page       分页对象
     * @param query 查询条件
     * @return 分页结果
     */
    Page<CourseStudentOutput> findCourseStudentByPage(Page<CourseStudentOutput> objectPage, @Param("query") CourseStudentQueryInput query);


    /**
     * 批量删除指定用户创建的课程
     *
     * @param userIds 课程ID列表
     * @return 删除结果
     */
    @Delete("<script>delete from course where user_id in <foreach item='item' index='index' collection='userIds' open='(' separator=',' close=')'>#{item}</foreach></script>")
    int deleteByUserIds(List<String> userIds);
}