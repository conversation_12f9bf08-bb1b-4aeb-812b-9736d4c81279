package com.ruijie.nse.mgr.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruijie.nse.mgr.repository.entity.CourseLesson;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

/**
 * 课程实验关联表 Mapper 接口
 */
public interface CourseLessonDao extends BaseMapper<CourseLesson> {

    @Delete("<script>delete from crs_course_lesson where lesson_id in <foreach item='item' index='index' collection='lessonIds' open='(' separator=',' close=')'>#{item}</foreach></script>")
    void deleteByLessonIds(List<String> lessonIds);
}