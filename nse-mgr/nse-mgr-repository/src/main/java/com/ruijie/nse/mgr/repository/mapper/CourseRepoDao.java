package com.ruijie.nse.mgr.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruijie.nse.mgr.repository.dto.input.CourseRepoQueryInput;
import com.ruijie.nse.mgr.repository.entity.CourseRepo;
import org.apache.ibatis.annotations.Param;

/**
 * 课程库表 Mapper 接口
 */
public interface CourseRepoDao extends BaseMapper<CourseRepo> {

    Page<CourseRepo> findPage(Page<CourseRepo> page, @Param("queryInput") CourseRepoQueryInput queryInput);
}
