package com.ruijie.nse.mgr.repository.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.github.yulichang.base.MPJBaseMapper;
import com.ruijie.nse.mgr.repository.entity.Experiment;
import com.ruijie.nse.mgr.repository.entity.SerHosts;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

public interface ExperimentDao extends MPJBaseMapper<Experiment> {


    @Delete("DELETE FROM exp_experiment ${ew.customSqlSegment}")
    void deletePhysically(@Param(Constants.WRAPPER) LambdaQueryWrapper<Experiment> wrapper);

}
