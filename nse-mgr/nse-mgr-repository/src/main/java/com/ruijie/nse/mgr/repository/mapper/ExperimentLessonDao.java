package com.ruijie.nse.mgr.repository.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.github.yulichang.base.MPJBaseMapper;
import com.ruijie.nse.mgr.repository.entity.ExperimentLesson;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

public interface ExperimentLessonDao extends MPJBaseMapper<ExperimentLesson> {


    @Delete("DELETE FROM exp_experiment_lesson ${ew.customSqlSegment}")
    void deletePhysically(@Param(Constants.WRAPPER) LambdaQueryWrapper<ExperimentLesson> wrapper);
}
