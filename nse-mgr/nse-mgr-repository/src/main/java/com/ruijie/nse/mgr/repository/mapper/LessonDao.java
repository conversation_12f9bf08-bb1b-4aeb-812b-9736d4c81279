package com.ruijie.nse.mgr.repository.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseMapper;
import com.ruijie.nse.mgr.repository.dto.input.ExperimentLessonDataInput;
import com.ruijie.nse.mgr.repository.dto.output.ExperimentLessonDataOutput;
import com.ruijie.nse.mgr.repository.entity.Lesson;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 实验课表 Mapper 接口
 */
public interface LessonDao extends MPJBaseMapper<Lesson> {

    @Delete("<script>" +
            "DELETE FROM crs_lesson WHERE id IN " +
            "<foreach collection='ids' item='id' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</script>")
    void deleteLessonByIds(@Param("ids") List<String> ids);

    Page<ExperimentLessonDataOutput> listLessonData(Page<ExperimentLessonDataOutput> pageParam, @Param("req") ExperimentLessonDataInput req, @Param("userId") String userId);


    List<ExperimentLessonDataOutput> listLessonData(@Param("req") ExperimentLessonDataInput req, @Param("userId") String userId);

}