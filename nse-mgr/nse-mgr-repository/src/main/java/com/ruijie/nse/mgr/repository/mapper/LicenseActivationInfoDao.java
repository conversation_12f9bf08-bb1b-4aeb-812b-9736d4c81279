package com.ruijie.nse.mgr.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruijie.nse.mgr.repository.entity.LicenseActivationInfo;

/**
 * License激活信息数据访问接口
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface LicenseActivationInfoDao extends BaseMapper<LicenseActivationInfo> {
    
    /**
     * 查询最新的激活信息
     * 
     * @return 最新的激活信息
     */
    LicenseActivationInfo findLatestActivation();
    
    /**
     * 更新所有激活状态的License为已注销状态
     * 
     * @return 更新的记录数
     */
    int updateStatusToCanceled();
    
    /**
     * 更新所有激活状态的License为已撤销状态
     * 
     * @return 更新的记录数
     */
    int updateStatusToRevoked();
    
    /**
     * 更新所有激活状态的License为硬件不匹配状态
     * 
     * @return 更新的记录数
     */
    int updateStatusToHardwareMismatch();
}
