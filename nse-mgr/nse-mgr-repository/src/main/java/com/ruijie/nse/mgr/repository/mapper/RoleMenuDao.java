package com.ruijie.nse.mgr.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruijie.nse.mgr.repository.entity.RoleMenu;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 角色菜单关联表 Mapper 接口
 * </p>
 */
public interface RoleMenuDao extends BaseMapper<RoleMenu> {

    /**
     * 批量新增角色菜单关联
     *
     * @param roleMenuList 角色菜单列表
     * @return 结果
     */
    int batchInsert(List<RoleMenu> roleMenuList);

    /**
     * 通过角色ID删除角色菜单关联
     *
     * @param roleId 角色ID
     * @return 结果
     */
    int deleteByRoleId(String roleId);

    /**
     * 通过菜单ID删除角色菜单关联
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    int deleteByMenuId(String menuId);

    /**
     * 通过角色ID和菜单ID列表批量删除
     *
     * @param roleId  角色ID
     * @param menuIds 菜单ID列表
     * @return 结果
     */
    int deleteByRoleIdAndMenuIds(@Param("roleId") String roleId, @Param("menuIds") List<String> menuIds);

    /**
     * 查询角色拥有的菜单ID列表
     *
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    List<String> selectMenuIdsByRoleId(String roleId);
}
