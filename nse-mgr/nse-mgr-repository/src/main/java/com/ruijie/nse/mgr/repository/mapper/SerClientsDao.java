package com.ruijie.nse.mgr.repository.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.ruijie.nse.mgr.repository.entity.SerClients;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

public interface SerClientsDao extends BaseMapper<SerClients> {


    /**
     * 物理删除
     * @param wrapper
     * @return
     */
    @Delete("DELETE FROM ser_clients ${ew.customSqlSegment}")
    int deletePhysically(@Param(Constants.WRAPPER) LambdaQueryWrapper<SerClients> wrapper);


}
