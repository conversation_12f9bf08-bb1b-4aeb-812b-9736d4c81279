package com.ruijie.nse.mgr.repository.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.ruijie.nse.mgr.repository.entity.SerHosts;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

public interface SerHostsDao extends BaseMapper<SerHosts> {

    /**
     * 物理删除
     *
     * @param wrapper
     */
    @Delete("DELETE FROM ser_hosts ${ew.customSqlSegment}")
    void deletePhysically(@Param(Constants.WRAPPER) LambdaQueryWrapper<SerHosts> wrapper);

}
