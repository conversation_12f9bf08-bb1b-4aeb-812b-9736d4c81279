package com.ruijie.nse.mgr.repository.typehandler;

import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.apache.ibatis.type.TypeHandler;

import java.sql.*;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 数组自动映射list
 * <AUTHOR>
 */
@MappedTypes(List.class)
@MappedJdbcTypes(JdbcType.ARRAY)
public class StringListTypeHandler implements TypeHandler<List<String>> {

    @Override
    public void setParameter(PreparedStatement ps, int i, List<String> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter != null) {
            String[] array = parameter.toArray(new String[0]);
            Array sqlArray = ps.getConnection().createArrayOf("VARCHAR", array);
            ps.setArray(i, sqlArray);
        } else {
            ps.setArray(i, null);
        }
    }

    @Override
    public List<String> getResult(ResultSet rs, String columnName) throws SQLException {
        Array array = rs.getArray(columnName);
        return array == null ? null : convertArrayToList(array);
    }

    @Override
    public List<String> getResult(ResultSet rs, int columnIndex) throws SQLException {
        Array array = rs.getArray(columnIndex);
        return array == null ? null : convertArrayToList(array);
    }

    @Override
    public List<String> getResult(CallableStatement cs, int columnIndex) throws SQLException {
        Array array = cs.getArray(columnIndex);
        return array == null ? null : convertArrayToList(array);
    }

    private List<String> convertArrayToList(Array array) throws SQLException {
        Object[] objectArray = (Object[]) array.getArray();
        return Arrays.stream(objectArray)
                .map(Object::toString)
                .collect(Collectors.toList());
    }
}
