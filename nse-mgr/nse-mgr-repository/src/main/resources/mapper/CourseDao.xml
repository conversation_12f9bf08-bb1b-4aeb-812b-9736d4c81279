<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijie.nse.mgr.repository.mapper.CourseDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id,
        a.user_id,
        a.course_name,
        a.class_name,
        a.class_id,
        a.status,
        a.created_date,
        a.modified_date,
        a.modified_by,
        a.is_deleted,
        a.remark
    </sql>

    <select id="findPage" resultType="com.ruijie.nse.mgr.repository.entity.Course">
        SELECT
            <include refid="Base_Column_List"/>
            ,COALESCE(stu.num, 0) as student_cnt
            ,COALESCE(ex.num, 0) as exp_cnt
            ,su.name as created_by
        FROM crs_course a
        LEFT JOIN sys_user su ON su.id = a.created_by
        LEFT JOIN (
            SELECT course_id, count(cl.id) as num FROM crs_course_lesson ccl
            INNER JOIN crs_lesson cl ON ccl.lesson_id = cl.id
            WHERE cl.is_deleted = 0 AND cl.lesson_type = '上课'
            GROUP BY ccl.course_id
        ) stu ON stu.course_id = a.id
        LEFT JOIN (
            SELECT course_id, COUNT(1) as num, ROW_NUMBER() OVER (PARTITION BY course_id ORDER BY COUNT(1) DESC) as rn
            FROM crs_course_lesson ccl
            INNER JOIN crs_lesson cl ON ccl.lesson_id = cl.id
            WHERE cl.is_deleted = 0 AND cl.lesson_type = '作业'
            GROUP BY course_id, student_id
        ) ex ON ex.course_id = a.id AND ex.rn = 1
        WHERE a.is_deleted = 0
        <if test="queryInput.searchKey != null and queryInput.searchKey != ''">
            AND (a.course_name || '-' || a.class_name) LIKE CONCAT('%', #{queryInput.searchKey}::varchar, '%')
        </if>
        <if test="queryInput.courseName != null and queryInput.courseName != ''">
            AND a.course_name LIKE CONCAT('%', #{queryInput.courseName}::varchar, '%')
        </if>
        <if test="queryInput.className != null and queryInput.className != ''">
            AND a.class_name LIKE CONCAT('%', #{queryInput.className}::varchar, '%')
        </if>
        <if test="queryInput.status != null and queryInput.status != ''">
            AND a.status = #{queryInput.status}::varchar
        </if>
        <if test="queryInput.studentCntMin != null">
            AND a.student_cnt &gt;= #{queryInput.studentCntMin}
        </if>
        <if test="queryInput.studentCntMax != null">
            AND a.student_cnt &lt;= #{queryInput.studentCntMax}
        </if>
        <if test="queryInput.expCntMin != null">
            AND a.exp_cnt &gt;= #{queryInput.expCntMin}
        </if>
        <if test="queryInput.expCntMax != null">
            AND a.exp_cnt &lt;= #{queryInput.expCntMax}
        </if>
        <if test="queryInput.createdBy != null and queryInput.createdBy != ''">
            AND su.name LIKE CONCAT('%', #{queryInput.createdBy}::varchar, '%')
        </if>
        <if test="queryInput.startTime != null">
            AND a.created_date &gt;= #{queryInput.startTime}
        </if>
        <if test="queryInput.endTime != null">
            AND a.created_date &lt;= #{queryInput.endTime}
        </if>
        <if test="queryInput.userId != null and queryInput.userId != ''">
            AND a.user_id = #{queryInput.userId}::varchar
        </if>
        <if test="queryInput.id != null and queryInput.id != ''">
            AND a.id = #{queryInput.id}::varchar
        </if>
        ORDER BY a.created_date DESC
    </select>

    <select id="findCourseStudentByPage"
            resultType="com.ruijie.nse.mgr.repository.dto.output.CourseStudentOutput">
        SELECT
            cl.id,
            cl.student_id,
            su.account,
            su.name,
            so.name as className,
            cb.name as createdBy,
            cl.created_date
        FROM crs_course cc
        INNER JOIN crs_course_lesson ccl ON cc.id = ccl.course_id
        INNER JOIN crs_lesson cl ON cl.id = ccl.lesson_id AND cl.is_deleted = 0 AND cl.lesson_type = '上课'
        INNER JOIN sys_user su ON cl.student_id = su.id AND su.is_deleted = 0
        INNER JOIN sys_user cb ON cl.created_by = cb.id AND cb.is_deleted = 0
        INNER JOIN sys_user_official suo ON suo.user_id = su.id
        INNER JOIN sys_official so ON so.id = suo.official_id AND so.is_deleted = 0
        WHERE cc.is_deleted = 0 and cc.id = #{query.courseId}
        <if test="query.account != null and query.account != ''">
            AND su.account LIKE CONCAT('%', #{query.account}::varchar, '%')
        </if>
        <if test="query.name != null and query.name != ''">
            AND su.name LIKE CONCAT('%', #{query.name}::varchar, '%')
        </if>
        <if test="query.className != null and query.className != ''">
            AND so.name LIKE CONCAT('%', #{query.className}::varchar, '%')
        </if>
        <if test="query.createdBy != null and query.createdBy != ''">
            AND cb.name LIKE CONCAT('%', #{query.createdBy}::varchar, '%')
        </if>
        <if test="query.startTime != null">
            AND cl.created_date &gt;= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND cl.created_date &lt;= #{query.endTime}
        </if>
        ORDER BY cl.created_date DESC
    </select>

</mapper>