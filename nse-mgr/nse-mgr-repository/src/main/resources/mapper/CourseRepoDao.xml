<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijie.nse.mgr.repository.mapper.CourseRepoDao">

    <select id="findPage" resultType="com.ruijie.nse.mgr.repository.entity.CourseRepo">
        SELECT
            a.id,
            a.course_pkg,
            a.exp_name,
            a.exp_type,
            a.exp_manual_name,
            a.exp_project_name,
            a.exp_manual_path,
            a.exp_project_path,
            a.created_date,
            b.name AS createdBy
        FROM crs_course_repo a
        LEFT JOIN sys_user b ON a.created_by = b.id
        <where>
            <if test="queryInput.coursePkg != null and queryInput.coursePkg != ''">
                AND course_pkg LIKE CONCAT('%', #{queryInput.coursePkg}::varchar, '%')
            </if>
            <if test="queryInput.expName != null and queryInput.expName != ''">
                AND exp_name LIKE CONCAT('%', #{queryInput.expName}::varchar, '%')
            </if>
            <if test="queryInput.expType != null and queryInput.expType != ''">
                AND exp_type = #{queryInput.expType}::varchar
            </if>
            <if test="queryInput.createdBy != null and queryInput.createdBy != ''">
                AND b.name LIKE CONCAT('%', #{queryInput.createdBy}::varchar, '%')
            </if>
            <if test="queryInput.startTime != null">
                AND a.created_date &gt;= #{queryInput.startTime}
            </if>
            <if test="queryInput.endTime != null">
                AND a.created_date &lt;= #{queryInput.endTime}
            </if>
        </where>
        ORDER BY created_date DESC
    </select>
</mapper>
