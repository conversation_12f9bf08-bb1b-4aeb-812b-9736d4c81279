<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijie.nse.mgr.repository.mapper.LessonDao">
    
    <select id="listLessonData" resultType="com.ruijie.nse.mgr.repository.dto.output.ExperimentLessonDataOutput">
        select
            cl.id as lessonId,
            eel.exp_id as expId,
            su.account as account,
            su."name" as studentName,
            so."name" as officialName,
            cl.submit_status  as submitStatus,
            cl.score as score,
            cl.submit_date as submitDate,
            cl.exp_result_filename as expResultFilename,
            cl.exp_result_path as expResultPath,
            cl.lesson_type as lessonType
        from crs_lesson cl
        left join exp_experiment_lesson eel on cl.id = eel.lesson_id
        left join sys_user su on cl.student_id =su.id
        left join sys_user_official suo on suo.user_id =su.id
        left join sys_official so on suo.official_id =so.id
        <where>
            cl.lesson_type='作业' and cl.teacher_id = #{userId} and cl.is_deleted = 0
            <if test="req.lessonBatchNo != null and req.lessonBatchNo != ''">
                AND cl.remark = #{req.lessonBatchNo}
            </if>
            <if test="req.account != null and req.account != ''">
                AND su.account like concat('%', #{req.account}::varchar, '%')
            </if>
            <if test="req.officialName != null and req.officialName != ''">
                AND so."name" like concat('%', #{req.officialName}::varchar, '%')
            </if>
            <if test="req.submitStatus != null and req.submitStatus != ''">
                AND cl.submit_status = #{req.submitStatus}
            </if>
            <if test="req.scores != null and req.scores.size() > 0">
                AND cl.score &gt;= #{req.scores[0]}
            </if>
            <if test="req.scores != null and req.scores.size() > 1">
                AND cl.score &lt;= #{req.scores[1]}
            </if>
            <if test="req.submitDates != null and req.submitDates.size() > 0">
                AND cl.submit_date &gt;= #{req.submitDates[0]}
            </if>
            <if test="req.submitDates != null and req.submitDates.size() > 1">
                AND cl.submit_date &lt;= #{req.submitDates[1]}
            </if>
        </where>
    </select>

</mapper>