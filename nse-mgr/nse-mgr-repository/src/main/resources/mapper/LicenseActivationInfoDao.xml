<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijie.nse.mgr.repository.mapper.LicenseActivationInfoDao">

    <select id="findLatestActivation" resultType="com.ruijie.nse.mgr.repository.entity.LicenseActivationInfo">
        select *
        from lic_activation_info
        where is_deleted = 0
        order by created_date desc
        limit 1
    </select>
    
    <update id="updateStatusToCanceled">
        update lic_activation_info
        set status = '已注销', cancel_time = now()
        where is_deleted = 0 and status = '已授权'
    </update>
    
    <update id="updateStatusToRevoked">
        update lic_activation_info
        set status = '已撤销', cancel_time = now()
        where is_deleted = 0 and status = '已授权'
    </update>
    
    <update id="updateStatusToHardwareMismatch">
        update lic_activation_info
        set status = '硬件不匹配', cancel_time = now()
        where is_deleted = 0 and status = '已授权'
    </update>

</mapper>