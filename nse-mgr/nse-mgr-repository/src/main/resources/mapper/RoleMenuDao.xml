<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijie.nse.mgr.repository.mapper.RoleMenuDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
       role_id, menu_id
    </sql>

    <!-- 批量新增角色菜单关联 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO sys_role_menu (role_id, menu_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.roleId}, #{item.menuId})
        </foreach>
    </insert>

    <!-- 通过角色ID删除角色菜单关联 -->
    <delete id="deleteByRoleId" parameterType="string">
        DELETE FROM sys_role_menu WHERE role_id = #{roleId}
    </delete>

    <!-- 通过菜单ID删除角色菜单关联 -->
    <delete id="deleteByMenuId" parameterType="string">
        DELETE FROM sys_role_menu WHERE menu_id = #{menuId}
    </delete>

    <!-- 通过角色ID和菜单ID列表批量删除 -->
    <delete id="deleteByRoleIdAndMenuIds">
        DELETE FROM sys_role_menu
        WHERE role_id = #{roleId}
        AND menu_id IN
        <foreach collection="menuIds" item="menuId" open="(" separator="," close=")">
            #{menuId}
        </foreach>
    </delete>

    <!-- 查询角色拥有的菜单ID列表 -->
    <select id="selectMenuIdsByRoleId" parameterType="string" resultType="string">
        SELECT menu_id
        FROM sys_role_menu
        WHERE role_id = #{roleId}
    </select>

</mapper>
