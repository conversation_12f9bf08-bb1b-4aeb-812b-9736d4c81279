<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijie.nse.mgr.repository.mapper.UserDao">


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id, a.name, a.password, a.salt, a.email, a.mobile_phone, a.status,
        a.avatar, a.user_type, a.valid_date, a.remark,
        a.login_ip, a.login_date,
        a.created_date, a.modified_date, a.modified_by, a.is_deleted,
        a.account, a.is_default_pwd
    </sql>


    <select id="selectUserByRoleId" parameterType="string"
            resultType="com.ruijie.nse.mgr.repository.entity.User">
        select
        <include refid="Base_Column_List" />
        from sys_user a
        left join sys_user_role b ON b.user_id = a.id
        WHERE b.role_id = #{roleId}
    </select>

    <select id="listPermissionsByUserId" resultType="java.lang.String">
        select sm.perm from sys_role_menu srm
        inner join sys_menu sm on sm.id = srm.menu_id
        where role_id in
        ( select role_id from sys_user_role
        where user_id = #{user_id}
        )
        and sm.perm is not null
    </select>

    <select id="findUserRoleByUserId" resultType="java.lang.String">
        SELECT sys_role.name FROM sys_user_role
        LEFT JOIN sys_role ON sys_user_role.role_id=sys_role.id
        WHERE sys_user_role.user_id=#{user_id}

    </select>


    <select id="listRolesByUserId" resultType="java.lang.String">
        SELECT sys_role.name FROM sys_user_role
        LEFT JOIN sys_role ON sys_user_role.role_id=sys_role.id
        WHERE sys_user_role.user_id=#{user_id}
    </select>

    <select id="findPage" resultType="com.ruijie.nse.mgr.repository.dto.output.UserOutput">
        SELECT
            <include refid="Base_Column_List" />
            ,su.name as created_by
            ,so.name as classes
        FROM sys_user a
        LEFT JOIN sys_user su ON su.id = a.created_by
        LEFT JOIN sys_user_official suo ON suo.user_id = a.id
        LEFT JOIN sys_official so ON so.id = suo.official_id AND so.is_deleted = 0
        <where>
            AND a.is_deleted = 0
            AND a.id NOT IN (
                SELECT DISTINCT ur.user_id
                FROM sys_user_role ur
                INNER JOIN sys_role r ON ur.role_id = r.id
                WHERE r.name = 'super_admin'
            )
            <if test="queryInput.account != null and queryInput.account != ''">
                AND a.account LIKE CONCAT('%', #{queryInput.account}::varchar, '%')
            </if>
            <if test="queryInput.name != null and queryInput.name != ''">
                AND a.name LIKE CONCAT('%', #{queryInput.name}::varchar, '%')
            </if>
            <if test="queryInput.mobilePhone != null and queryInput.mobilePhone != ''">
                AND a.mobile_phone LIKE CONCAT('%', #{queryInput.mobilePhone}::varchar, '%')
            </if>
            <if test="queryInput.userType != null and queryInput.userType != ''">
                AND a.user_type = #{queryInput.userType}
            </if>
            <if test="queryInput.userIds != null and queryInput.userIds != ''">
                AND a.id IN
                <foreach collection="queryInput.userIds.split(',')" item="userId" separator="," open="(" close=")">
                    ${userId}::varchar
                </foreach>
            </if>
            <if test="queryInput.classes != null and queryInput.classes != ''">
                AND so.name LIKE CONCAT('%', #{queryInput.classes}::varchar, '%')
                <!--
                AND EXISTS (
                    SELECT name FROM sys_official so
                    INNER JOIN sys_user_official suo ON so.id = suo.official_id
                    WHERE so.is_deleted = 0 AND so.type = 'CLASS'
                    AND so.name LIKE CONCAT('%', #{queryInput.classes}::varchar, '%') AND a.id = suo.user_id
                )
                -->
            </if>
            <if test="queryInput.createdBy != null and queryInput.createdBy != ''">
                AND su.name LIKE CONCAT('%', #{queryInput.createdBy}::varchar, '%')
            </if>
            <if test="queryInput.startTime != null">
                AND a.created_date &gt;= #{queryInput.startTime}
            </if>
            <if test="queryInput.endTime != null">
                AND a.created_date &lt;= #{queryInput.endTime}
            </if>
            <if test="queryInput.validStartDate != null">
                AND a.valid_date &gt;= #{queryInput.validStartDate}
            </if>
            <if test="queryInput.validEndDate != null">
                AND a.valid_date &lt;= #{queryInput.validEndDate}
            </if>
        </where>
        ORDER BY a.account
    </select>


    <delete id="deleteRolesOfUser">
        delete from
        sys_user_role
        where user_id = #{userId}
    </delete>

    <insert id="insertRolesToUser">
        insert into sys_user_role(user_id, role_id ) values
        <foreach collection="roles" item="roleId" separator=",">
            (#{userId}, #{roleId})
        </foreach>
    </insert>

</mapper>
