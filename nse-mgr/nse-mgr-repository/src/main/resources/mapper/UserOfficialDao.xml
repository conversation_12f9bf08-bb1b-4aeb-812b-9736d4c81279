<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijie.nse.mgr.repository.mapper.UserOfficialDao">

    <select id="selectUserOfficialByUserId"
            resultType="com.ruijie.nse.mgr.repository.dto.output.UserOfficialOutput">
        select
            suo.user_id,
            suo.official_id,
            so.name as official_name,
            so.code as official_code,
            so.type as official_type
        from sys_user_official suo
        left join sys_official so on suo.official_id = so.id
        WHERE so.is_deleted = 0 and
        so.type = 'CLASS'
        <choose>
            <when test="userIds != null and userIds.size() > 0">
                AND suo.user_id IN
                <foreach item="userId" open="(" close=")" separator="," collection="userIds" >
                    #{userId}
                </foreach>
            </when>
            <otherwise>
                AND 1 = 0
            </otherwise>
        </choose>
        ORDER BY
        so.created_date
    </select>
</mapper>
