<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruijie.nse.mgr.repository.mapper.UserRoleDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, role_id
    </sql>

    <!-- 批量新增用户角色关联 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO sys_user_role (user_id, role_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.roleId})
        </foreach>
    </insert>

    <!-- 通过用户ID删除用户角色关联 -->
    <delete id="deleteByUserId" parameterType="string">
        DELETE FROM sys_user_role WHERE user_id = #{userId}
    </delete>

    <!-- 通过角色ID删除用户角色关联 -->
    <delete id="deleteByRoleId" parameterType="string">
        DELETE FROM sys_user_role WHERE role_id = #{roleId}
    </delete>

    <!-- 通过用户ID和角色ID列表批量删除 -->
    <delete id="deleteByUserIdAndRoleIds">
        DELETE FROM sys_user_role
        WHERE user_id = #{userId}
        AND role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>

    <!-- 查询用户拥有的角色ID列表 -->
    <select id="selectRoleIdsByUserId" parameterType="string" resultType="string">
        SELECT role_id
        FROM sys_user_role
        WHERE user_id = #{userId}
    </select>

</mapper>
