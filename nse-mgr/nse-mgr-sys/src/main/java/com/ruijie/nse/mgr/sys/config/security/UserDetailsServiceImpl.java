package com.ruijie.nse.mgr.sys.config.security;

import com.ruijie.nse.common.config.security.UserPrincipal;
import com.ruijie.nse.mgr.repository.entity.User;
import com.ruijie.nse.mgr.repository.entity.enums.UserType;
import com.ruijie.nse.mgr.sys.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户详情服务实现
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserService userService;

    @Override
    public UserPrincipal loadUserByUsername(String account) throws UsernameNotFoundException {
        log.debug("加载用户信息: {}", account);
        
        // 根据用户名查询用户信息
        User user = userService.lambdaQuery().eq(User::getAccount, account).one();
        if (user == null) {
            log.warn("用户不存在: {}", account);
            throw new UsernameNotFoundException("用户不存在: " + account);
        }

        // 获取用户权限
        List<String> permissions = userService.listPermissionsByUserId(user.getId());

        // 构建UserPrincipal
        UserPrincipal userPrincipal = new UserPrincipal();
        userPrincipal.setUserId(user.getId());
        userPrincipal.setUserType(user.getUserType() != null ? user.getUserType().getValue() : UserType.UNKNOWN.getValue());
        userPrincipal.setAccount(user.getAccount());
        userPrincipal.setUsername(user.getAccount());
        userPrincipal.setPassword(user.getPassword());
        userPrincipal.setEmail(user.getEmail());
        userPrincipal.setPermissions(permissions);
        userPrincipal.setValidDate(user.getValidDate());
        
        // 设置角色信息（从权限中提取角色）
        List<String> roles = userService.listRolesByUserId(user.getId());
        userPrincipal.setRoles(roles);
        log.debug("用户信息加载完成: {}, 权限数量: {}", account, permissions.size());
        return userPrincipal;
    }

    /**
     * 根据用户ID加载用户信息
     *
     * @param userId 用户ID
     * @return 用户详情
     */
    public UserDetails loadUserByUserId(Long userId) {
        log.debug("根据用户ID加载用户信息: {}", userId);
        
        User user = userService.getById(userId);
        if (user == null) {
            log.warn("用户不存在: {}", userId);
            throw new UsernameNotFoundException("用户不存在: " + userId);
        }
        
        return loadUserByUsername(user.getAccount());
    }
}