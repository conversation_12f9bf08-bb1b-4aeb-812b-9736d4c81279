package com.ruijie.nse.mgr.sys.controller;

import com.ruijie.nse.common.dto.R;
import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.license.service.LicenseActivationInfoService;
import com.ruijie.nse.mgr.py3server.dto.ProcessDto;
import com.ruijie.nse.mgr.py3server.launcher.Python3ServerLauncher;
import com.ruijie.nse.mgr.py3server.service.ProcessService;
import com.ruijie.nse.mgr.repository.entity.LicenseActivationInfo;
import com.ruijie.nse.mgr.repository.entity.SerHosts;
import com.ruijie.nse.mgr.repository.entity.enums.UserType;
import com.ruijie.nse.mgr.sys.dto.output.OnlineUserOutput;
import com.ruijie.nse.mgr.sys.dto.output.SummaryOutput;
import com.ruijie.nse.mgr.sys.service.OnlineUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.collection.CollUtil;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;
import oshi.hardware.GlobalMemory;
import oshi.software.os.OSFileStore;
import oshi.software.os.OperatingSystem;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/env")
@RequiredArgsConstructor
public class EnvironmentController {

    private final Python3ServerLauncher python3ServerLauncher;
    private final ProcessService processService;
    private final LicenseActivationInfoService licenseActivationInfoService;
    private final OnlineUserService onlineUserService;


    /**
     *
     * @return
     */
    @GetMapping("summary")
    public R<SummaryOutput> summary() {
        LicenseActivationInfo currentLicense = licenseActivationInfoService.getCurrentLicense();
        SummaryOutput summaryOutput = new SummaryOutput();
        summaryOutput.setPeriMgrCnt(currentLicense.getPermitMgrCnt());
        summaryOutput.setPeriUserCnt(currentLicense.getPermitUserCnt());

        // 当前登录用户
        List<OnlineUserOutput> onlineUsers = onlineUserService.getOnlineUsers();
        if(CollUtil.isNotEmpty(onlineUsers)) {
            // 根据userType分组，统计数量
            Map<UserType, Long> countByUserType = onlineUsers.stream()
                    .collect(Collectors.groupingBy(
                            OnlineUserOutput::getUserType,
                            Collectors.counting()
                    ));
            summaryOutput.setLoginMgrCnt(countByUserType.getOrDefault(UserType.TEACHER, 0L).intValue());
            summaryOutput.setLoginUserCnt(countByUserType.getOrDefault(UserType.STUDENT, 0L).intValue());
        }

        return R.success(summaryOutput);
    }


    /**
     * 获取服务器资源预览
     * @return
     */
    @GetMapping("get/server")
    public R<ProcessDto> get_server() {
        SystemInfo systemInfo = new SystemInfo();
        OperatingSystem os = systemInfo.getOperatingSystem();
        CentralProcessor processor = systemInfo.getHardware().getProcessor();
        GlobalMemory memory = systemInfo.getHardware().getMemory();

        ProcessDto processDto = new ProcessDto();

        // 获取CPU使用率(0-1之间)
        long[] prevTicks = processor.getSystemCpuLoadTicks();
        try {
            TimeUnit.MILLISECONDS.sleep(1000); // 等待1秒获取CPU使用率
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return R.error("获取CPU信息被中断");
        }
        long[] ticks = processor.getSystemCpuLoadTicks();

        long user = ticks[CentralProcessor.TickType.USER.getIndex()] - prevTicks[CentralProcessor.TickType.USER.getIndex()];
        long nice = ticks[CentralProcessor.TickType.NICE.getIndex()] - prevTicks[CentralProcessor.TickType.NICE.getIndex()];
        long sys = ticks[CentralProcessor.TickType.SYSTEM.getIndex()] - prevTicks[CentralProcessor.TickType.SYSTEM.getIndex()];
        long idle = ticks[CentralProcessor.TickType.IDLE.getIndex()] - prevTicks[CentralProcessor.TickType.IDLE.getIndex()];
        long iowait = ticks[CentralProcessor.TickType.IOWAIT.getIndex()] - prevTicks[CentralProcessor.TickType.IOWAIT.getIndex()];
        long irq = ticks[CentralProcessor.TickType.IRQ.getIndex()] - prevTicks[CentralProcessor.TickType.IRQ.getIndex()];
        long softirq = ticks[CentralProcessor.TickType.SOFTIRQ.getIndex()] - prevTicks[CentralProcessor.TickType.SOFTIRQ.getIndex()];
        long steal = ticks[CentralProcessor.TickType.STEAL.getIndex()] - prevTicks[CentralProcessor.TickType.STEAL.getIndex()];

        long totalCpu = user + nice + sys + idle + iowait + irq + softirq + steal;

        if (totalCpu > 0) {
            double cpuUsage = (double) (totalCpu - idle) / totalCpu;
            processDto.setCpuUsage(cpuUsage);
        }

        // 获取内存信息
        long memoryTotal = memory.getTotal(); // 单位: bytes
        long memoryAvailable = memory.getAvailable(); // 单位: bytes
        long memoryUsage = memoryTotal - memoryAvailable;

        processDto.setMemoryTotal(memoryTotal);
        processDto.setMemoryUsage(memoryUsage);

        // 获取磁盘信息(取所有磁盘总和)
        long diskTotal = 0L;
        long diskUsage = 0L;

        for (OSFileStore fs : os.getFileSystem().getFileStores()) {
            diskTotal += fs.getTotalSpace();
            diskUsage += (fs.getTotalSpace() - fs.getFreeSpace());
        }

        processDto.setDiskTotal(diskTotal);
        processDto.setDiskUsage(diskUsage);

        return R.success(processDto);
    }

    /**
     * 获取当前资源概览
     * @return
     * @throws Exception
     */
    @GetMapping("get")
    public R<ProcessDto> get() throws Exception {
        String userId = SecurityUtils.getUserId();
        SerHosts serHost = python3ServerLauncher.getServerInfo(userId);
        if(serHost == null || !processService.isValidPid(serHost.getPid())) {
            log.info("当前NSE Server未启动，等待启动......");
            serHost = python3ServerLauncher.launcher();
        }
        log.info("当前NSE Server信息: {}", serHost);

        // 根据pid获取当前的资源情况
        ProcessDto processDto = processService.getProcessInfo(serHost.getPid());
        log.info("当前NSE Server资源情况: {}", processDto);
        return R.success(processDto);
    }

}
