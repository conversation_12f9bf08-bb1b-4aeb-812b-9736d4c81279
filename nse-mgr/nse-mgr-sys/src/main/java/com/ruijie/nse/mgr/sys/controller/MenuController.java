package com.ruijie.nse.mgr.sys.controller;

import com.ruijie.nse.mgr.sys.dto.input.MenuFormInput;
import com.ruijie.nse.mgr.sys.dto.output.MenuOutput;
import com.ruijie.nse.mgr.sys.dto.output.OptionOutput;
import com.ruijie.nse.mgr.sys.dto.output.RouteOutput;
import com.ruijie.nse.mgr.sys.service.MenuService;
import com.ruijie.nse.common.dto.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/menu")
public class MenuController {

    @Autowired
    private MenuService menuService;

    /**
     * 获取当前用户路由数据
     * @return
     */
    @GetMapping("routes")
    public R<List<RouteOutput>> routes() {
        return R.success(menuService.routes());
    }

    @GetMapping
    public R<List<MenuOutput>> listMenus(String keywords) {
        List<MenuOutput> menuList = menuService.listMenus(keywords);
        return R.success(menuList);
    }

    @GetMapping("/options")
    public R<List<OptionOutput<String>>> listMenuOptions(
            @RequestParam(required = false, defaultValue = "false") boolean onlyParent
    ) {
        List<OptionOutput<String>> menus = menuService.listMenuOptions(onlyParent);
        return R.success(menus);
    }

    @GetMapping("/{id}/form")
    public R<MenuFormInput> getMenuForm(@PathVariable String id) {
        MenuFormInput menu = menuService.getMenuForm(id);
        return R.success(menu);
    }

    @PostMapping
    public R<?> addMenu(@RequestBody MenuFormInput menuFormInput) {
        boolean result = menuService.saveMenu(menuFormInput);
        return R.success(result);
    }

    @PutMapping(value = "/{id}")
    public R<?> updateMenu(
            @RequestBody MenuFormInput menuFormInput
    ) {
        boolean result = menuService.saveMenu(menuFormInput);
        return R.success(result);
    }

    @DeleteMapping("/{id}")
    public R<?> deleteMenu(@PathVariable("id") String id) {
        boolean result = menuService.deleteMenu(id);
        return R.success(result);
    }

    @PatchMapping("/{menuId}")
    public R<?> updateMenuVisible(@PathVariable String menuId, Integer visible) {
        boolean result = menuService.updateMenuVisible(menuId, visible);
        return R.success(result);
    }

}
