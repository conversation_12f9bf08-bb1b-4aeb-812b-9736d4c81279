package com.ruijie.nse.mgr.sys.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruijie.nse.common.dto.PageInput;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.dto.R;
import com.ruijie.nse.mgr.repository.entity.Role;
import com.ruijie.nse.mgr.sys.service.RoleService;
import org.dromara.hutool.core.text.StrValidator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/api/role")
public class RoleController {

    @Autowired
    private RoleService roleService;

    /**
     * 角色列表
     *
     * @param pageInput
     * @param keywords
     * @return
     */
    @GetMapping("page")
    public R<PageOutput<Role>> page(PageInput pageInput, String keywords) {
        Page<Role> page = roleService.page(new Page<>(pageInput.getPageNumber(), pageInput.getPageSize()),
                Wrappers.lambdaQuery(Role.class)
                        .like(StrValidator.isNotBlank(keywords), Role::getName, keywords)
                        .orderByAsc(Role::getCreatedDate));
        return R.success(new PageOutput<>(page.getTotal(), page.getRecords()));
    }

    /**
     * 角色菜单列表
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}/menuIds")
    public R<List<String>> getMenuIds(@PathVariable String id) {
        return R.success(roleService.findMenuIdsByRoleId(id));
    }

    /**
     * 角色菜单保存
     *
     * @param id
     * @param menuIds
     * @return
     */
    @PutMapping("/{id}/menus")
    public R<?> assignMenu(@PathVariable String id, @RequestBody List<String> menuIds) {
        roleService.assignMenu(id, menuIds);
        return R.success();
    }

    /**
     * 角色信息
     *
     * @param id
     * @return
     */
    @GetMapping("/{id}")
    public R<Role> get(@PathVariable String id) {
        return R.success(roleService.getById(id));
    }

    /**
     * 新增角色
     *
     * @param role
     */
    @PostMapping
    public R<Boolean> create(@RequestBody Role role) {
        String name = role.getName();
        long count = roleService.count(Wrappers.lambdaQuery(Role.class).eq(Role::getName, name));
        if (count > 0) {
            return R.error("角色名称已存在");
        }
        return R.success(roleService.save(role));
    }

    /**
     * 修改角色
     *
     * @param role
     */
    @PutMapping("/{id}")
    public R<Boolean> update(@PathVariable String id, @RequestBody Role role) {
        role.setId(id);
        return R.success(roleService.updateById(role));
    }

    /**
     * 删除角色
     * @param ids
     */
    @DeleteMapping("/{ids}")
    public R<?> delete(@PathVariable String ids) {
        if (StrValidator.isNotBlank(ids)) {
            String[] idArray = ids.split(",");
            roleService.removeByIds(Arrays.asList(idArray));
        }
        return R.success();
    }


    @GetMapping
    public List<Role> list() {
        return roleService.list();
    }

    @GetMapping("/menu/{roleId}")
    public List<String> getRoleMenus(@PathVariable String roleId) {
        return roleService.findMenuIdsByRoleId(roleId);
    }


    @PostMapping("/menu/{roleId}")
    public void saveRoleMenus(@PathVariable String roleId, @RequestBody List<String> menuIds) {
        roleService.assignMenu(roleId, menuIds);
    }


    @GetMapping("/user/{roleId}")
    public List<String> getRoleUsers(@PathVariable String roleId) {
        return roleService.findUserIdsByRoleId(roleId);
    }
}
