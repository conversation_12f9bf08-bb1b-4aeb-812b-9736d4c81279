package com.ruijie.nse.mgr.sys.controller;

import com.ruijie.nse.mgr.sys.service.LogExportService;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/sys")
@RequiredArgsConstructor
public class SysController {

    private final LogExportService logExportService;

    /**
     * 帮助文档
     */
    @GetMapping("/help")
    public void getHelpPdf(HttpServletResponse response) throws IOException {
        Resource resource = new ClassPathResource("pdf/help.pdf");
        response.setHeader("content-disposition", "inline; filename=帮助文档.pdf");
        response.setContentType("application/octet-stream");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            IOUtils.copy(resource.getInputStream(), outputStream);
        }
    }

    /**
     * 导出软件日志
     */
    @GetMapping("/logs/export")
    public void exportLogs(HttpServletResponse response) throws IOException {
        logExportService.exportLogs(response);
    }
}
