package com.ruijie.nse.mgr.sys.dto.input;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruijie.nse.mgr.repository.entity.enums.UserType;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 用户保存输入
 */
@Data
public class UserInput {

    /**
     * 主键
     */
    private String id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 登录i账号
     */
    private String account;

    /**
     * 手机
     */
    private String mobilePhone;

    /**
     * 用户类型
     */
    private UserType userType;

    /**
     * 班级
     */
    private String classes;

    /**
     * 有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date validDate;

    /**
     * 主键列表
     */
    private List<String> ids;
}
