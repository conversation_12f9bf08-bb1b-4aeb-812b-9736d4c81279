package com.ruijie.nse.mgr.sys.dto.output;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoginOutput {

    /**
     * 访问令牌
     */
    private String accessToken;

    /**
     * 刷新令牌
     */
    private String refreshToken;

    /**
     * 令牌类型
     */
    private String tokenType = "Bearer";

    /**
     * 过期时间（秒）
     */
    private Long expiresIn;

    /**
     * 用户信息
     */
    private UserInfo userInfo;

    /**
     * 登录结果 正常登录：200 并发限制已满无法登录：201 进入排队：202
     */
    private int loginResult;

    /**
     * 登录错误信息
     */
    private String loginErrMsg;

    /**
     * 已登录的用户信息
     */
    private List<OnlineUser> onlineUsers;


    /**
     * 已在线的用户信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OnlineUser {

        /**
         * 用户姓名
         */
        private String name;

        /**
         * 手机号
         */
        private String mobilePhone;

    }


    /**
     * 用户信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserInfo {

        /**
         * 用户ID
         */
        private String userId;

        /**
         * 用户名
         */
        private String username;

        private String account;

    }
}
