package com.ruijie.nse.mgr.sys.dto.output;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 下拉选项对象
 */
@Data
@NoArgsConstructor
public class OptionOutput<T> {

    public OptionOutput(T value, String label) {
        this.value = value;
        this.label = label;
    }

    public OptionOutput(T value, String label, List<OptionOutput<T>> children) {
        this.value = value;
        this.label = label;
        this.children = children;
    }

    public OptionOutput(T value, String label, String tag) {
        this.value = value;
        this.label = label;
        this.tag = tag;
    }

    /**
     * 选项的值
     */
    private T value;

    /**
     * 选项的标签
     */
    private String label;

    /**
     * 标签类型
     */
    @JsonInclude(value = JsonInclude.Include.NON_EMPTY)
    private String tag;

    /**
     * 子选项列表
     */
    @JsonInclude(value = JsonInclude.Include.NON_EMPTY)
    private List<OptionOutput<T>> children;

}
