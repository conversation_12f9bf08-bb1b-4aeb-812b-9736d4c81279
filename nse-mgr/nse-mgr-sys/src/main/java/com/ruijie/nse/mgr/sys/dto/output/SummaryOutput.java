package com.ruijie.nse.mgr.sys.dto.output;

import lombok.Data;

/**
 * 首页账号资源概览
 */
@Data
public class SummaryOutput {

    /**
     * 授权管理员数
     */
    private Integer periMgrCnt = 0;

    /**
     * 授权用户数
     */
    private Integer periUserCnt = 0;


    /**
     * 已登录管理员数
     */
    private Integer loginMgrCnt = 0;

    /**
     * 已登录用户数
     */
    private Integer loginUserCnt = 0;


    /**
     * 剩余数量
     */
    private Integer overMgrCnt;
    private Integer overUserCnt;

    public Integer getOverMgrCnt() {
        return periMgrCnt - loginMgrCnt;
    }

    public Integer getOverUserCnt() {
        return periUserCnt - loginUserCnt;
    }
}
