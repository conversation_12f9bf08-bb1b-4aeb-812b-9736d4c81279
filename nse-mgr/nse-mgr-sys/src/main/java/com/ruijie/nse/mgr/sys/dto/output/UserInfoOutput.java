package com.ruijie.nse.mgr.sys.dto.output;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 用户信息
 */
@Data
@Accessors(chain = true)
public class UserInfoOutput {

    /**
     * 用户id
     */
    private String userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 姓名
     */
    private String nickName;

    /**
     * 头像
     */
    private String avatar;
    /**
     * 是否默认密码
     */
    private Boolean isDefaultPwd;

    /**
     * 权限
     */
    private List<String> perms;

    /**
     * 角色
     */
    private List<String> roles;

}
