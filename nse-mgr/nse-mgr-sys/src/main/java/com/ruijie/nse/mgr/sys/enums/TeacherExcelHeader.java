package com.ruijie.nse.mgr.sys.enums;

import lombok.Getter;

@Getter
public enum TeacherExcelHeader implements BaseExcelHeader {

    ACCOUNT("工号*", 20, "account"),
    NAME("姓名*", 20, "name"),
    MOBILE_PHONE("联系方式", 20, "mobilePhone");

    private final String alias;
    private final Integer width;
    private final String property;

    /**
     * @param alias    标题名称
     * @param width    列宽度
     * @param property 属性
     */
    TeacherExcelHeader(String alias, Integer width, String property) {
        this.alias = alias;
        this.width = width;
        this.property = property;
    }

    @Override
    public BaseExcelHeader[] getValues() {
        return values();
    }

}
