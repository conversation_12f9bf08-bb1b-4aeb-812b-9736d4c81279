package com.ruijie.nse.mgr.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruijie.nse.mgr.repository.entity.EventVerifyRecords;
import com.ruijie.nse.mgr.repository.mapper.EventVerifyRecordsDao;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 日志导出服务
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LogExportService {

    private final EventVerifyRecordsDao eventVerifyRecordsDao;

    /**
     * 导出软件日志
     *
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    public void exportLogs(HttpServletResponse response) throws IOException {
        String fileName = "软件日志_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + ".zip";
        String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
        
        response.setHeader("content-disposition", "attachment; filename*=UTF-8''" + encodedFileName);
        response.setContentType("application/zip");

        try (ServletOutputStream outputStream = response.getOutputStream();
             ZipOutputStream zipOut = new ZipOutputStream(outputStream)) {

            // 添加最近3天的日志文件
            addLogFilesToZip(zipOut);

            // 添加数据库evt_verify_records表数据
            addEventVerifyRecordsToZip(zipOut);

            zipOut.finish();
            log.info("软件日志导出完成: {}", fileName);
        } catch (Exception e) {
            log.error("导出软件日志失败", e);
            throw new IOException("导出软件日志失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加最近3天的日志文件到ZIP
     *
     * @param zipOut ZIP输出流
     * @throws IOException IO异常
     */
    private void addLogFilesToZip(ZipOutputStream zipOut) throws IOException {
        String logDir = "./logs";
        Path logPath = Paths.get(logDir);

        if (!Files.exists(logPath)) {
            log.warn("日志目录不存在: {}", logDir);
            return;
        }

        LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);
        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(logPath, "*.log*")) {
            for (Path logFile : stream) {
                try {
                    // 检查文件修改时间是否在最近3天内
                    LocalDateTime fileModifiedTime = LocalDateTime.ofInstant(
                            Files.getLastModifiedTime(logFile).toInstant(),
                            java.time.ZoneId.systemDefault()
                    );

                    if (fileModifiedTime.isAfter(threeDaysAgo)) {
                        addFileToZip(zipOut, logFile, "logs/" + logFile.getFileName().toString());
                        log.debug("添加日志文件到ZIP: {}", logFile.getFileName());
                    }
                } catch (Exception e) {
                    log.warn("处理日志文件失败: {}, 错误: {}", logFile.getFileName(), e.getMessage());
                }
            }
        }
    }

    /**
     * 添加evt_verify_records表数据到ZIP
     *
     * @param zipOut ZIP输出流
     * @throws IOException IO异常
     */
    private void addEventVerifyRecordsToZip(ZipOutputStream zipOut) throws IOException {
        try {
            // 查询最近3天的事件验证记录
            LocalDateTime threeDaysAgo = LocalDateTime.now().minusDays(3);
            LambdaQueryWrapper<EventVerifyRecords> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.ge(EventVerifyRecords::getCreatedDate, threeDaysAgo)
                    .orderByDesc(EventVerifyRecords::getCreatedDate);

            List<EventVerifyRecords> records = eventVerifyRecordsDao.selectList(queryWrapper);

            // 创建CSV内容
            StringBuilder csvContent = new StringBuilder();
            csvContent.append("ID,事件类型,事件详情,事件级别,事件消息,备注,创建时间,修改时间\n");

            for (EventVerifyRecords record : records) {
                csvContent.append(escapeCSV(record.getId())).append(",")
                        .append(escapeCSV(record.getEvt() != null ? record.getEvt().name() : "")).append(",")
                        .append(escapeCSV(record.getEvtDetails())).append(",")
                        .append(escapeCSV(record.getEvtLevel())).append(",")
                        .append(escapeCSV(record.getEvtMessage())).append(",")
                        .append(escapeCSV(record.getRemark())).append(",")
                        .append(escapeCSV(record.getCreatedDate() != null ? record.getCreatedDate().toString() : "")).append(",")
                        .append(escapeCSV(record.getModifiedDate() != null ? record.getModifiedDate().toString() : ""))
                        .append("\n");
            }

            // 添加CSV文件到ZIP
            ZipEntry csvEntry = new ZipEntry("evtdetails/evt_verify_records.csv");
            zipOut.putNextEntry(csvEntry);

            // 添加UTF-8 BOM头，确保Excel能正确识别中文
            byte[] bom = {(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};
            zipOut.write(bom);
            zipOut.write(csvContent.toString().getBytes(StandardCharsets.UTF_8));
            zipOut.closeEntry();

            log.info("添加事件验证记录到ZIP，共{}条记录", records.size());
        } catch (Exception e) {
            log.error("导出事件验证记录失败", e);
            throw new IOException("导出事件验证记录失败: " + e.getMessage(), e);
        }
    }

    /**
     * 添加文件到ZIP
     *
     * @param zipOut   ZIP输出流
     * @param filePath 文件路径
     * @param entryName ZIP条目名称
     * @throws IOException IO异常
     */
    private void addFileToZip(ZipOutputStream zipOut, Path filePath, String entryName) throws IOException {
        ZipEntry zipEntry = new ZipEntry(entryName);
        zipOut.putNextEntry(zipEntry);

        try (InputStream fileInputStream = Files.newInputStream(filePath)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                zipOut.write(buffer, 0, bytesRead);
            }
        }
        zipOut.closeEntry();
    }

    /**
     * 转义CSV字段
     *
     * @param field 字段值
     * @return 转义后的字段值
     */
    private String escapeCSV(String field) {
        if (field == null) {
            return "";
        }
        // 如果包含逗号、双引号或换行符，需要用双引号包围，并转义内部的双引号
        if (field.contains(",") || field.contains("\"") || field.contains("\n") || field.contains("\r")) {
            return "\"" + field.replace("\"", "\"\"") + "\"";
        }
        return field;
    }
}
