package com.ruijie.nse.mgr.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruijie.nse.common.constant.CommonConstant;
import com.ruijie.nse.common.exception.BusinessException;
import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.common.context.LicenseContext;
import com.ruijie.nse.mgr.repository.entity.Menu;
import com.ruijie.nse.mgr.repository.entity.enums.MenuTypeEnum;
import com.ruijie.nse.mgr.repository.mapper.MenuDao;
import com.ruijie.nse.mgr.sys.dto.input.MenuFormInput;
import com.ruijie.nse.mgr.sys.dto.output.MenuOutput;
import com.ruijie.nse.mgr.sys.dto.output.OptionOutput;
import com.ruijie.nse.mgr.sys.dto.output.RouteOutput;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.bean.BeanUtil;
import org.dromara.hutool.core.collection.CollUtil;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.dromara.hutool.core.text.StrValidator;
import org.dromara.hutool.json.JSONUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MenuService extends ServiceImpl<MenuDao, Menu> {


    @Autowired
    private UserService userService;

    /**
     * 获取用户路由
     * @return
     */
    public List<RouteOutput> routes() {
        List<Menu> menuList;
        // 校验license
        if(!LicenseContext.isValid()) {
            menuList = baseMapper.selectList(new LambdaQueryWrapper<Menu>()
                    .eq(Menu::getVerifyLicense, false)
                    .orderByAsc(Menu::getSort));
            return buildRoutes("0", menuList);
        }
        // license校验通过
        String userId = SecurityUtils.getUserId();
        List<String> roles = userService.listRolesByUserId(userId);
        if (CollUtil.isEmpty(roles)) {
            return Collections.emptyList();
        }
        // 超级管理员获取所有菜单
        if (roles.contains(CommonConstant.System.SUPER_ADMIN)) {
            menuList = baseMapper.selectList(new LambdaQueryWrapper<Menu>()
                    .ne(Menu::getType, MenuTypeEnum.BUTTON.getValue())
                    .orderByAsc(Menu::getSort)
            );
        } else {
            menuList = this.baseMapper.getMenusByRoles(roles);
        }
        return buildRoutes("0", menuList);
    }


    /**
     * 递归生成菜单路由层级列表
     *
     * @param parentId 父级ID
     * @param menuList 菜单列表
     * @return 路由层级列表
     */
    private List<RouteOutput> buildRoutes(String parentId, List<Menu> menuList) {
        List<RouteOutput> routeList = new ArrayList<>();

        for (Menu menu : menuList) {
            if (menu.getParentId().equals(parentId)) {
                RouteOutput routeVO = toRouteVo(menu);
                List<RouteOutput> children = buildRoutes(menu.getId(), menuList);
                if (!children.isEmpty()) {
                    routeVO.setChildren(children);
                }
                routeList.add(routeVO);
            }
        }

        return routeList;
    }

    /**
     * 根据RouteBO创建RouteVO
     */
    private RouteOutput toRouteVo(Menu menu) {
        RouteOutput routeVO = new RouteOutput();
        // 获取路由名称
        String routeName = menu.getRouteName();
        if (StrValidator.isBlank(routeName)) {
            // 路由 name 需要驼峰，首字母大写
            routeName = StringUtils.capitalize(CharSequenceUtil.toCamelCase(menu.getRoutePath(), '-'));
        }
        // 根据name路由跳转 this.$router.push({name:xxx})
        routeVO.setName(routeName);

        // 根据path路由跳转 this.$router.push({path:xxx})
        routeVO.setPath(menu.getRoutePath());
        routeVO.setRedirect(menu.getRedirect());
        routeVO.setComponent(menu.getComponent());

        RouteOutput.Meta meta = new RouteOutput.Meta();
        meta.setTitle(menu.getName());
        meta.setIcon(menu.getIcon());
        meta.setHidden(Objects.equals(menu.getVisible(), 0));
        // 【菜单】是否开启页面缓存
        if (MenuTypeEnum.MENU.getValue().equals(menu.getType())
                && Objects.equals(menu.getKeepAlive(), 1)) {
            meta.setKeepAlive(true);
        }
        meta.setAlwaysShow(Objects.equals(menu.getAlwaysShow(), 1));

        String paramsJson = menu.getParams();
        // 将 JSON 字符串转换为 Map<String, String>
        if (StrValidator.isNotBlank(paramsJson)) {
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                Map<String, String> paramMap = objectMapper.readValue(paramsJson, new TypeReference<>() {
                });
                meta.setParams(paramMap);
            } catch (Exception e) {
                log.error("toRouteVo 解析参数失败：", e);
                throw BusinessException.errorByMessage("解析参数失败");
            }
        }
        routeVO.setMeta(meta);
        return routeVO;
    }

    /**
     * 菜单列表
     *
     * @param keywords
     */
    public List<MenuOutput> listMenus(String keywords) {
        List<Menu> menus = this.list(Wrappers.lambdaQuery(Menu.class)
                .like(StrValidator.isNotBlank(keywords), Menu::getName, keywords)
                .orderByAsc(Menu::getSort)
        );
        // 获取所有菜单ID
        Set<String> menuIds = menus.stream()
                .map(Menu::getId)
                .collect(Collectors.toSet());

        // 获取所有父级ID
        Set<String> parentIds = menus.stream()
                .map(Menu::getParentId)
                .collect(Collectors.toSet());

        // 获取根节点ID（递归的起点），即父节点ID中不包含在部门ID中的节点，注意这里不能拿顶级菜单 O 作为根节点，因为菜单筛选的时候 O 会被过滤掉
        List<String> rootIds = parentIds.stream()
                .filter(id -> !menuIds.contains(id))
                .toList();

        // 使用递归函数来构建菜单树
        return rootIds.stream()
                .flatMap(rootId -> buildMenuTree(rootId, menus).stream())
                .toList();
    }

    /**
     * 递归生成菜单列表
     *
     * @param parentId 父级ID
     * @param menuList 菜单列表
     * @return 菜单列表
     */
    private List<MenuOutput> buildMenuTree(String parentId, List<Menu> menuList) {
        return CollUtil.emptyIfNull(menuList)
                .stream()
                .filter(menu -> menu.getParentId().equals(parentId))
                .map(entity -> {
                    MenuOutput menuOutput = BeanUtil.toBean(entity, MenuOutput.class);
                    List<MenuOutput> children = buildMenuTree(entity.getId(), menuList);
                    menuOutput.setChildren(children);
                    return menuOutput;
                }).toList();
    }

    /**
     * 菜单下拉数据
     *
     * @param onlyParent 是否只查询父级菜单 如果为true，排除按钮
     */
    public List<OptionOutput<String>> listMenuOptions(boolean onlyParent) {
        List<Menu> menuList = this.list(new LambdaQueryWrapper<Menu>()
                .in(onlyParent, Menu::getType, MenuTypeEnum.CATALOG.getValue(), MenuTypeEnum.MENU.getValue())
                .orderByAsc(Menu::getSort)
        );
        return buildMenuOptions(CommonConstant.System.ROOT_MENU_ID, menuList);
    }

    /**
     * 递归生成菜单下拉层级列表
     *
     * @param parentId 父级ID
     * @param menuList 菜单列表
     * @return 菜单下拉列表
     */
    private List<OptionOutput<String>> buildMenuOptions(String parentId, List<Menu> menuList) {
        List<OptionOutput<String>> menuOptions = new ArrayList<>();

        for (Menu menu : menuList) {
            if (menu.getParentId().equals(parentId)) {
                OptionOutput<String> option = new OptionOutput<>(menu.getId(), menu.getName());
                List<OptionOutput<String>> subMenuOptions = buildMenuOptions(menu.getId(), menuList);
                if (!subMenuOptions.isEmpty()) {
                    option.setChildren(subMenuOptions);
                }
                menuOptions.add(option);
            }
        }

        return menuOptions;
    }

    /**
     * 新增/修改菜单
     */
    public boolean saveMenu(MenuFormInput menuFormInput) {

        Integer menuType = menuFormInput.getType();

        if (MenuTypeEnum.CATALOG.getValue().equals(menuType)) {  // 如果是目录
            String path = menuFormInput.getRoutePath();
            if (CommonConstant.System.ROOT_MENU_ID.equals(menuFormInput.getParentId()) && !path.startsWith("/")) {
                menuFormInput.setRoutePath("/" + path); // 一级目录需以 / 开头
            }
            menuFormInput.setComponent("Layout");
        } else if (MenuTypeEnum.EXTLINK.getValue().equals(menuType)) {
            // 外链菜单组件设置为 null
            menuFormInput.setComponent(null);
        }
        if (Objects.equals(menuFormInput.getParentId(), menuFormInput.getId())) {
            throw BusinessException.errorByMessage("父级菜单不能为当前菜单");
        }
        Menu entity = BeanUtil.toBean(menuFormInput, Menu.class);
        String treePath = generateMenuTreePath(menuFormInput.getParentId());
        entity.setTreePath(treePath);

        List<MenuFormInput.KeyValue> params = menuFormInput.getParams();
        // 路由参数 [{key:"id",value:"1"}，{key:"name",value:"张三"}] 转换为 [{"id":"1"},{"name":"张三"}]
        if (CollUtil.isNotEmpty(params)) {
            entity.setParams(JSONUtil.toJsonStr(params.stream()
                    .collect(Collectors.toMap(MenuFormInput.KeyValue::getKey, MenuFormInput.KeyValue::getValue))));
        } else {
            entity.setParams(null);
        }
        // 新增类型为菜单时候 路由名称唯一
        if (MenuTypeEnum.MENU.getValue().equals(menuType)) {
            boolean exists = this.exists(new LambdaQueryWrapper<Menu>()
                    .eq(Menu::getRouteName, entity.getRouteName())
                    .ne(menuFormInput.getId() != null, Menu::getId, menuFormInput.getId())
            );
            if (exists) {
                throw BusinessException.error(StrValidator.EMPTY, "路由名称已存在");
            }
        } else {
            // 其他类型时 给路由名称赋值为空
            entity.setRouteName(null);
        }

        boolean result = this.saveOrUpdate(entity);
        // 修改菜单如果有子菜单，则更新子菜单的树路径
        updateChildrenTreePath(entity.getId(), treePath);
        return result;
    }

    /**
     * 更新子菜单树路径
     *
     * @param id       当前菜单ID
     * @param treePath 当前菜单树路径
     */
    private void updateChildrenTreePath(String id, String treePath) {
        List<Menu> children = this.list(new LambdaQueryWrapper<Menu>().eq(Menu::getParentId, id));
        if (CollUtil.isNotEmpty(children)) {
            // 子菜单的树路径等于父菜单的树路径加上父菜单ID
            String childTreePath = treePath + "," + id;
            this.update(new LambdaUpdateWrapper<Menu>()
                    .eq(Menu::getParentId, id)
                    .set(Menu::getTreePath, childTreePath)
            );
            for (Menu child : children) {
                // 递归更新子菜单
                updateChildrenTreePath(child.getId(), childTreePath);
            }
        }
    }

    /**
     * 部门路径生成
     *
     * @param parentId 父ID
     * @return 父节点路径以英文逗号(, )分割，eg: 1,2,3
     */
    private String generateMenuTreePath(String parentId) {
        if (CommonConstant.System.ROOT_MENU_ID.equals(parentId)) {
            return parentId;
        } else {
            Menu parent = this.getById(parentId);
            return parent != null ? parent.getTreePath() + "," + parent.getId() : null;
        }
    }


    /**
     * 修改菜单显示状态
     *
     * @param menuId  菜单ID
     * @param visible 是否显示(1->显示；2->隐藏)
     * @return 是否修改成功
     */
    @CacheEvict(cacheNames = "menu", key = "'routes'")
    public boolean updateMenuVisible(String menuId, Integer visible) {
        return this.update(new LambdaUpdateWrapper<Menu>()
                .eq(Menu::getId, menuId)
                .set(Menu::getVisible, visible)
        );
    }

    /**
     * 获取菜单表单数据
     *
     * @param id 菜单ID
     * @return 菜单表单数据
     */
    public MenuFormInput getMenuForm(String id) {
        Menu entity = this.getById(id);
        if (entity == null) {
            throw BusinessException.error(StrValidator.EMPTY, "菜单不存在");
        }
        MenuFormInput formData = BeanUtil.toBean(entity, MenuFormInput.class);
        // 路由参数字符串 {"id":"1","name":"张三"} 转换为 [{key:"id", value:"1"}, {key:"name", value:"张三"}]
        String params = entity.getParams();
        if (StrValidator.isNotBlank(params)) {
            ObjectMapper objectMapper = new ObjectMapper();
            try {
                // 解析 JSON 字符串为 Map<String, String>
                Map<String, String> paramMap = objectMapper.readValue(params, new TypeReference<>() {
                });

                // 转换为 List<KeyValue> 格式 [{key:"id", value:"1"}, {key:"name", value:"张三"}]
                List<MenuFormInput.KeyValue> transformedList = paramMap.entrySet().stream()
                        .map(entry -> new MenuFormInput.KeyValue(entry.getKey(), entry.getValue()))
                        .toList();

                // 将转换后的列表存入 MenuForm
                formData.setParams(transformedList);
            } catch (Exception e) {
                log.error("解析参数失败", e);
                throw BusinessException.errorByMessage("解析菜单参数失败");
            }
        }

        return formData;
    }

    /**
     * 删除菜单
     *
     * @param id 菜单ID
     * @return 是否删除成功
     */
    public boolean deleteMenu(String id) {
        return this.remove(new LambdaQueryWrapper<Menu>()
                .eq(Menu::getId, id)
                .or()
                .apply("CONCAT (',',tree_path,',') LIKE CONCAT('%,','" + id + "',',%')"));
    }

}
