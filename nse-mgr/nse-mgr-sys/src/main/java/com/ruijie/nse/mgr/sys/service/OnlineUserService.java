package com.ruijie.nse.mgr.sys.service;

import com.ruijie.nse.common.constant.CacheConstants;
import com.ruijie.nse.common.service.cache.EhcacheService;
import com.ruijie.nse.common.utils.enctry.JwtUtil;
import com.ruijie.nse.mgr.repository.entity.User;
import com.ruijie.nse.mgr.repository.mapper.UserDao;
import com.ruijie.nse.mgr.sys.dto.output.OnlineUserOutput;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.text.StrUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 在线用户管理服务
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OnlineUserService {

    private final EhcacheService ehcacheService;
    private final UserDao userDao;

    /**
     * 获取当前所有在线用户列表
     * 
     * @return 在线用户列表
     */
    public List<OnlineUserOutput> getOnlineUsers() {
        List<OnlineUserOutput> onlineUsers = new ArrayList<>();
        
        try {
            // 获取JWT缓存中的所有Token
            List<String> allTokens = ehcacheService.getAllValues(CacheConstants.JWT_TOKEN_CACHE_NO_PERSISTENCE, String.class);
            
            log.debug("从缓存中获取到 {} 个Token", allTokens.size());
            
            // 遍历所有Token，提取用户信息
            for (String token : allTokens) {
                try {
                    OnlineUserOutput onlineUser = extractUserFromToken(token);
                    if (onlineUser != null) {
                        onlineUsers.add(onlineUser);
                    }
                } catch (Exception e) {
                    log.warn("解析Token失败: {}", e.getMessage());
                }
            }
            
            // 按登录时间倒序排列
            onlineUsers.sort((u1, u2) -> u2.getLoginTime().compareTo(u1.getLoginTime()));
            
            log.info("当前在线用户数量: {}", onlineUsers.size());
            
        } catch (Exception e) {
            log.error("获取在线用户列表失败", e);
        }
        
        return onlineUsers;
    }

    /**
     * 获取在线用户数量
     * 
     * @return 在线用户数量
     */
    public int getOnlineUserCount() {
        try {
            List<String> allTokens = ehcacheService.getAllValues(CacheConstants.JWT_TOKEN_CACHE_NO_PERSISTENCE, String.class);
            
            // 过滤有效的Token
            long validTokenCount = allTokens.stream()
                    .filter(JwtUtil::validateToken)
                    .count();
            
            return (int) validTokenCount;
        } catch (Exception e) {
            log.error("获取在线用户数量失败", e);
            return 0;
        }
    }

    /**
     * 根据用户ID强制下线用户
     * 
     * @param userId 用户ID
     * @return 是否成功
     */
    public boolean forceLogout(String userId) {
        try {
            if (StrUtil.isBlank(userId)) {
                return false;
            }
            
            // 从缓存中移除用户的Token
            ehcacheService.evict(CacheConstants.JWT_TOKEN_CACHE_NO_PERSISTENCE, userId);
            
            log.info("用户 {} 已被强制下线", userId);
            return true;
            
        } catch (Exception e) {
            log.error("强制下线用户失败, userId: {}", userId, e);
            return false;
        }
    }

    /**
     * 批量强制下线用户
     * 
     * @param userIds 用户ID列表
     * @return 成功下线的用户数量
     */
    public int batchForceLogout(List<String> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return 0;
        }
        
        int successCount = 0;
        for (String userId : userIds) {
            if (forceLogout(userId)) {
                successCount++;
            }
        }
        
        log.info("批量强制下线完成，成功: {}, 总数: {}", successCount, userIds.size());
        return successCount;
    }

    /**
     * 检查指定用户是否在线
     * 
     * @param userId 用户ID
     * @return 是否在线
     */
    public boolean isUserOnline(String userId) {
        try {
            if (StrUtil.isBlank(userId)) {
                return false;
            }
            
            String token = ehcacheService.get(CacheConstants.JWT_TOKEN_CACHE_NO_PERSISTENCE, userId, String.class);
            return StrUtil.isNotBlank(token) && JwtUtil.validateToken(token);
            
        } catch (Exception e) {
            log.error("检查用户在线状态失败, userId: {}", userId, e);
            return false;
        }
    }

    /**
     * 获取指定用户的在线信息
     * 
     * @param userId 用户ID
     * @return 在线用户信息，如果用户不在线则返回null
     */
    public OnlineUserOutput getOnlineUserInfo(String userId) {
        try {
            if (StrUtil.isBlank(userId)) {
                return null;
            }
            
            String token = ehcacheService.get(CacheConstants.JWT_TOKEN_CACHE_NO_PERSISTENCE, userId, String.class);
            if (StrUtil.isBlank(token) || !JwtUtil.validateToken(token)) {
                return null;
            }
            
            return extractUserFromToken(token);
            
        } catch (Exception e) {
            log.error("获取用户在线信息失败, userId: {}", userId, e);
            return null;
        }
    }

    /**
     * 清理所有过期的Token
     * 
     * @return 清理的Token数量
     */
    public int cleanExpiredTokens() {
        try {
            List<String> allTokens = ehcacheService.getAllValues(CacheConstants.JWT_TOKEN_CACHE_NO_PERSISTENCE, String.class);
            int expiredCount = 0;
            
            for (String token : allTokens) {
                if (!JwtUtil.validateToken(token)) {
                    String userId = JwtUtil.getUserId(token);
                    if (StrUtil.isNotBlank(userId)) {
                        ehcacheService.evict(CacheConstants.JWT_TOKEN_CACHE_NO_PERSISTENCE, userId);
                        expiredCount++;
                    }
                }
            }
            
            log.info("清理过期Token完成，清理数量: {}", expiredCount);
            return expiredCount;
            
        } catch (Exception e) {
            log.error("清理过期Token失败", e);
            return 0;
        }
    }

    /**
     * 从Token中提取用户信息
     * 
     * @param token JWT Token
     * @return 在线用户信息
     */
    private OnlineUserOutput extractUserFromToken(String token) {
        try {
            if (StrUtil.isBlank(token) || !JwtUtil.validateToken(token)) {
                return null;
            }
            
            String userId = JwtUtil.getUserId(token);
            String username = JwtUtil.getUsername(token);
            String authorities = JwtUtil.getAuthorities(token);
            
            if (StrUtil.isBlank(userId) || StrUtil.isBlank(username)) {
                return null;
            }
            
            // 获取用户详细信息
            User user = userDao.selectById(userId);
            if (user == null) {
                log.warn("用户不存在: {}", userId);
                return null;
            }
            
            // 获取Token的签发时间作为登录时间
            Date loginTime = JwtUtil.getIssuedAt(token);
            Date expireTime = JwtUtil.getExpiration(token);
            
            return OnlineUserOutput.builder()
                    .userId(userId)
                    .username(username)
                    .name(user.getName())
                    .mobilePhone(user.getMobilePhone())
                    .userType(user.getUserType())
                    .userTypeText(user.getUserType() != null ? user.getUserType().getDescription() : "")
                    .authorities(authorities)
                    .loginTime(loginTime != null ? loginTime : new Date())
                    .expireTime(expireTime != null ? expireTime : new Date())
                    .ipAddress(getLastLoginIp(userId))
                    .status("在线")
                    .build();
            
        } catch (Exception e) {
            log.error("从Token提取用户信息失败", e);
            return null;
        }
    }

    /**
     * 获取用户最后登录IP（这里简化处理，实际可以从日志或其他地方获取）
     * 
     * @param userId 用户ID
     * @return IP地址
     */
    private String getLastLoginIp(String userId) {
        // TODO: 实际实现中可以从登录日志表或其他地方获取用户的登录IP
        // 这里暂时返回默认值
        return "127.0.0.1";
    }
}
