package com.ruijie.nse.mgr.sys.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruijie.nse.mgr.repository.entity.Role;
import com.ruijie.nse.mgr.repository.mapper.RoleDao;
import org.dromara.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * $!{table.comment} 服务类
 * </p>
 *
 * <AUTHOR>
 * @date 2018-08-14
 */
@Service
public class RoleService extends ServiceImpl<RoleDao, Role> {


    /**
     * 根据角色id获取菜单id
     *
     * @param roleId
     * @return
     */
    public List<String> findMenuIdsByRoleId(String roleId) {
        return baseMapper.listMenusByRoleId(roleId);
    }

    /**
     * 根据角色id获取用户id
     *
     * @param roleId
     * @return
     */
    public List<String> findUserIdsByRoleId(String roleId) {
        return baseMapper.findUserListByRoleId(roleId);
    }

    /**
     * 给角色分配菜单
     *
     * @param roleId
     * @param menuIds
     */
    @Transactional
    public void assignMenu(String roleId, List<String> menuIds) {
        // 清除现有的
        baseMapper.deleteMenusOfRole(roleId);
        if (CollUtil.isNotEmpty(menuIds)) {
            baseMapper.insertMenusToRole(menuIds, roleId);
        }
    }
}

