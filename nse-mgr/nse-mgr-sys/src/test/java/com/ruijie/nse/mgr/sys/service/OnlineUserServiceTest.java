//package com.ruijie.nse.mgr.sys.service;
//
//import com.ruijie.nse.common.constant.CacheConstants;
//import com.ruijie.nse.common.service.cache.EhcacheService;
//import com.ruijie.nse.common.utils.enctry.JwtUtil;
//import com.ruijie.nse.mgr.repository.entity.User;
//import com.ruijie.nse.mgr.repository.entity.enums.UserType;
//import com.ruijie.nse.mgr.sys.dto.output.OnlineUserOutput;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.util.Arrays;
//import java.util.List;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.ArgumentMatchers.*;
//import static org.mockito.Mockito.*;
//
///**
// * 在线用户服务测试
// *
// * <AUTHOR>
// * @since 2025-07-25
// */
//@ExtendWith(MockitoExtension.class)
//class OnlineUserServiceTest {
//
//    @Mock
//    private EhcacheService ehcacheService;
//
//    @Mock
//    private UserService userService;
//
//    @InjectMocks
//    private OnlineUserService onlineUserService;
//
//    private String testToken;
//    private User testUser;
//
//    @BeforeEach
//    void setUp() {
//        // 创建测试用户
//        testUser = new User();
//        testUser.setId("test-user-id");
//        testUser.setAccount("testuser");
//        testUser.setName("测试用户");
//        testUser.setUserType(UserType.TEACHER);
//
//        // 生成测试Token
//        testToken = JwtUtil.generateAccessToken("test-user-id", "testuser", "ROLE_TEACHER");
//    }
//
//    @Test
//    void testGetOnlineUsers() {
//        // 模拟缓存返回Token列表
//        when(ehcacheService.getAllValues(eq(CacheConstants.JWT_TOKEN_CACHE), eq(String.class)))
//                .thenReturn(Arrays.asList(testToken));
//
//        // 模拟用户服务返回用户信息
//        when(userService.getById("test-user-id")).thenReturn(testUser);
//
//        // 执行测试
//        List<OnlineUserOutput> onlineUsers = onlineUserService.getOnlineUsers();
//
//        // 验证结果
//        assertNotNull(onlineUsers);
//        assertEquals(1, onlineUsers.size());
//
//        OnlineUserOutput onlineUser = onlineUsers.get(0);
//        assertEquals("test-user-id", onlineUser.getUserId());
//        assertEquals("testuser", onlineUser.getUsername());
//        assertEquals("测试用户", onlineUser.getName());
//        assertEquals(UserType.TEACHER, onlineUser.getUserType());
//        assertEquals("在线", onlineUser.getStatus());
//
//        // 验证方法调用
//        verify(ehcacheService).getAllValues(CacheConstants.JWT_TOKEN_CACHE, String.class);
//        verify(userService).getById("test-user-id");
//    }
//
//    @Test
//    void testGetOnlineUserCount() {
//        // 模拟缓存返回Token列表
//        when(ehcacheService.getAllValues(eq(CacheConstants.JWT_TOKEN_CACHE), eq(String.class)))
//                .thenReturn(Arrays.asList(testToken, testToken));
//
//        // 执行测试
//        int count = onlineUserService.getOnlineUserCount();
//
//        // 验证结果
//        assertEquals(2, count);
//
//        // 验证方法调用
//        verify(ehcacheService).getAllValues(CacheConstants.JWT_TOKEN_CACHE, String.class);
//    }
//
//    @Test
//    void testIsUserOnline() {
//        // 模拟缓存返回有效Token
//        when(ehcacheService.get(eq(CacheConstants.JWT_TOKEN_CACHE), eq("test-user-id"), eq(String.class)))
//                .thenReturn(testToken);
//
//        // 执行测试
//        boolean isOnline = onlineUserService.isUserOnline("test-user-id");
//
//        // 验证结果
//        assertTrue(isOnline);
//
//        // 验证方法调用
//        verify(ehcacheService).get(CacheConstants.JWT_TOKEN_CACHE, "test-user-id", String.class);
//    }
//
//    @Test
//    void testIsUserOnline_UserNotOnline() {
//        // 模拟缓存返回null
//        when(ehcacheService.get(eq(CacheConstants.JWT_TOKEN_CACHE), eq("test-user-id"), eq(String.class)))
//                .thenReturn(null);
//
//        // 执行测试
//        boolean isOnline = onlineUserService.isUserOnline("test-user-id");
//
//        // 验证结果
//        assertFalse(isOnline);
//
//        // 验证方法调用
//        verify(ehcacheService).get(CacheConstants.JWT_TOKEN_CACHE, "test-user-id", String.class);
//    }
//
//    @Test
//    void testForceLogout() {
//        // 执行测试
//        boolean result = onlineUserService.forceLogout("test-user-id");
//
//        // 验证结果
//        assertTrue(result);
//
//        // 验证方法调用
//        verify(ehcacheService).evict(CacheConstants.JWT_TOKEN_CACHE, "test-user-id");
//    }
//
//    @Test
//    void testForceLogout_EmptyUserId() {
//        // 执行测试
//        boolean result = onlineUserService.forceLogout("");
//
//        // 验证结果
//        assertFalse(result);
//
//        // 验证没有调用缓存删除方法
//        verify(ehcacheService, never()).evict(anyString(), anyString());
//    }
//
//    @Test
//    void testBatchForceLogout() {
//        List<String> userIds = Arrays.asList("user1", "user2", "user3");
//
//        // 执行测试
//        int successCount = onlineUserService.batchForceLogout(userIds);
//
//        // 验证结果
//        assertEquals(3, successCount);
//
//        // 验证方法调用次数
//        verify(ehcacheService, times(3)).evict(eq(CacheConstants.JWT_TOKEN_CACHE), anyString());
//    }
//
//    @Test
//    void testGetOnlineUserInfo() {
//        // 模拟缓存返回有效Token
//        when(ehcacheService.get(eq(CacheConstants.JWT_TOKEN_CACHE), eq("test-user-id"), eq(String.class)))
//                .thenReturn(testToken);
//
//        // 模拟用户服务返回用户信息
//        when(userService.getById("test-user-id")).thenReturn(testUser);
//
//        // 执行测试
//        OnlineUserOutput userInfo = onlineUserService.getOnlineUserInfo("test-user-id");
//
//        // 验证结果
//        assertNotNull(userInfo);
//        assertEquals("test-user-id", userInfo.getUserId());
//        assertEquals("testuser", userInfo.getUsername());
//        assertEquals("测试用户", userInfo.getName());
//
//        // 验证方法调用
//        verify(ehcacheService).get(CacheConstants.JWT_TOKEN_CACHE, "test-user-id", String.class);
//        verify(userService).getById("test-user-id");
//    }
//
//    @Test
//    void testGetOnlineUserInfo_UserNotOnline() {
//        // 模拟缓存返回null
//        when(ehcacheService.get(eq(CacheConstants.JWT_TOKEN_CACHE), eq("test-user-id"), eq(String.class)))
//                .thenReturn(null);
//
//        // 执行测试
//        OnlineUserOutput userInfo = onlineUserService.getOnlineUserInfo("test-user-id");
//
//        // 验证结果
//        assertNull(userInfo);
//
//        // 验证方法调用
//        verify(ehcacheService).get(CacheConstants.JWT_TOKEN_CACHE, "test-user-id", String.class);
//        verify(userService, never()).getById(anyString());
//    }
//
//    @Test
//    void testCleanExpiredTokens() {
//        // 创建一个过期的Token（这里简化处理，实际中需要创建真正过期的Token）
//        String expiredToken = "expired.token.here";
//
//        // 模拟缓存返回Token列表（包含有效和无效Token）
//        when(ehcacheService.getAllValues(eq(CacheConstants.JWT_TOKEN_CACHE), eq(String.class)))
//                .thenReturn(Arrays.asList(testToken, expiredToken));
//
//        // 执行测试
//        int cleanedCount = onlineUserService.cleanExpiredTokens();
//
//        // 验证结果（由于expiredToken格式不正确，会被认为是无效Token）
//        assertTrue(cleanedCount >= 0);
//
//        // 验证方法调用
//        verify(ehcacheService).getAllValues(CacheConstants.JWT_TOKEN_CACHE, String.class);
//    }
//}
