#Configure here general information about the environment, such as SonarQube server connection details for example
#No information about specific project should appear here

#----- Default SonarQube server
#sonar.host.url=http://localhost:9000

#----- Default source code encoding
#sonar.sourceEncoding=UTF-8

sonar.host.url=http://*************:9600/
sonar.login=sqa_d0ecae3eccd0ff79e7aa06dd0a49e5808e8d10bd
sonar.sourceEncoding=UTF-8
sonar.java.binaries=.
sonar.java.libraries=D:/Rj_maven_repository/org/projectlombok/lombok/**/*.jar
sonar.exclusions=**/*.html, **/*.css, **/*.vue, **/*.js, **/*.py, **/*.dll, **/*.iml, **/*.txt, **/*.log, **/*.md, **/*.json, **/*.xml, **/*.yml, **/*.yaml, **/*.properties, **/target/**, **/workspace/**, **/.*, **/*Test.java
sonar.projectKey=nse
sonar.projectName=\u7f51\u7edc\u4eff\u771f\u73af\u5883